{"id": "f5c34445-7905-4b64-9121-7bd7d3f94ef1", "revision": 0, "last_node_id": 66, "last_link_id": 137, "nodes": [{"id": 41, "type": "DualCLIPLoader", "pos": [203.9858856201172, 389.494384765625], "size": [310, 130], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [75, 76]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.23", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp8_e4m3fn_scaled.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 39, "type": "VAELoader", "pos": [202.6847381591797, 593.5802612304688], "size": [311.81634521484375, 60.429901123046875], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [78, 88]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.23", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 66, "type": "LoraLoaderModelOnly", "pos": [629.7550048828125, 272.53814697265625], "size": [315, 82], "flags": {}, "order": 6, "mode": 4, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 136}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [137]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": [null, 1]}, {"id": 21, "type": "CLIPTextEncode", "pos": [628.7538452148438, 745.9877319335938], "size": [330, 88], "flags": {"collapsed": true}, "order": 4, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 75}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [90]}], "title": "CLIP Text Encode (Unused)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.23", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 45, "type": "Flex2Conditioner", "pos": [993.2865600585938, 401.2900085449219], "size": [315, 294], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "link": 137}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 88}, {"label": "positive", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 89}, {"label": "negative", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 90}, {"label": "latent", "localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 135}, {"label": "inpaint_image", "localized_name": "inpaint_image", "name": "inpaint_image", "shape": 7, "type": "IMAGE"}, {"label": "inpaint_mask", "localized_name": "inpaint_mask", "name": "inpaint_mask", "shape": 7, "type": "MASK"}, {"label": "control_image", "localized_name": "control_image", "name": "control_image", "shape": 7, "type": "IMAGE"}, {"localized_name": "bypass_guidance_embedder", "name": "bypass_guidance_embedder", "type": "COMBO", "widget": {"name": "bypass_guidance_embedder"}, "link": null}, {"localized_name": "guidance", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}, {"localized_name": "control_strength", "name": "control_strength", "type": "FLOAT", "widget": {"name": "control_strength"}, "link": null}, {"localized_name": "control_start_percent", "name": "control_start_percent", "type": "FLOAT", "widget": {"name": "control_start_percent"}, "link": null}, {"localized_name": "control_end_percent", "name": "control_end_percent", "type": "FLOAT", "widget": {"name": "control_end_percent"}, "link": null}], "outputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "links": [92]}, {"label": "positive", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "links": [93]}, {"label": "negative", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": [94]}, {"label": "latent", "localized_name": "latent", "name": "latent", "type": "LATENT", "links": [95]}], "properties": {"aux_id": "ostris/ComfyUI-FlexTools", "ver": "7aa734a482743c198d97b932b0c5918b14ad28de", "Node name for S&R": "Flex2Conditioner"}, "widgets_values": ["no", 3.5, 0.5000000000000001, 0, 0.6000000000000001]}, {"id": 20, "type": "K<PERSON><PERSON><PERSON>", "pos": [1346.59814453125, 395.89630126953125], "size": [315, 474], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 92}, {"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 93}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 94}, {"label": "latent_image", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 95}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [38]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.23", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [620735870427390, "randomize", 25, 1, "deis", "beta", 1]}, {"id": 16, "type": "CLIPTextEncode", "pos": [615.0457153320312, 410.36474609375], "size": [343.70819091796875, 283.6534729003906], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 76}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [89]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.18", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["V8-powered motorcycles embody an industrial-meets-futuristic aesthetic dominated by their massive engine blocks, where the 90-degree V8 configuration becomes the visual anchor with cylinder banks splayed like mechanical wings. These machines favor exposed engineering: polished aluminum pushrod tubes, labyrinthine exhaust headers snaking around chrome-moly frames, and gear trains coated in neon-green thermal paint create a \"mechanical skeleton\" effect. Carbon fiber panels with matte or forged-carbon textures wrap partially around engines like the 4.4L Maserati unit in the Larazeth LM 847, contrasting with mirror-polished titanium fasteners and oil-cooled copper piping. Low-slung profiles emphasize brute force, with engines often mounted as stressed members between CNC-machined aluminum subframes, while wide 240mm rear tires (e.g., Boss Hoss models) amplify muscular proportions. Lighting leans into angular futurism – triple-stack vertical LED headlights on the PGM V8 cast prismatic beams through laser-etched polycarbonate lenses, complemented by under-chassis neon strips that bathe exposed crankshafts in cyan or magenta glow. Color schemes oscillate between industrial austerity (Aurora Hellfire OZ26’s gunmetal gray with blackened exhausts) and flamboyant automotive-inspired finishes, like the Great Wall SOULO’s two-tone burgundy-and-silver livery mimicking classic GT cars. Retro-futurist touches emerge through circular digital gauges embedded in Art Deco-inspired nacelles or leather saddle seats stitched with aerospace-grade nylon thread. Thermal management dictates visual drama: the F355 Birger Hansen prototype flaunts twin centrifugal fans with turbine-like carbon fiber blades flanking its Ferrari-derived V8, while the Curtiss V8 integrates radiator veins into flowing aluminum side panels resembling Art Nouveau motifs. Wheels become kinetic sculptures – the Larazeth LM 847’s hollow-spoke magnesium alloys house holographic speed projections, while PGM’s carbon fiber rims expose glowing brake calipers through laser-cut patterns. Every surface serves dual purposes: the Hellfire OZ26’s fuel tank doubles as an oil reservoir with sight glass windows revealing amber lubricant cascading through internal channels, and exhaust heat shields on Boss Hoss cruisers morph into airbrushed canvas for flame motifs. Even ergonomics showcase engineering theater – footpegs mount directly to engine cases on the PGM V8, and handlebars cantilever over cam covers on the Curtiss prototype. Despite their diversity, all V8 motorcycles share a deliberate tension between automotive-scale power and motorcycle minimalism, epitomized by the Great Wall SOULO’s paradox of a 2000cc horizontally opposed V8 squeezed into a chassis barely wider than a conventional superbike, its polished engine fins glowing like radioactive armor under LED underlighting. These machines transform mechanical components into aesthetic statements, where every bolt flange and oil passage becomes a deliberate brushstroke in a portrait of controlled chaos."], "color": "#232", "bgcolor": "#353"}, {"id": 65, "type": "SDXLEmptyLatentSizePicker+", "pos": [191.73895263671875, 742.5042114257812], "size": [307.0261535644531, 174.97613525390625], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "resolution", "name": "resolution", "type": "COMBO", "widget": {"name": "resolution"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}, {"localized_name": "width_override", "name": "width_override", "type": "INT", "widget": {"name": "width_override"}, "link": null}, {"localized_name": "height_override", "name": "height_override", "type": "INT", "widget": {"name": "height_override"}, "link": null}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [135]}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT"}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT"}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "SDXLEmptyLatentSizePicker+"}, "widgets_values": ["768x1280 (0.6)", 1, 0, 0]}, {"id": 40, "type": "UNETLoader", "pos": [210.00694274902344, 251.82647705078125], "size": [310, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [136]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.23", "Node name for S&R": "UNETLoader"}, "widgets_values": ["Flex.2-preview.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 11, "type": "VAEDecode", "pos": [1443.952880859375, 293.5309753417969], "size": [210, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "samples", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 38}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 78}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [134]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.18", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 64, "type": "SaveImage", "pos": [1694.718017578125, 339.06201171875], "size": [635.5423583984375, 589.2984008789062], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "images", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 134}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}], "links": [[38, 20, 0, 11, 0, "LATENT"], [75, 41, 0, 21, 0, "CLIP"], [76, 41, 0, 16, 0, "CLIP"], [78, 39, 0, 11, 1, "VAE"], [88, 39, 0, 45, 1, "VAE"], [89, 16, 0, 45, 2, "CONDITIONING"], [90, 21, 0, 45, 3, "CONDITIONING"], [92, 45, 0, 20, 0, "MODEL"], [93, 45, 1, 20, 1, "CONDITIONING"], [94, 45, 2, 20, 2, "CONDITIONING"], [95, 45, 3, 20, 3, "LATENT"], [134, 11, 0, 64, 0, "IMAGE"], [135, 65, 0, 45, 4, "LATENT"], [136, 40, 0, 66, 0, "MODEL"], [137, 66, 0, 45, 0, "MODEL"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.7247295000000104, "offset": [114.56463365268932, -51.368140730731305]}, "frontendVersion": "1.18.9", "VHS_KeepIntermediate": true, "ue_links": [], "VHS_MetadataImage": true, "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false}, "version": 0.4}