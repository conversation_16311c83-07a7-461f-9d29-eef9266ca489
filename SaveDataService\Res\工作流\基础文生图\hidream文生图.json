{"id": "01d66ae9-78be-4a8d-b737-24eee5e1d447", "revision": 0, "last_node_id": 331, "last_link_id": 611, "nodes": [{"id": 136, "type": "GetNode", "pos": [610, 1970], "size": [210, 60], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": []}], "title": "Get_width", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["width"], "color": "#14538e", "bgcolor": "#003f7a"}, {"id": 189, "type": "GetNode", "pos": [590, 1880], "size": [210, 60], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": []}], "title": "Get_latent_2", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["latent_2"], "color": "#14538e", "bgcolor": "#003f7a"}, {"id": 190, "type": "SetNode", "pos": [600, 1850], "size": [210, 60], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [{"name": "LATENT", "type": "LATENT", "link": 313}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_latent_3", "properties": {"previousName": "latent_3", "widget_ue_connectable": {}}, "widgets_values": ["latent_3"], "color": "#323", "bgcolor": "#535"}, {"id": 275, "type": "LoadImage", "pos": [-3173.68408203125, 2672.************], "size": [340, 330], "flags": {}, "order": 2, "mode": 4, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [479]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["250501-2235_HiResFix_00001_.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 274, "type": "ShowText|pysssss", "pos": [-2013.6839599609375, 2672.************], "size": [450, 430], "flags": {}, "order": 28, "mode": 4, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 605}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": []}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.4", "Node name for S&R": "ShowText|pysssss", "widget_ue_connectable": {}}, "widgets_values": [", A sleek, futuristic spacecraft gracefully navigates a vibrant nebula, detailed hull reflecting the swirling cosmic dust and light, dramatic perspective from below, showcasing the ship's powerful engines emitting trails of blue energy, a distant planet visible through the nebula, cinematic lighting, volumetric light rays, hyperrealistic textures, intricate mechanical details, science fiction concept art, 8k resolution, photorealistic, octane render, trending on ArtStation, award-winning photography"], "color": "#232", "bgcolor": "#353"}, {"id": 90, "type": "SamplerCustomAdvanced", "pos": [0, 1960], "size": [441.7259826660156, 496.************], "flags": {"collapsed": false}, "order": 36, "mode": 0, "inputs": [{"localized_name": "噪波", "name": "noise", "type": "NOISE", "link": 459}, {"localized_name": "引导器", "name": "guider", "type": "GUIDER", "link": 212}, {"localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 489}, {"localized_name": "西格玛", "name": "sigmas", "type": "SIGMAS", "link": 197}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 608}], "outputs": [{"localized_name": "Latent", "name": "output", "type": "LATENT", "links": [318, 498]}, {"localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "title": "<PERSON><PERSON> 1st pass", "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "SamplerCustomAdvanced", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#4f1489", "bgcolor": "#3b0075"}, {"id": 83, "type": "SimpleMath+", "pos": [530, 1980], "size": [320, 100], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "a", "name": "a", "shape": 7, "type": "INT,FLOAT", "link": 606}, {"localized_name": "b", "name": "b", "shape": 7, "type": "INT,FLOAT", "link": 610}, {"localized_name": "value", "name": "value", "type": "STRING", "widget": {"name": "value"}, "link": null}, {"name": "c", "shape": 7, "type": "*", "link": null}], "outputs": [{"localized_name": "整数", "name": "INT", "type": "INT", "links": [180]}, {"localized_name": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "title": "HiRes Fix - width", "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "SimpleMath+", "widget_ue_connectable": {}}, "widgets_values": ["a*b"], "color": "#14538e", "bgcolor": "#003f7a"}, {"id": 84, "type": "SimpleMath+", "pos": [530, 2140], "size": [320, 100], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "a", "name": "a", "shape": 7, "type": "INT,FLOAT", "link": 607}, {"localized_name": "b", "name": "b", "shape": 7, "type": "INT,FLOAT", "link": 609}, {"localized_name": "value", "name": "value", "type": "STRING", "widget": {"name": "value"}, "link": null}, {"name": "c", "shape": 7, "type": "*", "link": null}], "outputs": [{"localized_name": "整数", "name": "INT", "type": "INT", "links": [181]}, {"localized_name": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "title": "HiRes Fix - height", "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "SimpleMath+", "widget_ue_connectable": {}}, "widgets_values": ["a*b"], "color": "#14538e", "bgcolor": "#003f7a"}, {"id": 82, "type": "LatentUpscale", "pos": [530, 1790], "size": [320, 130], "flags": {}, "order": 38, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 498}, {"localized_name": "缩放算法", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 180}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 181}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [313, 499]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LatentUpscale", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": ["nearest-exact", 512, 512, "disabled"], "color": "#14538e", "bgcolor": "#003f7a"}, {"id": 98, "type": "SamplerCustomAdvanced", "pos": [960, 1960], "size": [450, 500], "flags": {"collapsed": false}, "order": 41, "mode": 0, "inputs": [{"localized_name": "噪波", "name": "noise", "type": "NOISE", "link": 460}, {"localized_name": "引导器", "name": "guider", "type": "GUIDER", "link": 496}, {"localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 495}, {"localized_name": "西格玛", "name": "sigmas", "type": "SIGMAS", "link": 227}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 499}], "outputs": [{"localized_name": "Latent", "name": "output", "type": "LATENT", "links": [315]}, {"localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "title": "<PERSON><PERSON> 2nd pass", "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "SamplerCustomAdvanced", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#4e1489", "bgcolor": "#3a0075"}, {"id": 271, "type": "OllamaGenerateV2", "pos": [-2443.68408203125, 2672.************], "size": [400, 270], "flags": {}, "order": 21, "mode": 4, "inputs": [{"localized_name": "connectivity", "name": "connectivity", "shape": 7, "type": "OLLAMA_CONNECTIVITY", "link": 473}, {"localized_name": "options", "name": "options", "shape": 7, "type": "OLLAMA_OPTIONS", "link": 474}, {"localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 479}, {"localized_name": "context", "name": "context", "shape": 7, "type": "OLLAMA_CONTEXT", "link": null}, {"localized_name": "meta", "name": "meta", "shape": 7, "type": "OLLAMA_META", "link": null}, {"localized_name": "system", "name": "system", "type": "STRING", "widget": {"name": "system"}, "link": null}, {"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "keep_context", "name": "keep_context", "type": "BOOLEAN", "widget": {"name": "keep_context"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}], "outputs": [{"localized_name": "result", "name": "result", "type": "STRING", "links": [605]}, {"localized_name": "context", "name": "context", "type": "OLLAMA_CONTEXT", "links": null}, {"localized_name": "meta", "name": "meta", "type": "OLLAMA_META", "links": null}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaGenerateV2", "widget_ue_connectable": {"prompt": true}}, "widgets_values": ["You are an AI artist creating image prompts from 400 tokens max. Take the input and create stunning scenes. Output only the ready prompt without extra comments.  Use natural language rather than words separated by commas\n", "What is art?", false, "text", [false, true], [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 16, "type": "CLIPTextEncode", "pos": [-1590, 2330], "size": [400, 240], "flags": {}, "order": 34, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 591}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 600}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [485]}], "title": "Positive Prompt", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["A portrait of a captivating 19-year-old goth hippy woman, drumming passionately on stage. Her vibrant rainbow hair, styled in a chic bob, frames her striking features: pale skin with a healthy glow, mesmerizing blue eyes flashing with determination, and delicate eyebrow, lip, and ear piercings adding a touch of edge.\n\nShe sits behind a purple drum kit bathed in colorful stage lights, her body moving in perfect rhythm with the pounding beat. Her expression is one of pure focus and energy, mouth slightly open in exertion as she drives the music forward. Drumsticks blur as they fly across the cymbals and toms, creating a powerful soundscape.\n\nHer attire reflects her unique blend of styles: a bold tie-dye t-shirt peeks out from beneath a flowing purple velvet skirt, giving her an edgy look.", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 40, "type": "CLIPTextEncode", "pos": [-1473.683837890625, 2612.************], "size": [400, 190], "flags": {"collapsed": false}, "order": 33, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 592}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [486]}], "title": "Negative Prompt", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["blurry, deformed, ugly, watermark, text, scribbles, noise, static, low quality, bad quality, jpeg artifacts, low resolution", [false, true]], "color": "#322", "bgcolor": "#533"}, {"id": 192, "type": "VAEDecode", "pos": [960, 2520], "size": [210, 46], "flags": {}, "order": 42, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 315}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 483}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [523, 525, 571, 578, 582]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#4e1489", "bgcolor": "#3a0075"}, {"id": 321, "type": "Bookmark (rgthree)", "pos": [2460, 1670], "size": [210, 62], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "🔖", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["3", 1], "color": "#149294", "bgcolor": "#007e80"}, {"id": 309, "type": "SamplerCustomAdvanced", "pos": [2840, 1790], "size": [360, 330], "flags": {}, "order": 52, "mode": 4, "inputs": [{"label": "noise", "localized_name": "噪波", "name": "noise", "type": "NOISE", "link": 572}, {"label": "guider", "localized_name": "引导器", "name": "guider", "type": "GUIDER", "link": 574}, {"label": "sampler", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 573}, {"label": "sigmas", "localized_name": "西格玛", "name": "sigmas", "type": "SIGMAS", "link": 562}, {"label": "latent_image", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 563}], "outputs": [{"label": "output", "localized_name": "Latent", "name": "output", "type": "LATENT", "slot_index": 0, "links": [550]}, {"label": "denoised_output", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "SamplerCustomAdvanced", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 295, "type": "VAEEncode", "pos": [2490, 1960], "size": [320, 50], "flags": {}, "order": 51, "mode": 4, "inputs": [{"label": "pixels", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 548}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 576}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [563]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "VAEEncode", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 298, "type": "BasicScheduler", "pos": [2490, 1790], "size": [320, 110], "flags": {}, "order": 23, "mode": 4, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 575}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "SIGMAS", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "slot_index": 0, "links": [562]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "BasicScheduler", "widget_ue_connectable": {}}, "widgets_values": ["normal", 8, 0.5000000000000001], "color": "#149294", "bgcolor": "#007e80"}, {"id": 314, "type": "PreviewImage", "pos": [2840, 2390], "size": [320, 360], "flags": {}, "order": 55, "mode": 4, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 568}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 310, "type": "ImageListToImageBatch", "pos": [2840, 2810], "size": [320, 30], "flags": {}, "order": 54, "mode": 4, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "link": 564}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [541, 558]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.8.1", "Node name for S&R": "ImageListToImageBatch", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 308, "type": "PreviewImage", "pos": [2840, 2900], "size": [320, 380], "flags": {}, "order": 57, "mode": 4, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 558}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 300, "type": "UpscaleModelLoader", "pos": [3230, 1790], "size": [280, 60], "flags": {}, "order": 4, "mode": 4, "inputs": [{"localized_name": "模型名称", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"label": "UPSCALE_MODEL", "localized_name": "放大模型", "name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "slot_index": 0, "links": [549]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "UpscaleModelLoader", "widget_ue_connectable": {}}, "widgets_values": ["4x_NMKD-Siax_200k.pth"], "color": "#149294", "bgcolor": "#007e80"}, {"id": 299, "type": "ImageUpscaleWithModel", "pos": [3230, 1910], "size": [280, 50], "flags": {}, "order": 45, "mode": 4, "inputs": [{"label": "upscale_model", "localized_name": "放大模型", "name": "upscale_model", "type": "UPSCALE_MODEL", "link": 549}, {"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 571}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [551, 555]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "ImageUpscaleWithModel", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 301, "type": "VAEDecodeTiled", "pos": [2840, 2180], "size": [360, 150], "flags": {}, "order": 53, "mode": 4, "inputs": [{"label": "samples", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 550}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 577}, {"localized_name": "分块尺寸", "name": "tile_size", "type": "INT", "widget": {"name": "tile_size"}, "link": null}, {"localized_name": "重叠", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "时间尺寸", "name": "temporal_size", "type": "INT", "widget": {"name": "temporal_size"}, "link": null}, {"localized_name": "时间重叠", "name": "temporal_overlap", "type": "INT", "widget": {"name": "temporal_overlap"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [564, 568]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "VAEDecodeTiled", "widget_ue_connectable": {}}, "widgets_values": [1024, 64, 64, 8], "color": "#149294", "bgcolor": "#007e80"}, {"id": 302, "type": "ImageScaleToTotalPixels", "pos": [3230, 2020], "size": [280, 90], "flags": {}, "order": 46, "mode": 4, "inputs": [{"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 551}, {"localized_name": "缩放算法", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "像素数量", "name": "megapixels", "type": "FLOAT", "widget": {"name": "megapixels"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [540, 545]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "ImageScaleToTotalPixels", "widget_ue_connectable": {}}, "widgets_values": ["lanc<PERSON>s", 8], "color": "#149294", "bgcolor": "#007e80"}, {"id": 292, "type": "TTP_Tile_image_size", "pos": [3230, 2170], "size": [280, 130], "flags": {"collapsed": false}, "order": 48, "mode": 4, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 540}, {"localized_name": "width_factor", "name": "width_factor", "type": "INT", "widget": {"name": "width_factor"}, "link": null}, {"localized_name": "height_factor", "name": "height_factor", "type": "INT", "widget": {"name": "height_factor"}, "link": null}, {"localized_name": "overlap_rate", "name": "overlap_rate", "type": "FLOAT", "widget": {"name": "overlap_rate"}, "link": null}], "outputs": [{"label": "tile_width", "localized_name": "tile_width", "name": "tile_width", "type": "INT", "slot_index": 0, "links": [546]}, {"label": "tile_height", "localized_name": "tile_height", "name": "tile_height", "type": "INT", "slot_index": 1, "links": [547]}], "properties": {"cnr_id": "comfyui_ttp_toolset", "ver": "1.0.5", "Node name for S&R": "TTP_Tile_image_size", "widget_ue_connectable": {}}, "widgets_values": [2, 3, 0.05], "color": "#149294", "bgcolor": "#007e80"}, {"id": 294, "type": "TTP_Image_Tile_Batch", "pos": [3540, 1790], "size": [320, 142], "flags": {"collapsed": false}, "order": 49, "mode": 4, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 545}, {"label": "tile_width", "localized_name": "tile_width", "name": "tile_width", "type": "INT", "widget": {"name": "tile_width"}, "link": 546}, {"label": "tile_height", "localized_name": "tile_height", "name": "tile_height", "type": "INT", "widget": {"name": "tile_height"}, "link": 547}], "outputs": [{"label": "IMAGES", "localized_name": "IMAGES", "name": "IMAGES", "type": "IMAGE", "slot_index": 0, "links": [565]}, {"label": "POSITIONS", "localized_name": "POSITIONS", "name": "POSITIONS", "type": "LIST", "slot_index": 1, "links": [542]}, {"label": "ORIGINAL_SIZE", "localized_name": "ORIGINAL_SIZE", "name": "ORIGINAL_SIZE", "type": "TUPLE", "slot_index": 2, "links": [543]}, {"label": "GRID_SIZE", "localized_name": "GRID_SIZE", "name": "GRID_SIZE", "type": "TUPLE", "slot_index": 3, "links": [544]}], "properties": {"cnr_id": "comfyui_ttp_toolset", "ver": "1.0.5", "Node name for S&R": "TTP_Image_Tile_Batch", "widget_ue_connectable": {"tile_width": true, "tile_height": true}}, "widgets_values": [1024, 1024], "color": "#149294", "bgcolor": "#007e80"}, {"id": 305, "type": "PreviewImage", "pos": [3540, 1990], "size": [220, 260], "flags": {}, "order": 47, "mode": 4, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 555}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 293, "type": "TTP_Image_Assy", "pos": [3540, 2310], "size": [320, 120], "flags": {"collapsed": false}, "order": 56, "mode": 4, "inputs": [{"label": "tiles", "localized_name": "tiles", "name": "tiles", "type": "IMAGE", "link": 541}, {"label": "positions", "localized_name": "positions", "name": "positions", "type": "LIST", "link": 542}, {"label": "original_size", "localized_name": "original_size", "name": "original_size", "type": "TUPLE", "link": 543}, {"label": "grid_size", "localized_name": "grid_size", "name": "grid_size", "type": "TUPLE", "link": 544}, {"localized_name": "padding", "name": "padding", "type": "INT", "widget": {"name": "padding"}, "link": null}], "outputs": [{"label": "RECONSTRUCTED_IMAGE", "localized_name": "RECONSTRUCTED_IMAGE", "name": "RECONSTRUCTED_IMAGE", "type": "IMAGE", "slot_index": 0, "links": [552, 553, 556]}], "properties": {"cnr_id": "comfyui_ttp_toolset", "ver": "1.0.5", "Node name for S&R": "TTP_Image_Assy", "widget_ue_connectable": {}}, "widgets_values": [128], "color": "#149294", "bgcolor": "#007e80"}, {"id": 311, "type": "ImageListToImageBatch", "pos": [3540, 2800], "size": [320, 40], "flags": {}, "order": 50, "mode": 4, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "link": 565}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [548]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.8.1", "Node name for S&R": "ImageListToImageBatch", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 303, "type": "DepthAnythingV2Preprocessor", "pos": [3890, 1790], "size": [320, 100], "flags": {}, "order": 58, "mode": 4, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 552}, {"localized_name": "ckpt_name", "name": "ckpt_name", "shape": 7, "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}, {"localized_name": "resolution", "name": "resolution", "shape": 7, "type": "INT", "widget": {"name": "resolution"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [554, 557]}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "1.0.7", "Node name for S&R": "DepthAnythingV2Preprocessor", "widget_ue_connectable": {}}, "widgets_values": ["depth_anything_v2_vitl.pth", 512], "color": "#149294", "bgcolor": "#007e80"}, {"id": 307, "type": "PreviewImage", "pos": [3890, 1950], "size": [320, 270], "flags": {}, "order": 61, "mode": 4, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 557}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 304, "type": "LayerFilter: FilmV2", "pos": [3890, 2280], "size": [320, 370], "flags": {}, "order": 60, "mode": 4, "inputs": [{"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 553}, {"label": "depth_map", "localized_name": "深度图", "name": "depth_map", "shape": 7, "type": "IMAGE", "link": 554}, {"localized_name": "中心X", "name": "center_x", "type": "FLOAT", "widget": {"name": "center_x"}, "link": null}, {"localized_name": "中心Y", "name": "center_y", "type": "FLOAT", "widget": {"name": "center_y"}, "link": null}, {"localized_name": "饱和度", "name": "saturation", "type": "FLOAT", "widget": {"name": "saturation"}, "link": null}, {"localized_name": "晕影强度", "name": "vignette_intensity", "type": "FLOAT", "widget": {"name": "vignette_intensity"}, "link": null}, {"localized_name": "噪点方法", "name": "grain_method", "type": "COMBO", "widget": {"name": "grain_method"}, "link": null}, {"localized_name": "噪点强度", "name": "grain_power", "type": "FLOAT", "widget": {"name": "grain_power"}, "link": null}, {"localized_name": "噪点缩放", "name": "grain_scale", "type": "FLOAT", "widget": {"name": "grain_scale"}, "link": null}, {"localized_name": "噪点饱和度", "name": "grain_sat", "type": "FLOAT", "widget": {"name": "grain_sat"}, "link": null}, {"localized_name": "胶片颗粒阴影", "name": "filmgrainer_shadows", "type": "FLOAT", "widget": {"name": "filmgrainer_shadows"}, "link": null}, {"localized_name": "胶片颗粒高光", "name": "filmgrainer_highs", "type": "FLOAT", "widget": {"name": "filmgrainer_highs"}, "link": null}, {"localized_name": "模糊强度", "name": "blur_strength", "type": "INT", "widget": {"name": "blur_strength"}, "link": null}, {"localized_name": "模糊焦点扩散", "name": "blur_focus_spread", "type": "FLOAT", "widget": {"name": "blur_focus_spread"}, "link": null}, {"localized_name": "焦点深度", "name": "focal_depth", "type": "FLOAT", "widget": {"name": "focal_depth"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [566]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerFilter: FilmV2", "widget_ue_connectable": {}}, "widgets_values": [0.5, 0.5, 1, 0, "filmgrainer", 0.05, 1, 0.3, 0.3, 0.2, 2, 1, 0.2], "color": "#149294", "bgcolor": "#007e80"}, {"id": 312, "type": "ColorMatch", "pos": [3890, 2710], "size": [320, 102], "flags": {}, "order": 62, "mode": 4, "inputs": [{"label": "image_ref", "localized_name": "image_ref", "name": "image_ref", "type": "IMAGE", "link": 578}, {"label": "image_target", "localized_name": "image_target", "name": "image_target", "type": "IMAGE", "link": 566}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "strength", "name": "strength", "shape": 7, "type": "FLOAT", "widget": {"name": "strength"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [570, 581]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.0.8", "Node name for S&R": "ColorMatch", "widget_ue_connectable": {}}, "widgets_values": ["mkl", 1], "color": "#149294", "bgcolor": "#007e80"}, {"id": 306, "type": "PreviewImage", "pos": [3540, 2490], "size": [320, 250], "flags": {"collapsed": false}, "order": 59, "mode": 4, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 556}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#149294", "bgcolor": "#007e80"}, {"id": 319, "type": "Image Comparer (rgthree)", "pos": [4240, 1790], "size": [800, 1150], "flags": {}, "order": 64, "mode": 4, "inputs": [{"name": "image_a", "type": "IMAGE", "link": 581}, {"name": "image_b", "type": "IMAGE", "link": 582}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide", "widget_ue_connectable": {}}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_jpfxk_00007_.png&type=temp&subfolder=&rand=0.659730288652391"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_jpfxk_00008_.png&type=temp&subfolder=&rand=0.9457603051489158"}]], "color": "#149294", "bgcolor": "#007e80"}, {"id": 316, "type": "SaveImage", "pos": [3890, 2870], "size": [320, 270], "flags": {"collapsed": true}, "order": 63, "mode": 4, "inputs": [{"label": "images", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 570}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["HiDream/Txt2Img/%date:MM%/%date:MM-dd%/%date:yyMMdd-hhmm%_HiResFixUpTTP"], "color": "#149294", "bgcolor": "#007e80"}, {"id": 320, "type": "Bookmark (rgthree)", "pos": [-1600, 1200], "size": [210, 62], "flags": {"collapsed": true}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "🔖", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["2", 1], "color": "#222", "bgcolor": "#000"}, {"id": 70, "type": "ModelSamplingSD3", "pos": [-1590, 1480], "size": [400, 60], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 484}, {"localized_name": "移位", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [590]}], "title": "Shift", "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "ModelSamplingSD3", "widget_ue_connectable": {}}, "widgets_values": [3.0000000000000004], "color": "#222", "bgcolor": "#000"}, {"id": 249, "type": "SaveImage", "pos": [1470, 1730], "size": [960, 870], "flags": {}, "order": 43, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 523}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "title": "Save 2nd Pass", "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["HiDream/Txt2Img/%date:MM%/%date:MM-dd%/%date:yyMMdd-hhmm%_HiResFix"], "color": "#222", "bgcolor": "#000"}, {"id": 198, "type": "VAEDecode", "pos": [0, 2520], "size": [210, 46], "flags": {}, "order": 37, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 318}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 482}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [392, 594]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#4f1489", "bgcolor": "#3b0075"}, {"id": 324, "type": "SaveImage", "pos": [240, 2520], "size": [960, 870], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 594}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "title": "Save 1st Pass", "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["HiDream/Txt2Img/%date:MM%/%date:MM-dd%/%date:yyMMdd-hhmm%_1stPass"], "color": "#4e1489", "bgcolor": "#3a0075"}, {"id": 199, "type": "Image Comparer (rgthree)", "pos": [-30, 2670], "size": [890, 840], "flags": {}, "order": 44, "mode": 0, "inputs": [{"dir": 3, "name": "image_a", "type": "IMAGE", "link": 525}, {"dir": 3, "name": "image_b", "type": "IMAGE", "link": 392}], "outputs": [], "title": "Image Comparer- 1st vs 2nd Pass", "properties": {"cnr_id": "rgthree-comfy", "ver": "5dc53323e07a021038af9f2a4a06ebc071f7218c", "comparer_mode": "Slide", "widget_ue_connectable": {}}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_vmvyl_00009_.png&type=temp&subfolder=&rand=0.03477938017078397"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_vmvyl_00010_.png&type=temp&subfolder=&rand=0.08401343518551085"}]], "color": "#222", "bgcolor": "#000"}, {"id": 256, "type": "RandomNoise", "pos": [-1110, 1920], "size": [320, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "噪波随机种", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"localized_name": "噪波", "name": "NOISE", "type": "NOISE", "links": [459, 460, 572]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "RandomNoise", "widget_ue_connectable": {}}, "widgets_values": [124227032495126, "fixed"], "color": "#4e1489", "bgcolor": "#3a0075"}, {"id": 96, "type": "CFGGuider", "pos": [-760, 1920], "size": [320, 100], "flags": {"collapsed": false}, "order": 35, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 587}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 485}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 486}, {"localized_name": "CFG", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"localized_name": "引导器", "name": "GUIDER", "type": "GUIDER", "links": [212, 496, 574]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "CFGGuider", "widget_ue_connectable": {"cfg": true}}, "widgets_values": [1], "color": "#4e1489", "bgcolor": "#3a0075"}, {"id": 327, "type": "Note", "pos": [2570, 1430], "size": [440, 250], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["For Fast & Dev - Scheduler Normal, Steps 8\n\nFor Full - Scheduler Simple, Steps 20"], "color": "#149294", "bgcolor": "#007e80"}, {"id": 268, "type": "OllamaConnectivityV2", "pos": [-2803.68408203125, 2672.************], "size": [330, 130], "flags": {}, "order": 8, "mode": 4, "inputs": [{"localized_name": "url", "name": "url", "type": "STRING", "widget": {"name": "url"}, "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "keep_alive", "name": "keep_alive", "type": "INT", "widget": {"name": "keep_alive"}, "link": null}, {"localized_name": "keep_alive_unit", "name": "keep_alive_unit", "type": "COMBO", "widget": {"name": "keep_alive_unit"}, "link": null}], "outputs": [{"localized_name": "connection", "name": "connection", "type": "OLLAMA_CONNECTIVITY", "links": [473]}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaConnectivityV2", "widget_ue_connectable": {}}, "widgets_values": ["http://127.0.0.1:11434", "deepseek-r1:7b", 0, "minutes"], "color": "#232", "bgcolor": "#353"}, {"id": 269, "type": "OllamaOptionsV2", "pos": [-2803.68408203125, 2862.************], "size": [330, 760], "flags": {}, "order": 9, "mode": 4, "inputs": [{"localized_name": "enable_mirostat", "name": "enable_mirostat", "type": "BOOLEAN", "widget": {"name": "enable_mirostat"}, "link": null}, {"localized_name": "mi<PERSON><PERSON>", "name": "mi<PERSON><PERSON>", "type": "INT", "widget": {"name": "mi<PERSON><PERSON>"}, "link": null}, {"localized_name": "enable_mirostat_eta", "name": "enable_mirostat_eta", "type": "BOOLEAN", "widget": {"name": "enable_mirostat_eta"}, "link": null}, {"localized_name": "mirostat_eta", "name": "mirostat_eta", "type": "FLOAT", "widget": {"name": "mirostat_eta"}, "link": null}, {"localized_name": "enable_mirostat_tau", "name": "enable_mirostat_tau", "type": "BOOLEAN", "widget": {"name": "enable_mirostat_tau"}, "link": null}, {"localized_name": "mirostat_tau", "name": "mirostat_tau", "type": "FLOAT", "widget": {"name": "mirostat_tau"}, "link": null}, {"localized_name": "enable_num_ctx", "name": "enable_num_ctx", "type": "BOOLEAN", "widget": {"name": "enable_num_ctx"}, "link": null}, {"localized_name": "num_ctx", "name": "num_ctx", "type": "INT", "widget": {"name": "num_ctx"}, "link": null}, {"localized_name": "enable_repeat_last_n", "name": "enable_repeat_last_n", "type": "BOOLEAN", "widget": {"name": "enable_repeat_last_n"}, "link": null}, {"localized_name": "repeat_last_n", "name": "repeat_last_n", "type": "INT", "widget": {"name": "repeat_last_n"}, "link": null}, {"localized_name": "enable_repeat_penalty", "name": "enable_repeat_penalty", "type": "BOOLEAN", "widget": {"name": "enable_repeat_penalty"}, "link": null}, {"localized_name": "repeat_penalty", "name": "repeat_penalty", "type": "FLOAT", "widget": {"name": "repeat_penalty"}, "link": null}, {"localized_name": "enable_temperature", "name": "enable_temperature", "type": "BOOLEAN", "widget": {"name": "enable_temperature"}, "link": null}, {"localized_name": "temperature", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "enable_seed", "name": "enable_seed", "type": "BOOLEAN", "widget": {"name": "enable_seed"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "enable_stop", "name": "enable_stop", "type": "BOOLEAN", "widget": {"name": "enable_stop"}, "link": null}, {"localized_name": "stop", "name": "stop", "type": "STRING", "widget": {"name": "stop"}, "link": null}, {"localized_name": "enable_tfs_z", "name": "enable_tfs_z", "type": "BOOLEAN", "widget": {"name": "enable_tfs_z"}, "link": null}, {"localized_name": "tfs_z", "name": "tfs_z", "type": "FLOAT", "widget": {"name": "tfs_z"}, "link": null}, {"localized_name": "enable_num_predict", "name": "enable_num_predict", "type": "BOOLEAN", "widget": {"name": "enable_num_predict"}, "link": null}, {"localized_name": "num_predict", "name": "num_predict", "type": "INT", "widget": {"name": "num_predict"}, "link": null}, {"localized_name": "enable_top_k", "name": "enable_top_k", "type": "BOOLEAN", "widget": {"name": "enable_top_k"}, "link": null}, {"localized_name": "top_k", "name": "top_k", "type": "INT", "widget": {"name": "top_k"}, "link": null}, {"localized_name": "enable_top_p", "name": "enable_top_p", "type": "BOOLEAN", "widget": {"name": "enable_top_p"}, "link": null}, {"localized_name": "top_p", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "enable_min_p", "name": "enable_min_p", "type": "BOOLEAN", "widget": {"name": "enable_min_p"}, "link": null}, {"localized_name": "min_p", "name": "min_p", "type": "FLOAT", "widget": {"name": "min_p"}, "link": null}, {"localized_name": "debug", "name": "debug", "type": "BOOLEAN", "widget": {"name": "debug"}, "link": null}], "outputs": [{"localized_name": "options", "name": "options", "type": "OLLAMA_OPTIONS", "links": [474]}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaOptionsV2", "widget_ue_connectable": {}}, "widgets_values": [false, 0, false, 0.1, false, 5, false, 2048, false, 64, false, 1.1, false, 0.8, false, 67434665, "fixed", false, "", false, 1, false, -1, false, 40, false, 0.9, false, 0, false], "color": "#232", "bgcolor": "#353"}, {"id": 328, "type": "Fast Groups Bypasser (rgthree)", "pos": [3040, 1610], "size": [290, 70], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "OPT_CONNECTION", "type": "*", "links": null}], "properties": {"matchColors": "", "matchTitle": "TTP Upscaler", "showNav": true, "sort": "position", "customSortAlphabet": "", "toggleRestriction": "default", "widget_ue_connectable": {}}, "color": "#149294", "bgcolor": "#007e80"}, {"id": 331, "type": "PrimitiveFloat", "pos": [548.6836547851562, 2327.937744140625], "size": [270, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "FLOAT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "浮点", "name": "FLOAT", "type": "FLOAT", "links": [609, 610]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PrimitiveFloat"}, "widgets_values": [1.5]}, {"id": 55, "type": "VAELoader", "pos": [-1574.6666259765625, 1818.6663818359375], "size": [400, 60], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [482, 483, 576, 577]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAELoader", "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"], "color": "#222", "bgcolor": "#000"}, {"id": 323, "type": "<PERSON> Lora <PERSON> (rgthree)", "pos": [-1590, 1600], "size": [400, 142], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 590}, {"name": "clip", "type": "CLIP", "link": 611}], "outputs": [{"name": "MODEL", "shape": 3, "type": "MODEL", "links": [587, 588, 589]}, {"name": "CLIP", "shape": 3, "type": "CLIP", "links": [591, 592]}], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "Show Strengths": "Single Strength", "widget_ue_connectable": {}}, "widgets_values": [null, {"type": "PowerLoraLoaderHeaderWidget"}, {"on": false, "lora": "01. SDXL/Cinematic Hollywood Film.safetensors", "strength": 0.5, "strengthTwo": null}, null, ""], "color": "#222", "bgcolor": "#000"}, {"id": 260, "type": "OllamaGenerateV2", "pos": [-2537.814208984375, 1581.0311279296875], "size": [406.773681640625, 406.6026916503906], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "connectivity", "name": "connectivity", "shape": 7, "type": "OLLAMA_CONNECTIVITY", "link": 466}, {"localized_name": "options", "name": "options", "shape": 7, "type": "OLLAMA_OPTIONS", "link": 467}, {"localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "context", "name": "context", "shape": 7, "type": "OLLAMA_CONTEXT", "link": null}, {"localized_name": "meta", "name": "meta", "shape": 7, "type": "OLLAMA_META", "link": null}, {"localized_name": "system", "name": "system", "type": "STRING", "widget": {"name": "system"}, "link": null}, {"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 601}, {"localized_name": "keep_context", "name": "keep_context", "type": "BOOLEAN", "widget": {"name": "keep_context"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}], "outputs": [{"localized_name": "result", "name": "result", "type": "STRING", "links": [603]}, {"localized_name": "context", "name": "context", "type": "OLLAMA_CONTEXT", "links": null}, {"localized_name": "meta", "name": "meta", "type": "OLLAMA_META", "links": null}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaGenerateV2", "widget_ue_connectable": {"prompt": true}}, "widgets_values": ["You are an AI artist creating image prompts from 400 tokens max. Take the input and create stunning scenes. Output only the ready prompt without extra comments. Use natural language and avoid words separated by commas", "What is art?", true, "text", [false, true], [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 262, "type": "OllamaOptionsV2", "pos": [-2890.70751953125, 1771.8189697265625], "size": [330, 760], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "enable_mirostat", "name": "enable_mirostat", "type": "BOOLEAN", "widget": {"name": "enable_mirostat"}, "link": null}, {"localized_name": "mi<PERSON><PERSON>", "name": "mi<PERSON><PERSON>", "type": "INT", "widget": {"name": "mi<PERSON><PERSON>"}, "link": null}, {"localized_name": "enable_mirostat_eta", "name": "enable_mirostat_eta", "type": "BOOLEAN", "widget": {"name": "enable_mirostat_eta"}, "link": null}, {"localized_name": "mirostat_eta", "name": "mirostat_eta", "type": "FLOAT", "widget": {"name": "mirostat_eta"}, "link": null}, {"localized_name": "enable_mirostat_tau", "name": "enable_mirostat_tau", "type": "BOOLEAN", "widget": {"name": "enable_mirostat_tau"}, "link": null}, {"localized_name": "mirostat_tau", "name": "mirostat_tau", "type": "FLOAT", "widget": {"name": "mirostat_tau"}, "link": null}, {"localized_name": "enable_num_ctx", "name": "enable_num_ctx", "type": "BOOLEAN", "widget": {"name": "enable_num_ctx"}, "link": null}, {"localized_name": "num_ctx", "name": "num_ctx", "type": "INT", "widget": {"name": "num_ctx"}, "link": null}, {"localized_name": "enable_repeat_last_n", "name": "enable_repeat_last_n", "type": "BOOLEAN", "widget": {"name": "enable_repeat_last_n"}, "link": null}, {"localized_name": "repeat_last_n", "name": "repeat_last_n", "type": "INT", "widget": {"name": "repeat_last_n"}, "link": null}, {"localized_name": "enable_repeat_penalty", "name": "enable_repeat_penalty", "type": "BOOLEAN", "widget": {"name": "enable_repeat_penalty"}, "link": null}, {"localized_name": "repeat_penalty", "name": "repeat_penalty", "type": "FLOAT", "widget": {"name": "repeat_penalty"}, "link": null}, {"localized_name": "enable_temperature", "name": "enable_temperature", "type": "BOOLEAN", "widget": {"name": "enable_temperature"}, "link": null}, {"localized_name": "temperature", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "enable_seed", "name": "enable_seed", "type": "BOOLEAN", "widget": {"name": "enable_seed"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "enable_stop", "name": "enable_stop", "type": "BOOLEAN", "widget": {"name": "enable_stop"}, "link": null}, {"localized_name": "stop", "name": "stop", "type": "STRING", "widget": {"name": "stop"}, "link": null}, {"localized_name": "enable_tfs_z", "name": "enable_tfs_z", "type": "BOOLEAN", "widget": {"name": "enable_tfs_z"}, "link": null}, {"localized_name": "tfs_z", "name": "tfs_z", "type": "FLOAT", "widget": {"name": "tfs_z"}, "link": null}, {"localized_name": "enable_num_predict", "name": "enable_num_predict", "type": "BOOLEAN", "widget": {"name": "enable_num_predict"}, "link": null}, {"localized_name": "num_predict", "name": "num_predict", "type": "INT", "widget": {"name": "num_predict"}, "link": null}, {"localized_name": "enable_top_k", "name": "enable_top_k", "type": "BOOLEAN", "widget": {"name": "enable_top_k"}, "link": null}, {"localized_name": "top_k", "name": "top_k", "type": "INT", "widget": {"name": "top_k"}, "link": null}, {"localized_name": "enable_top_p", "name": "enable_top_p", "type": "BOOLEAN", "widget": {"name": "enable_top_p"}, "link": null}, {"localized_name": "top_p", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "enable_min_p", "name": "enable_min_p", "type": "BOOLEAN", "widget": {"name": "enable_min_p"}, "link": null}, {"localized_name": "min_p", "name": "min_p", "type": "FLOAT", "widget": {"name": "min_p"}, "link": null}, {"localized_name": "debug", "name": "debug", "type": "BOOLEAN", "widget": {"name": "debug"}, "link": null}], "outputs": [{"localized_name": "options", "name": "options", "type": "OLLAMA_OPTIONS", "links": [467]}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaOptionsV2", "widget_ue_connectable": {}}, "widgets_values": [false, 0, false, 0.1, false, 5, false, 2048, false, 64, false, 1.1, false, 0.8, false, 792818250, "fixed", false, "", false, 1, false, -1, false, 40, false, 0.9, false, 0, false], "color": "#232", "bgcolor": "#353"}, {"id": 54, "type": "QuadrupleCLIPLoader", "pos": [-2109.9443359375, 1293.148681640625], "size": [400, 130], "flags": {"collapsed": false}, "order": 14, "mode": 0, "inputs": [{"localized_name": "clip_name1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "clip_name2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "clip_name3", "name": "clip_name3", "type": "COMBO", "widget": {"name": "clip_name3"}, "link": null}, {"localized_name": "clip_name4", "name": "clip_name4", "type": "COMBO", "widget": {"name": "clip_name4"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [611]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "QuadrupleCLIPLoader", "widget_ue_connectable": {}}, "widgets_values": ["clip_l_hidream.safetensors", "clip_g.safetensors", "t5xxl_fp16.safetensors", "llama_3.1_8b_instruct_fp8_scaled.safetensors"], "color": "#222", "bgcolor": "#000"}, {"id": 261, "type": "OllamaConnectivityV2", "pos": [-2907.25048828125, 1584.080810546875], "size": [330, 130], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "url", "name": "url", "type": "STRING", "widget": {"name": "url"}, "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "keep_alive", "name": "keep_alive", "type": "INT", "widget": {"name": "keep_alive"}, "link": null}, {"localized_name": "keep_alive_unit", "name": "keep_alive_unit", "type": "COMBO", "widget": {"name": "keep_alive_unit"}, "link": null}], "outputs": [{"localized_name": "connection", "name": "connection", "type": "OLLAMA_CONNECTIVITY", "links": [466]}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaConnectivityV2", "widget_ue_connectable": {}}, "widgets_values": ["http://127.0.0.1:11434", "gemma2:27b", 0, "minutes"], "color": "#232", "bgcolor": "#353"}, {"id": 69, "type": "UNETLoader", "pos": [-1590, 1340], "size": [400, 82], "flags": {"collapsed": false}, "order": 16, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [484, 575]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["hidream_full_il_model.safetensors", "default"], "color": "#222", "bgcolor": "#000"}, {"id": 92, "type": "KSamplerSelect", "pos": [-760, 2080], "size": [320, 60], "flags": {"collapsed": false}, "order": 17, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [488]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "KSamplerSelect", "widget_ue_connectable": {"sampler_name": true}}, "widgets_values": ["uni_pc"], "color": "#4e1489", "bgcolor": "#3a0075"}, {"id": 325, "type": "Note", "pos": [-510, 1590], "size": [450, 220], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["Use recommended Sampler and Scheduler for the HiDream model:\n\nFull - uni_pc & simple.\n\nDev & Fast - lcm & normal.\n\nSteps:\n\nFull - 1st Pass: 50, 2nd Pass: 20\n\nDev - 1st Pass: 28, 2nd Pass: 8\n\nFast - 1st Pass: 16 2nd Pass: 8\n"], "color": "#4e1489", "bgcolor": "#3a0075"}, {"id": 91, "type": "BasicScheduler", "pos": [0, 1790], "size": [320, 110], "flags": {"collapsed": false}, "order": 31, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 588}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [197]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "BasicScheduler", "widget_ue_connectable": {"scheduler": true, "steps": true, "denoise": true}}, "widgets_values": ["simple", 50, 1], "color": "#4f1489", "bgcolor": "#3b0075"}, {"id": 99, "type": "BasicScheduler", "pos": [960, 1790], "size": [320, 110], "flags": {"collapsed": false}, "order": 32, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 589}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [227]}], "title": "BasicScheduler 2nd pass", "properties": {"cnr_id": "comfy-core", "ver": "0.3.30", "Node name for S&R": "BasicScheduler", "widget_ue_connectable": {"scheduler": true}}, "widgets_values": ["simple", 20, 0.5000000000000001], "color": "#4e1489", "bgcolor": "#3a0075"}, {"id": 329, "type": "CR Prompt Text", "pos": [-3309.919677734375, 1733.6025390625], "size": [400, 200], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}], "outputs": [{"localized_name": "prompt", "name": "prompt", "type": "STRING", "links": [601]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Prompt Text"}, "widgets_values": ["我要画一个动漫风开放世界游戏的UI界面，给我一段英文描述词", [false, true]]}, {"id": 266, "type": "ShowText|pysssss", "pos": [-2107.15283203125, 1581.1728515625], "size": [450, 430], "flags": {}, "order": 30, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 603}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [600]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.4", "Node name for S&R": "ShowText|pysssss", "widget_ue_connectable": {}}, "widgets_values": ["Design a vibrant user interface for an anime-style open world game, featuring stylized icons, playful typography, and dynamic map elements that evoke a sense of adventure and discovery.  \n\n\n"], "color": "#232", "bgcolor": "#353"}, {"id": 330, "type": "SDXLEmptyLatentSizePicker+", "pos": [-1097.04296875, 2078.5517578125], "size": [319.8736267089844, 170], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "resolution", "name": "resolution", "type": "COMBO", "widget": {"name": "resolution"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}, {"localized_name": "width_override", "name": "width_override", "type": "INT", "widget": {"name": "width_override"}, "link": null}, {"localized_name": "height_override", "name": "height_override", "type": "INT", "widget": {"name": "height_override"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [608]}, {"localized_name": "width", "name": "width", "type": "INT", "links": [606]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [607]}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "SDXLEmptyLatentSizePicker+"}, "widgets_values": ["1344x768 (1.75)", 1, 0, 0]}, {"id": 93, "type": "DetailDaemonSamplerNode", "pos": [-410, 1920], "size": [320, 274], "flags": {"collapsed": false}, "order": 24, "mode": 4, "inputs": [{"localized_name": "sampler", "name": "sampler", "type": "SAMPLER", "link": 488}, {"localized_name": "detail_amount", "name": "detail_amount", "type": "FLOAT", "widget": {"name": "detail_amount"}, "link": null}, {"localized_name": "start", "name": "start", "type": "FLOAT", "widget": {"name": "start"}, "link": null}, {"localized_name": "end", "name": "end", "type": "FLOAT", "widget": {"name": "end"}, "link": null}, {"localized_name": "bias", "name": "bias", "type": "FLOAT", "widget": {"name": "bias"}, "link": null}, {"localized_name": "exponent", "name": "exponent", "type": "FLOAT", "widget": {"name": "exponent"}, "link": null}, {"localized_name": "start_offset", "name": "start_offset", "type": "FLOAT", "widget": {"name": "start_offset"}, "link": null}, {"localized_name": "end_offset", "name": "end_offset", "type": "FLOAT", "widget": {"name": "end_offset"}, "link": null}, {"localized_name": "fade", "name": "fade", "type": "FLOAT", "widget": {"name": "fade"}, "link": null}, {"localized_name": "smooth", "name": "smooth", "type": "BOOLEAN", "widget": {"name": "smooth"}, "link": null}, {"localized_name": "cfg_scale_override", "name": "cfg_scale_override", "type": "FLOAT", "widget": {"name": "cfg_scale_override"}, "link": null}], "outputs": [{"localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [489, 495, 573]}], "properties": {"cnr_id": "comfyui-detail-daemon", "ver": "f391accbda2d309cdcbec65cb9fcc80a41197b20", "Node name for S&R": "DetailDaemonSamplerNode", "widget_ue_connectable": {}}, "widgets_values": [0.10000000000000002, 0.2, 0.8, 0.5, 1, 0, 0, 0, true, 0], "color": "#4e1489", "bgcolor": "#3a0075"}], "links": [[180, 83, 0, 82, 2, "INT"], [181, 84, 0, 82, 3, "INT"], [197, 91, 0, 90, 3, "SIGMAS"], [212, 96, 0, 90, 1, "GUIDER"], [227, 99, 0, 98, 3, "SIGMAS"], [313, 82, 0, 190, 0, "*"], [315, 98, 0, 192, 0, "LATENT"], [318, 90, 0, 198, 0, "LATENT"], [392, 198, 0, 199, 1, "IMAGE"], [459, 256, 0, 90, 0, "NOISE"], [460, 256, 0, 98, 0, "NOISE"], [466, 261, 0, 260, 0, "OLLAMA_CONNECTIVITY"], [467, 262, 0, 260, 1, "OLLAMA_OPTIONS"], [473, 268, 0, 271, 0, "OLLAMA_CONNECTIVITY"], [474, 269, 0, 271, 1, "OLLAMA_OPTIONS"], [479, 275, 0, 271, 2, "IMAGE"], [482, 55, 0, 198, 1, "VAE"], [483, 55, 0, 192, 1, "VAE"], [484, 69, 0, 70, 0, "MODEL"], [485, 16, 0, 96, 1, "CONDITIONING"], [486, 40, 0, 96, 2, "CONDITIONING"], [488, 92, 0, 93, 0, "SAMPLER"], [489, 93, 0, 90, 2, "SAMPLER"], [495, 93, 0, 98, 2, "SAMPLER"], [496, 96, 0, 98, 1, "GUIDER"], [498, 90, 0, 82, 0, "LATENT"], [499, 82, 0, 98, 4, "LATENT"], [523, 192, 0, 249, 0, "IMAGE"], [525, 192, 0, 199, 0, "IMAGE"], [540, 302, 0, 292, 0, "IMAGE"], [541, 310, 0, 293, 0, "IMAGE"], [542, 294, 1, 293, 1, "LIST"], [543, 294, 2, 293, 2, "TUPLE"], [544, 294, 3, 293, 3, "TUPLE"], [545, 302, 0, 294, 0, "IMAGE"], [546, 292, 0, 294, 1, "INT"], [547, 292, 1, 294, 2, "INT"], [548, 311, 0, 295, 0, "IMAGE"], [549, 300, 0, 299, 0, "UPSCALE_MODEL"], [550, 309, 0, 301, 0, "LATENT"], [551, 299, 0, 302, 0, "IMAGE"], [552, 293, 0, 303, 0, "IMAGE"], [553, 293, 0, 304, 0, "IMAGE"], [554, 303, 0, 304, 1, "IMAGE"], [555, 299, 0, 305, 0, "IMAGE"], [556, 293, 0, 306, 0, "IMAGE"], [557, 303, 0, 307, 0, "IMAGE"], [558, 310, 0, 308, 0, "IMAGE"], [562, 298, 0, 309, 3, "SIGMAS"], [563, 295, 0, 309, 4, "LATENT"], [564, 301, 0, 310, 0, "IMAGE"], [565, 294, 0, 311, 0, "IMAGE"], [566, 304, 0, 312, 1, "IMAGE"], [568, 301, 0, 314, 0, "IMAGE"], [570, 312, 0, 316, 0, "IMAGE"], [571, 192, 0, 299, 1, "IMAGE"], [572, 256, 0, 309, 0, "NOISE"], [573, 93, 0, 309, 2, "SAMPLER"], [574, 96, 0, 309, 1, "GUIDER"], [575, 69, 0, 298, 0, "MODEL"], [576, 55, 0, 295, 1, "VAE"], [577, 55, 0, 301, 1, "VAE"], [578, 192, 0, 312, 0, "IMAGE"], [581, 312, 0, 319, 0, "IMAGE"], [582, 192, 0, 319, 1, "IMAGE"], [587, 323, 0, 96, 0, "MODEL"], [588, 323, 0, 91, 0, "MODEL"], [589, 323, 0, 99, 0, "MODEL"], [590, 70, 0, 323, 0, "MODEL"], [591, 323, 1, 16, 0, "CLIP"], [592, 323, 1, 40, 0, "CLIP"], [594, 198, 0, 324, 0, "IMAGE"], [600, 266, 0, 16, 1, "STRING"], [601, 329, 0, 260, 6, "STRING"], [603, 260, 0, 266, 0, "STRING"], [605, 271, 0, 274, 0, "STRING"], [606, 330, 1, 83, 0, "INT,FLOAT"], [607, 330, 2, 84, 0, "INT,FLOAT"], [608, 330, 0, 90, 4, "LATENT"], [609, 331, 0, 84, 1, "INT,FLOAT"], [610, 331, 0, 83, 1, "INT,FLOAT"], [611, 54, 0, 323, 1, "CLIP"]], "groups": [{"id": 1, "title": "HiRex Fix", "bounding": [500, 1700, 400, 710], "color": "#0084ff", "font_size": 24, "flags": {}}, {"id": 2, "title": "HiDream model and clip loader", "bounding": [-1620, 1250, 460, 960], "color": "#444", "font_size": 24, "flags": {}}, {"id": 3, "title": "Prompts", "bounding": [-1620, 2240, 460, 600], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Workflow settings", "bounding": [-1130, 1840, 1070, 460], "color": "#8000ff", "font_size": 24, "flags": {}}, {"id": 7, "title": "01c. <PERSON><PERSON> Prompter", "bounding": [-3320, 1500, 1670, 1060], "color": "#8A8", "font_size": 24, "flags": {}}, {"id": 8, "title": "01d. Vision LLM Prompter", "bounding": [-3320, 2590, 1670, 1070], "color": "#8A8", "font_size": 24, "flags": {}}, {"id": 11, "title": "1st Pass Gen", "bounding": [-30, 1700, 500, 900], "color": "#8000ff", "font_size": 24, "flags": {}}, {"id": 12, "title": "2nd Pass Gen", "bounding": [930, 1700, 510, 900], "color": "#8000ff", "font_size": 24, "flags": {}}, {"id": 13, "title": "TTP Upscaler", "bounding": [2460, 1700, 2610, 1610], "color": "#00fbff", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.3310000000000004, "offset": [2886.5819003919414, -1302.433610967209]}, "frontendVersion": "1.18.9", "ue_links": [], "links_added_by_ue": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}