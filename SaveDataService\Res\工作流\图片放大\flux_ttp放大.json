{"id": "34fdbfba-2315-4550-8dd2-d8b35d7955cb", "revision": 0, "last_node_id": 574, "last_link_id": 804, "nodes": [{"id": 138, "type": "Reroute", "pos": [2390, 1780], "size": [75, 26], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 204}], "outputs": [{"name": "", "type": "IMAGE", "links": [208]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 222, "type": "SAMLoader", "pos": [5750.81591796875, 2119.232666015625], "size": [315, 82], "flags": {}, "order": 0, "mode": 4, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "device_mode", "name": "device_mode", "type": "COMBO", "widget": {"name": "device_mode"}, "link": null}], "outputs": [{"label": "SAM模型", "localized_name": "SAM_MODEL", "name": "SAM_MODEL", "type": "SAM_MODEL", "slot_index": 0, "links": [323]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "808b0dedf03534a2594ecb60a9d6305a044efdc2", "Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "AUTO"]}, {"id": 223, "type": "UltralyticsDetectorProvider", "pos": [5750.81591796875, 1969.23193359375], "size": [315, 78], "flags": {}, "order": 1, "mode": 4, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"label": "BBox检测", "localized_name": "BBOX_DETECTOR", "name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "slot_index": 0, "links": [322]}, {"label": "SEGM检测", "localized_name": "SEGM_DETECTOR", "name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null}], "properties": {"cnr_id": "comfyui-impact-subpack", "ver": "74db20c95eca152a6d686c914edc0ef4e4762cb8", "Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 250, "type": "CLIPVisionEncode", "pos": [6150.81591796875, 1399.23193359375], "size": [315, 78], "flags": {}, "order": 19, "mode": 4, "inputs": [{"label": "CLIP视觉", "localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 367}, {"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 368}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "CLIP视觉输出", "localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [366]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 165, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2142.162841796875, 1295.4901123046875], "size": [315, 126], "flags": {}, "order": 16, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 240}, {"label": "CLIP", "localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 241}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [242]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [243]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["flux\\FLUX-dev-lora-One-Click-Creative-Template.safetensors", 1, 1]}, {"id": 151, "type": "UNETLoader", "pos": [1818.376220703125, 1297.2904052734375], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [240]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev-fp8.safetensors", "fp8_e4m3fn_fast"]}, {"id": 152, "type": "DualCLIPLoader", "pos": [1808.3759765625, 1432.3358154296875], "size": [315, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [241]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn.safetensors", "flux", "default"]}, {"id": 154, "type": "UpscaleModelLoader", "pos": [1800.271240234375, 1577.75048828125], "size": [315, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "模型名称", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"label": "放大模型", "localized_name": "放大模型", "name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "slot_index": 0, "links": [207]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "UpscaleModelLoader"}, "widgets_values": ["4x_NMKD-Siax_200k.pth"]}, {"id": 153, "type": "VAELoader", "pos": [2143.16455078125, 1460.5426025390625], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [165, 175, 199, 216, 318]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.sft"]}, {"id": 141, "type": "ImageUpscaleWithModel", "pos": [2149.4677734375, 1569.10302734375], "size": [300.4600830078125, 46], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "放大模型", "localized_name": "放大模型", "name": "upscale_model", "type": "UPSCALE_MODEL", "link": 207}, {"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 208}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [209]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "ImageUpscaleWithModel"}, "widgets_values": []}, {"id": 164, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2480.54296875, 1296.2093505859375], "size": [315, 126], "flags": {}, "order": 20, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 242}, {"label": "CLIP", "localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 243}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [238]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [239]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["flux\\Hand v2.safetensors", 0.5, 0.5]}, {"id": 135, "type": "CR Prompt Text", "pos": [2464.697998046875, 1448.462890625], "size": [343.0618896484375, 175.9185791015625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}], "outputs": [{"label": "提示词文本", "localized_name": "prompt", "name": "prompt", "type": "STRING", "links": [196]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Prompt Text"}, "widgets_values": ["", [false, true]]}, {"id": 131, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2804.578369140625, 1297.6365966796875], "size": [315, 126], "flags": {}, "order": 22, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 238}, {"label": "CLIP", "localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 239}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [163, 188, 218, 316]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [176, 195, 317]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["flux\\AntiBlur.safetensors", 1, 1]}, {"id": 140, "type": "MathExpression|pysssss", "pos": [2091.7939453125, 1660.5460205078125], "size": [400, 200], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "a", "localized_name": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 206}, {"label": "b", "localized_name": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}, {"label": "c", "localized_name": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": null}, {"localized_name": "expression", "name": "expression", "type": "INT,FLOAT,IMAGE,LATENT", "widget": {"name": "expression"}, "link": null}], "outputs": [{"label": "整数", "localized_name": "整数", "name": "INT", "type": "INT", "slot_index": 0, "links": []}, {"label": "浮点", "localized_name": "浮点", "name": "FLOAT", "type": "FLOAT", "slot_index": 1, "links": [210]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "bbda5e52ad580c13ceaa53136d9c2bed9137bd2e", "Node name for S&R": "MathExpression|pysssss"}, "widgets_values": ["a/4", [false, true]]}, {"id": 148, "type": "ImpactInt", "pos": [1763.4063720703125, 1669.5589599609375], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "整数", "localized_name": "整数", "name": "INT", "type": "INT", "slot_index": 0, "links": [206]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "808b0dedf03534a2594ecb60a9d6305a044efdc2", "Node name for S&R": "ImpactInt"}, "widgets_values": [6]}, {"id": 142, "type": "ImageScaleBy", "pos": [2503.601318359375, 1661.7181396484375], "size": [315, 82], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 209}, {"localized_name": "缩放算法", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"label": "系数", "localized_name": "缩放系数", "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}, "link": 210}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [183]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 1]}, {"id": 126, "type": "Reroute", "pos": [2820.091064453125, 1597.7615966796875], "size": [75, 26], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 183}], "outputs": [{"name": "", "type": "IMAGE", "links": [184, 185]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 127, "type": "TTP_Tile_image_size", "pos": [2902.43359375, 1628.1162109375], "size": [257.0339050292969, 126], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 184}, {"localized_name": "width_factor", "name": "width_factor", "type": "INT", "widget": {"name": "width_factor"}, "link": null}, {"localized_name": "height_factor", "name": "height_factor", "type": "INT", "widget": {"name": "height_factor"}, "link": null}, {"localized_name": "overlap_rate", "name": "overlap_rate", "type": "FLOAT", "widget": {"name": "overlap_rate"}, "link": null}], "outputs": [{"label": "tile_width", "localized_name": "tile_width", "name": "tile_width", "type": "INT", "slot_index": 0, "links": [186]}, {"label": "tile_height", "localized_name": "tile_height", "name": "tile_height", "type": "INT", "slot_index": 1, "links": [187]}], "properties": {"cnr_id": "comfyui_ttp_toolset", "ver": "13bd85fc5ea8c3c6ecf2170d613c2a099c0c0d5b", "Node name for S&R": "TTP_Tile_image_size"}, "widgets_values": [2, 3, 0.05]}, {"id": 121, "type": "CLIPTextEncode", "pos": [2528.017333984375, 1788.4813232421875], "size": [210, 88], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 176}, {"label": "文本", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 177}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [160, 180]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["", [false, true]]}, {"id": 128, "type": "TTP_Image_Tile_Batch", "pos": [2772.2529296875, 1775.4180908203125], "size": [250.18014526367188, 142], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 185}, {"label": "tile_width", "localized_name": "tile_width", "name": "tile_width", "type": "INT", "widget": {"name": "tile_width"}, "link": 186}, {"label": "tile_height", "localized_name": "tile_height", "name": "tile_height", "type": "INT", "widget": {"name": "tile_height"}, "link": 187}], "outputs": [{"label": "IMAGES", "localized_name": "IMAGES", "name": "IMAGES", "type": "IMAGE", "slot_index": 0, "links": [173]}, {"label": "POSITIONS", "localized_name": "POSITIONS", "name": "POSITIONS", "type": "LIST", "slot_index": 1, "links": [181, 201]}, {"label": "ORIGINAL_SIZE", "localized_name": "ORIGINAL_SIZE", "name": "ORIGINAL_SIZE", "type": "TUPLE", "slot_index": 2, "links": [202]}, {"label": "GRID_SIZE", "localized_name": "GRID_SIZE", "name": "GRID_SIZE", "type": "TUPLE", "slot_index": 3, "links": [203]}], "properties": {"cnr_id": "comfyui_ttp_toolset", "ver": "13bd85fc5ea8c3c6ecf2170d613c2a099c0c0d5b", "Node name for S&R": "TTP_Image_Tile_Batch"}, "widgets_values": [1024, 1024]}, {"id": 147, "type": "LoadImage", "pos": [1451.9403076171875, 1749.8756103515625], "size": [615.3712158203125, 382.4696960449219], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [217]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "LoadImage"}, "widgets_values": ["55665_.png", "1002@880"]}, {"id": 145, "type": "ImageResizeKJ", "pos": [2108.277587890625, 1887.3104248046875], "size": [315, 266], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 217}, {"label": "参考图像", "localized_name": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "keep_proportion", "name": "keep_proportion", "type": "BOOLEAN", "widget": {"name": "keep_proportion"}, "link": null}, {"localized_name": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}, "link": null}, {"localized_name": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [204]}, {"label": "宽度", "localized_name": "width", "name": "width", "type": "INT", "links": null}, {"label": "高度", "localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "8f3cc622a8c417eafc9fe381d57db53ade17124b", "Node name for S&R": "ImageResizeKJ"}, "widgets_values": [1024, 1024, "lanc<PERSON>s", true, 0, 0]}, {"id": 118, "type": "Florence2Run", "pos": [2464.3251953125, 1883.262451171875], "size": [400, 364], "flags": {}, "order": 39, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 171}, {"label": "Florence2", "localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "link": 172}, {"localized_name": "text_input", "name": "text_input", "type": "STRING", "widget": {"name": "text_input"}, "link": null}, {"localized_name": "task", "name": "task", "type": "COMBO", "widget": {"name": "task"}, "link": null}, {"localized_name": "fill_mask", "name": "fill_mask", "type": "BOOLEAN", "widget": {"name": "fill_mask"}, "link": null}, {"localized_name": "keep_model_loaded", "name": "keep_model_loaded", "shape": 7, "type": "BOOLEAN", "widget": {"name": "keep_model_loaded"}, "link": null}, {"localized_name": "max_new_tokens", "name": "max_new_tokens", "shape": 7, "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "num_beams", "name": "num_beams", "shape": 7, "type": "INT", "widget": {"name": "num_beams"}, "link": null}, {"localized_name": "do_sample", "name": "do_sample", "shape": 7, "type": "BOOLEAN", "widget": {"name": "do_sample"}, "link": null}, {"localized_name": "output_mask_select", "name": "output_mask_select", "shape": 7, "type": "STRING", "widget": {"name": "output_mask_select"}, "link": null}, {"localized_name": "seed", "name": "seed", "shape": 7, "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"label": "caption", "localized_name": "caption", "name": "caption", "type": "STRING", "slot_index": 2, "links": [205]}, {"label": "json数据", "localized_name": "data", "name": "data", "type": "JSON", "links": null}], "properties": {"cnr_id": "comfyui-florence2", "ver": "90b012e922f8bb0482bcd2ae24cdc191ec12a11f", "Node name for S&R": "Florence2Run"}, "widgets_values": ["", "detailed_caption", true, false, 1024, 3, true, "", 1087147670407102, "fixed", [false, true]]}, {"id": 116, "type": "VAEEncode", "pos": [2885.009033203125, 1926.759033203125], "size": [210, 46], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "图像", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 164}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 165}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [170]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 115, "type": "BasicScheduler", "pos": [3200.569580078125, 1751.2930908203125], "size": [264.7033386230469, 109.95319366455078], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 163}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Sigmas", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [169]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 10, 0.5]}, {"id": 112, "type": "FluxGuidance", "pos": [3194.74169921875, 1894.1463623046875], "size": [254.8834228515625, 58], "flags": {}, "order": 42, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 160}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [189]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "FluxGuidance"}, "widgets_values": [2.5]}, {"id": 129, "type": "BasicGuider", "pos": [3230.************, 2001.172607421875], "size": [241.79998779296875, 46], "flags": {}, "order": 44, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 188}, {"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 189}], "outputs": [{"label": "引导", "localized_name": "引导器", "name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [162, 167]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 111, "type": "RandomNoise", "pos": [3188.4833984375, 2094.2412109375], "size": [274.7954406738281, 85.92839050292969], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "噪波随机种", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"label": "噪波生成", "localized_name": "噪波", "name": "NOISE", "type": "NOISE", "slot_index": 0, "links": [166]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "RandomNoise"}, "widgets_values": [316430325547060, "fixed"]}, {"id": 110, "type": "KSamplerSelect", "pos": [3181.************, 1630.49951171875], "size": [315, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"label": "采样器", "localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [168]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 117, "type": "SamplerCustomAdvanced", "pos": [3501.************, 1736.5433349609375], "size": [299.************, 326], "flags": {}, "order": 47, "mode": 0, "inputs": [{"label": "噪波生成", "localized_name": "噪波", "name": "noise", "type": "NOISE", "link": 166}, {"label": "引导", "localized_name": "引导器", "name": "guider", "type": "GUIDER", "link": 167}, {"label": "采样器", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 168}, {"label": "Sigmas", "localized_name": "西格玛", "name": "sigmas", "type": "SIGMAS", "link": 169}, {"label": "Latent", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 170}], "outputs": [{"label": "输出", "localized_name": "Latent", "name": "output", "type": "LATENT", "slot_index": 0, "links": [198]}, {"label": "降噪输出", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 136, "type": "VAEDecodeTiled", "pos": [3808.************, 1731.7498779296875], "size": [295.677734375, 150], "flags": {}, "order": 49, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 198}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 199}, {"localized_name": "分块尺寸", "name": "tile_size", "type": "INT", "widget": {"name": "tile_size"}, "link": null}, {"localized_name": "重叠", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "时间尺寸", "name": "temporal_size", "type": "INT", "widget": {"name": "temporal_size"}, "link": null}, {"localized_name": "时间重叠", "name": "temporal_overlap", "type": "INT", "widget": {"name": "temporal_overlap"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [161]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [1024, 64, 64, 8]}, {"id": 150, "type": "DownloadAndLoadFlorence2Model", "pos": [2014.0523681640625, 2209.909912109375], "size": [381.9399719238281, 109.9835205078125], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "LoRA", "localized_name": "lora", "name": "lora", "shape": 7, "type": "PEFTLORA", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "attention", "name": "attention", "type": "COMBO", "widget": {"name": "attention"}, "link": null}], "outputs": [{"label": "Florence2", "localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "slot_index": 0, "links": [172]}], "properties": {"cnr_id": "comfyui-florence2", "ver": "90b012e922f8bb0482bcd2ae24cdc191ec12a11f", "Node name for S&R": "DownloadAndLoadFlorence2Model"}, "widgets_values": ["gokaygokay/Florence-2-Flux-Large", "fp16", "sdpa"]}, {"id": 119, "type": "easy imageBatchToImageList", "pos": [2487.996337890625, 2281.7685546875], "size": [210, 26], "flags": {}, "order": 36, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 173}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "shape": 6, "type": "IMAGE", "slot_index": 0, "links": [164, 171]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "0daf114fe8870aeacfea484aa59e7f9973b91cd5", "Node name for S&R": "easy imageBatchToImageList"}, "widgets_values": []}, {"id": 139, "type": "ShowText|pysssss", "pos": [2894.638916015625, 2013.8314208984375], "size": [270.9800720214844, 326], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "STRING", "link": 205}], "outputs": [{"label": "字符串", "localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "slot_index": 0, "links": [177]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "bbda5e52ad580c13ceaa53136d9c2bed9137bd2e", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["The image shows a painting of a mysterious creature with horns and a cigarette in its mouth, set against a backdrop of a sky filled with clouds. The creature appears to be in a state of mystery, with its eyes wide open and its mouth slightly open, as if it is about to say something. The painting is detailed and vibrant, with a hint of mystery and intrigue.", "The image shows a painting of a blue sky with a yellow sun in the center, surrounded by white clouds. On the right side of the painting, there is a green plant, and on the left side, there are a few other plants. The painting is oil on canvas.", "The image shows an illustration of a boy holding a broom in front of a large monster. The boy is standing on the right side of the image, while the monster is on the left side. In the middle of the illustration, there is a red object inside the monster's mouth. On the top left corner of the painting, there appears to be a person standing on a rock, and on the bottom left corner there is an object.", "The image shows a painting of a boy looking up at a rainbow in the sky, surrounded by trees on the right and left side of the image. The sky is filled with clouds and a bird is flying in the top left corner.", "An illustration of a man in a blue shirt, red shoes, and a red cape standing in front of a blue and green tree. On the left side of the image, there is a large, dark blue object with yellow and green circles on it, and on the right side, there are white objects on the ground. In the middle of the painting, there appears to be a large pile of dirt, and there are a few plants in the background.", "The image shows a painting of two children playing with a kite in a lush green field. The children are wearing different colored dresses and one of them is holding the kite. To the right of the children is a wooden board attached to a wooden pole. The board has something written on it. In the background, there are many trees, plants, flowers, grass, and mountains. The sky is visible at the top of the painting."]}, {"id": 133, "type": "CLIPTextEncode", "pos": [3199.718505859375, 1306.6927490234375], "size": [354.9254455566406, 98.88697814941406], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 195}, {"label": "文本", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 196}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [197, 320, 362]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["", [false, true]]}, {"id": 221, "type": "ConditioningZeroOut", "pos": [3256.193603515625, 1459.6881103515625], "size": [210, 26], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 320}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 134, "type": "FluxGuidance", "pos": [3549.************, 1440.8134765625], "size": [268.7532043457031, 58], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 197}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [178, 213]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 137, "type": "TTP_Image_Assy", "pos": [3815.************, 1920.8214111328125], "size": [278.79998779296875, 194.11996459960938], "flags": {}, "order": 51, "mode": 0, "inputs": [{"label": "tiles", "localized_name": "tiles", "name": "tiles", "type": "IMAGE", "link": 200}, {"label": "positions", "localized_name": "positions", "name": "positions", "type": "LIST", "link": 201}, {"label": "original_size", "localized_name": "original_size", "name": "original_size", "type": "TUPLE", "link": 202}, {"label": "grid_size", "localized_name": "grid_size", "name": "grid_size", "type": "TUPLE", "link": 203}, {"localized_name": "padding", "name": "padding", "type": "INT", "widget": {"name": "padding"}, "link": null}], "outputs": [{"label": "RECONSTRUCTED_IMAGE", "localized_name": "RECONSTRUCTED_IMAGE", "name": "RECONSTRUCTED_IMAGE", "type": "IMAGE", "slot_index": 0, "links": [803]}], "properties": {"cnr_id": "comfyui_ttp_toolset", "ver": "13bd85fc5ea8c3c6ecf2170d613c2a099c0c0d5b", "Node name for S&R": "TTP_Image_Assy"}, "widgets_values": [128]}, {"id": 113, "type": "easy imageListToImageBatch", "pos": [3806.0546875, 1665.634033203125], "size": [210, 31.88014793395996], "flags": {}, "order": 50, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 161}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [200]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "0daf114fe8870aeacfea484aa59e7f9973b91cd5", "Node name for S&R": "easy imageListToImageBatch"}, "widgets_values": []}, {"id": 572, "type": "PreviewImage", "pos": [4145.84033203125, 1251.47802734375], "size": [913.5515747070312, 796.9557495117188], "flags": {}, "order": 58, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 754}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 114, "type": "Display Any (rgthree)", "pos": [3494.95263671875, 2102.827392578125], "size": [311.528076171875, 229.8948974609375], "flags": {}, "order": 46, "mode": 0, "inputs": [{"dir": 3, "label": "输入", "localized_name": "source", "name": "source", "type": "*", "link": 162}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "32142fe476878a354dda6e2d4b5ea98960de3ced", "Node name for S&R": "Display Any (rgthree)"}, "widgets_values": [""]}, {"id": 123, "type": "TTP_condtobatch", "pos": [4138.830078125, 2099.890625], "size": [258.125732421875, 28.03377342224121], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "conditionings", "localized_name": "conditionings", "name": "conditionings", "type": "CONDITIONING", "link": 180}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [190]}], "properties": {"cnr_id": "comfyui_ttp_toolset", "ver": "13bd85fc5ea8c3c6ecf2170d613c2a099c0c0d5b", "Node name for S&R": "TTP_condtobatch"}, "widgets_values": []}, {"id": 130, "type": "TTP_condsetarea_merge", "pos": [4766.2578125, 2091.341552734375], "size": [262, 78], "flags": {}, "order": 45, "mode": 0, "inputs": [{"label": "conditioning_batch", "localized_name": "conditioning_batch", "name": "conditioning_batch", "type": "CONDITIONING", "link": 190}, {"label": "coordinates", "localized_name": "coordinates", "name": "coordinates", "type": "LIST", "link": 191}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [179]}], "properties": {"cnr_id": "comfyui_ttp_toolset", "ver": "13bd85fc5ea8c3c6ecf2170d613c2a099c0c0d5b", "Node name for S&R": "TTP_condsetarea_merge"}, "widgets_values": [1]}, {"id": 122, "type": "ConditioningConcat", "pos": [4779.158203125, 2210.873046875], "size": [253.60000610351562, 46], "flags": {}, "order": 48, "mode": 0, "inputs": [{"localized_name": "条件到", "name": "conditioning_to", "type": "CONDITIONING", "link": 178}, {"localized_name": "条件从", "name": "conditioning_from", "type": "CONDITIONING", "link": 179}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [212]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "ConditioningConcat"}, "widgets_values": []}, {"id": 146, "type": "TiledDiffusion", "pos": [3838.032958984375, 2165.974365234375], "size": [268.2269287109375, 154], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "模型", "localized_name": "model", "name": "model", "type": "MODEL", "link": 218}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "tile_width", "name": "tile_width", "type": "INT", "widget": {"name": "tile_width"}, "link": null}, {"localized_name": "tile_height", "name": "tile_height", "type": "INT", "widget": {"name": "tile_height"}, "link": null}, {"localized_name": "tile_overlap", "name": "tile_overlap", "type": "INT", "widget": {"name": "tile_overlap"}, "link": null}, {"localized_name": "tile_batch_size", "name": "tile_batch_size", "type": "INT", "widget": {"name": "tile_batch_size"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [211]}], "properties": {"cnr_id": "ComfyUI-TiledDiffusion", "ver": "448e961d8289779eeef9045c1cf7ef438e3a2a7b", "Node name for S&R": "TiledDiffusion"}, "widgets_values": ["Mixture of Diffusers", 1024, 1024, 96, 4]}, {"id": 125, "type": "InjectLatentNoise+", "pos": [5044.16650390625, 2107.48779296875], "size": [265.5225524902344, 150], "flags": {}, "order": 54, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "latent", "name": "latent", "type": "LATENT", "link": 182}, {"localized_name": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "noise_strength", "name": "noise_strength", "type": "FLOAT", "widget": {"name": "noise_strength"}, "link": null}, {"label": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [214]}], "properties": {"cnr_id": "comfyui_essentials", "ver": "33ff89fd354d8ec3ab6affb605a79a931b445d99", "Node name for S&R": "InjectLatentNoise+"}, "widgets_values": [0, "fixed", 0.1]}, {"id": 144, "type": "VAEDecodeTiled", "pos": [5322.74560546875, 2109.5322265625], "size": [300, 150], "flags": {}, "order": 56, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 215}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 216}, {"localized_name": "分块尺寸", "name": "tile_size", "type": "INT", "widget": {"name": "tile_size"}, "link": null}, {"localized_name": "重叠", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "时间尺寸", "name": "temporal_size", "type": "INT", "widget": {"name": "temporal_size"}, "link": null}, {"localized_name": "时间重叠", "name": "temporal_overlap", "type": "INT", "widget": {"name": "temporal_overlap"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [315, 754]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [1024, 64, 64, 8]}, {"id": 143, "type": "KSamplerAdvanced //Inspire", "pos": [5106.3359375, 1360.3807373046875], "size": [300.87152099609375, 706], "flags": {}, "order": 55, "mode": 0, "inputs": [{"label": "模型", "localized_name": "model", "name": "model", "type": "MODEL", "link": 211}, {"label": "正面条件", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 212}, {"label": "负面条件", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 213}, {"label": "Latent", "localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 214}, {"localized_name": "noise_opt", "name": "noise_opt", "shape": 7, "type": "NOISE_IMAGE", "link": null}, {"localized_name": "scheduler_func_opt", "name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC", "link": null}, {"localized_name": "add_noise", "name": "add_noise", "type": "BOOLEAN", "widget": {"name": "add_noise"}, "link": null}, {"localized_name": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "start_at_step", "name": "start_at_step", "type": "INT", "widget": {"name": "start_at_step"}, "link": null}, {"localized_name": "end_at_step", "name": "end_at_step", "type": "INT", "widget": {"name": "end_at_step"}, "link": null}, {"localized_name": "noise_mode", "name": "noise_mode", "type": "COMBO", "widget": {"name": "noise_mode"}, "link": null}, {"localized_name": "return_with_leftover_noise", "name": "return_with_leftover_noise", "type": "BOOLEAN", "widget": {"name": "return_with_leftover_noise"}, "link": null}, {"localized_name": "batch_seed_mode", "name": "batch_seed_mode", "type": "COMBO", "widget": {"name": "batch_seed_mode"}, "link": null}, {"localized_name": "variation_seed", "name": "variation_seed", "type": "INT", "widget": {"name": "variation_seed"}, "link": null}, {"localized_name": "variation_strength", "name": "variation_strength", "type": "FLOAT", "widget": {"name": "variation_strength"}, "link": null}, {"localized_name": "variation_method", "name": "variation_method", "shape": 7, "type": "COMBO", "widget": {"name": "variation_method"}, "link": null}, {"localized_name": "internal_seed", "name": "internal_seed", "shape": 7, "type": "INT", "widget": {"name": "internal_seed"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [215]}], "properties": {"cnr_id": "comfyui-inspire-pack", "ver": "ecf8ab6f588bc8442ff7720b383d085ecabbc407", "Node name for S&R": "KSamplerAdvanced //Inspire"}, "widgets_values": [true, 833195543994351, "fixed", 20, 1, "euler", "simple", 18, 10000, "GPU(=A1111)", false, "incremental", 0, 0, "linear", 0]}, {"id": 245, "type": "SaveImage", "pos": [6929.630859375, 1274.49560546875], "size": [374.841552734375, 905.5076904296875], "flags": {}, "order": 59, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 360}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 254, "type": "ConditioningZeroOut", "pos": [6140.91748046875, 2122.************], "size": [210, 26], "flags": {}, "order": 34, "mode": 4, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 370}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [371]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 248, "type": "FluxGuidance", "pos": [6152.412109375, 2005.007568359375], "size": [268.7532043457031, 58], "flags": {}, "order": 32, "mode": 4, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 363}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [364, 370]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 247, "type": "StyleModelApply", "pos": [6150.81640625, 1839.************], "size": [315, 122], "flags": {}, "order": 30, "mode": 4, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 362}, {"label": "风格模型", "localized_name": "风格模型", "name": "style_model", "type": "STYLE_MODEL", "link": 365}, {"label": "CLIP视觉输出", "localized_name": "clip视觉输出", "name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 366}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "强度类型", "name": "strength_type", "type": "COMBO", "widget": {"name": "strength_type"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [363]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 124, "type": "TTP_CoordinateSplitter", "pos": [4133.55810546875, 2169.5732421875], "size": [267, 26], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "Positions", "localized_name": "Positions", "name": "Positions", "type": "LIST", "link": 181}], "outputs": [{"label": "Coordinates", "localized_name": "Coordinates", "name": "Coordinates", "type": "LIST", "slot_index": 0, "links": [191]}], "properties": {"cnr_id": "comfyui_ttp_toolset", "ver": "13bd85fc5ea8c3c6ecf2170d613c2a099c0c0d5b", "Node name for S&R": "TTP_CoordinateSplitter"}, "widgets_values": []}, {"id": 249, "type": "StyleModelLoader", "pos": [6146.01904296875, 1287.1944580078125], "size": [315, 58], "flags": {}, "order": 12, "mode": 4, "inputs": [{"localized_name": "风格模型名称", "name": "style_model_name", "type": "COMBO", "widget": {"name": "style_model_name"}, "link": null}], "outputs": [{"label": "风格模型", "localized_name": "风格模型", "name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [365]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 252, "type": "LoadImage", "pos": [5977.68359375, 1597.41552734375], "size": [315, 314], "flags": {}, "order": 13, "mode": 4, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [368]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_03677_.png", "image"]}, {"id": 251, "type": "CLIPVisionLoader", "pos": [5838.7783203125, 1447.4613037109375], "size": [315, 58], "flags": {}, "order": 14, "mode": 4, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"label": "CLIP视觉", "localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [367]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 220, "type": "FaceDetailer", "pos": [6505.6435546875, 1303.7657470703125], "size": [397.70782470703125, 960], "flags": {}, "order": 57, "mode": 4, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 315}, {"label": "模型", "localized_name": "model", "name": "model", "type": "MODEL", "link": 316}, {"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 317}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 318}, {"label": "正面条件", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 364}, {"label": "负面条件", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 371}, {"label": "BBox检测", "localized_name": "bbox_detector", "name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 322}, {"label": "SAM模型", "localized_name": "sam_model_opt", "name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL", "link": 323}, {"label": "Segm检测", "localized_name": "segm_detector_opt", "name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR", "link": null}, {"label": "细化约束", "localized_name": "detailer_hook", "name": "detailer_hook", "shape": 7, "type": "DETAILER_HOOK", "link": null}, {"localized_name": "scheduler_func_opt", "name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC", "link": null}, {"localized_name": "guide_size", "name": "guide_size", "type": "FLOAT", "widget": {"name": "guide_size"}, "link": null}, {"localized_name": "guide_size_for", "name": "guide_size_for", "type": "BOOLEAN", "widget": {"name": "guide_size_for"}, "link": null}, {"localized_name": "max_size", "name": "max_size", "type": "FLOAT", "widget": {"name": "max_size"}, "link": null}, {"label": "随机种", "localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 325}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}, {"localized_name": "feather", "name": "feather", "type": "INT", "widget": {"name": "feather"}, "link": null}, {"localized_name": "noise_mask", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}, {"localized_name": "force_inpaint", "name": "force_inpaint", "type": "BOOLEAN", "widget": {"name": "force_inpaint"}, "link": null}, {"localized_name": "bbox_threshold", "name": "bbox_threshold", "type": "FLOAT", "widget": {"name": "bbox_threshold"}, "link": null}, {"localized_name": "bbox_dilation", "name": "bbox_dilation", "type": "INT", "widget": {"name": "bbox_dilation"}, "link": null}, {"localized_name": "bbox_crop_factor", "name": "bbox_crop_factor", "type": "FLOAT", "widget": {"name": "bbox_crop_factor"}, "link": null}, {"localized_name": "sam_detection_hint", "name": "sam_detection_hint", "type": "COMBO", "widget": {"name": "sam_detection_hint"}, "link": null}, {"localized_name": "sam_dilation", "name": "sam_dilation", "type": "INT", "widget": {"name": "sam_dilation"}, "link": null}, {"localized_name": "sam_threshold", "name": "sam_threshold", "type": "FLOAT", "widget": {"name": "sam_threshold"}, "link": null}, {"localized_name": "sam_bbox_expansion", "name": "sam_bbox_expansion", "type": "INT", "widget": {"name": "sam_bbox_expansion"}, "link": null}, {"localized_name": "sam_mask_hint_threshold", "name": "sam_mask_hint_threshold", "type": "FLOAT", "widget": {"name": "sam_mask_hint_threshold"}, "link": null}, {"localized_name": "sam_mask_hint_use_negative", "name": "sam_mask_hint_use_negative", "type": "COMBO", "widget": {"name": "sam_mask_hint_use_negative"}, "link": null}, {"localized_name": "drop_size", "name": "drop_size", "type": "INT", "widget": {"name": "drop_size"}, "link": null}, {"localized_name": "wildcard", "name": "wildcard", "type": "STRING", "widget": {"name": "wildcard"}, "link": null}, {"localized_name": "cycle", "name": "cycle", "type": "INT", "widget": {"name": "cycle"}, "link": null}, {"localized_name": "inpaint_model", "name": "inpaint_model", "shape": 7, "type": "BOOLEAN", "widget": {"name": "inpaint_model"}, "link": null}, {"localized_name": "noise_mask_feather", "name": "noise_mask_feather", "shape": 7, "type": "INT", "widget": {"name": "noise_mask_feather"}, "link": null}, {"localized_name": "tiled_encode", "name": "tiled_encode", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_encode"}, "link": null}, {"localized_name": "tiled_decode", "name": "tiled_decode", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_decode"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [360]}, {"label": "细化图像", "localized_name": "cropped_refined", "name": "cropped_refined", "shape": 6, "type": "IMAGE", "links": null}, {"label": "细化部分", "localized_name": "cropped_enhanced_alpha", "name": "cropped_enhanced_alpha", "shape": 6, "type": "IMAGE", "links": null}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"label": "细化节点束", "localized_name": "detailer_pipe", "name": "detailer_pipe", "type": "DETAILER_PIPE", "links": null}, {"label": "ControlNet图像", "localized_name": "cnet_images", "name": "cnet_images", "shape": 6, "type": "IMAGE", "links": null}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "808b0dedf03534a2594ecb60a9d6305a044efdc2", "Node name for S&R": "FaceDetailer"}, "widgets_values": [512, true, 1024, 675565369708427, "randomize", 20, 1, "euler", "normal", 0.7000000000000001, 5, true, true, 0.5, 20, 3, "center-1", 0, 0.93, 0, 0.7, "False", 10, "", 1, false, 20, [false, true], false, [false, true]]}, {"id": 225, "type": "PrimitiveNode", "pos": [6234.86474609375, 1510.8677978515625], "size": [210, 82], "flags": {"collapsed": false}, "order": 15, "mode": 4, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "seed"}, "slot_index": 0, "links": [325]}], "properties": {"Run widget replace on values": false}, "widgets_values": [675565369708427, "fixed"]}, {"id": 120, "type": "VAEEncode", "pos": [4473.68017578125, 2133.201171875], "size": [210, 46], "flags": {}, "order": 53, "mode": 0, "inputs": [{"label": "图像", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 804}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 175}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [182]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 573, "type": "Sharpen (mtb)", "pos": [4474.802734375, 2256.3466796875], "size": [270, 130], "flags": {}, "order": 52, "mode": 4, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 803}, {"localized_name": "sharpen_radius", "name": "sharpen_radius", "type": "INT", "widget": {"name": "sharpen_radius"}, "link": null}, {"localized_name": "sigma_x", "name": "sigma_x", "type": "FLOAT", "widget": {"name": "sigma_x"}, "link": null}, {"localized_name": "sigma_y", "name": "sigma_y", "type": "FLOAT", "widget": {"name": "sigma_y"}, "link": null}, {"localized_name": "alpha", "name": "alpha", "type": "FLOAT", "widget": {"name": "alpha"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [804]}], "properties": {"cnr_id": "comfy-mtb", "ver": "0.4.0", "Node name for S&R": "Sharpen (mtb)"}, "widgets_values": [4, 0.8, 3, 0.5]}], "links": [[160, 121, 0, 112, 0, "CONDITIONING"], [161, 136, 0, 113, 0, "IMAGE"], [162, 129, 0, 114, 0, "*"], [163, 131, 0, 115, 0, "MODEL"], [164, 119, 0, 116, 0, "IMAGE"], [165, 153, 0, 116, 1, "VAE"], [166, 111, 0, 117, 0, "NOISE"], [167, 129, 0, 117, 1, "GUIDER"], [168, 110, 0, 117, 2, "SAMPLER"], [169, 115, 0, 117, 3, "SIGMAS"], [170, 116, 0, 117, 4, "LATENT"], [171, 119, 0, 118, 0, "IMAGE"], [172, 150, 0, 118, 1, "FL2MODEL"], [173, 128, 0, 119, 0, "IMAGE"], [175, 153, 0, 120, 1, "VAE"], [176, 131, 1, 121, 0, "CLIP"], [177, 139, 0, 121, 1, "STRING"], [178, 134, 0, 122, 0, "CONDITIONING"], [179, 130, 0, 122, 1, "CONDITIONING"], [180, 121, 0, 123, 0, "CONDITIONING"], [181, 128, 1, 124, 0, "LIST"], [182, 120, 0, 125, 0, "LATENT"], [183, 142, 0, 126, 0, "*"], [184, 126, 0, 127, 0, "IMAGE"], [185, 126, 0, 128, 0, "IMAGE"], [186, 127, 0, 128, 1, "INT"], [187, 127, 1, 128, 2, "INT"], [188, 131, 0, 129, 0, "MODEL"], [189, 112, 0, 129, 1, "CONDITIONING"], [190, 123, 0, 130, 0, "CONDITIONING"], [191, 124, 0, 130, 1, "LIST"], [195, 131, 1, 133, 0, "CLIP"], [196, 135, 0, 133, 1, "STRING"], [197, 133, 0, 134, 0, "CONDITIONING"], [198, 117, 0, 136, 0, "LATENT"], [199, 153, 0, 136, 1, "VAE"], [200, 113, 0, 137, 0, "IMAGE"], [201, 128, 1, 137, 1, "LIST"], [202, 128, 2, 137, 2, "TUPLE"], [203, 128, 3, 137, 3, "TUPLE"], [204, 145, 0, 138, 0, "*"], [205, 118, 2, 139, 0, "STRING"], [206, 148, 0, 140, 0, "INT,FLOAT,IMAGE,LATENT"], [207, 154, 0, 141, 0, "UPSCALE_MODEL"], [208, 138, 0, 141, 1, "IMAGE"], [209, 141, 0, 142, 0, "IMAGE"], [210, 140, 1, 142, 2, "FLOAT"], [211, 146, 0, 143, 0, "MODEL"], [212, 122, 0, 143, 1, "CONDITIONING"], [213, 134, 0, 143, 2, "CONDITIONING"], [214, 125, 0, 143, 3, "LATENT"], [215, 143, 0, 144, 0, "LATENT"], [216, 153, 0, 144, 1, "VAE"], [217, 147, 0, 145, 0, "IMAGE"], [218, 131, 0, 146, 0, "MODEL"], [238, 164, 0, 131, 0, "MODEL"], [239, 164, 1, 131, 1, "CLIP"], [240, 151, 0, 165, 0, "MODEL"], [241, 152, 0, 165, 1, "CLIP"], [242, 165, 0, 164, 0, "MODEL"], [243, 165, 1, 164, 1, "CLIP"], [315, 144, 0, 220, 0, "IMAGE"], [316, 131, 0, 220, 1, "MODEL"], [317, 131, 1, 220, 2, "CLIP"], [318, 153, 0, 220, 3, "VAE"], [320, 133, 0, 221, 0, "CONDITIONING"], [322, 223, 0, 220, 6, "BBOX_DETECTOR"], [323, 222, 0, 220, 7, "SAM_MODEL"], [325, 225, 0, 220, 14, "INT"], [360, 220, 0, 245, 0, "IMAGE"], [362, 133, 0, 247, 0, "CONDITIONING"], [363, 247, 0, 248, 0, "CONDITIONING"], [364, 248, 0, 220, 4, "CONDITIONING"], [365, 249, 0, 247, 1, "STYLE_MODEL"], [366, 250, 0, 247, 2, "CLIP_VISION_OUTPUT"], [367, 251, 0, 250, 0, "CLIP_VISION"], [368, 252, 0, 250, 1, "IMAGE"], [370, 248, 0, 254, 0, "CONDITIONING"], [371, 254, 0, 220, 5, "CONDITIONING"], [754, 144, 0, 572, 0, "IMAGE"], [803, 137, 0, 573, 0, "IMAGE"], [804, 573, 0, 120, 0, "IMAGE"]], "groups": [{"id": 20, "title": "TTP放大", "bounding": [937.2499389648438, 999.2453002929688, 6567.9990234375, 1448.7296142578125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 23, "title": "Group", "bounding": [5671.7666015625, 1220.6802978515625, 1741.9422607421875, 1026.0572509765625], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.41772481694157143, "offset": [-4138.592212730537, -1963.634097958558]}, "ue_links": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}