using System;
using System.Collections.Generic;
using System.Text.Json;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace SaveDataService.Manage
{
    /// <summary>
    /// ComfyUI详细日志管理器
    /// </summary>
    public class ComfyUILogger
    {
        private static ComfyUILogger? _instance;
        public static ComfyUILogger Instance => _instance ??= new ComfyUILogger();

        private ComfyUILogger() { }

        /// <summary>
        /// 记录工作流开始执行
        /// </summary>
        public void LogWorkflowStart(string taskId, string workflowJson, string serverId)
        {
            Console.WriteLine("=".PadRight(80, '='));
            Console.WriteLine($"🚀 ComfyUI工作流开始执行");
            Console.WriteLine($"📋 任务ID: {taskId}");
            Console.WriteLine($"🖥️  服务器ID: {serverId}");
            Console.WriteLine($"⏰ 开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine("=".PadRight(80, '='));

            // 解析并显示工作流概览
            try
            {
                var workflow = JsonConvert.DeserializeObject<JObject>(workflowJson);
                if (workflow != null)
                {
                    Console.WriteLine($"📊 工作流概览:");
                    var nodeCount = 0;
                    foreach (var node in workflow)
                    {
                        nodeCount++;
                        var nodeData = node.Value as JObject;
                        var classType = nodeData?["class_type"]?.ToString() ?? "Unknown";
                        Console.WriteLine($"   节点 {node.Key}: {classType}");
                    }
                    Console.WriteLine($"📈 总节点数: {nodeCount}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 解析工作流失败: {ex.Message}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 记录工作流提交结果
        /// </summary>
        public void LogWorkflowSubmission(string taskId, bool success, string response)
        {
            Console.WriteLine($"📤 工作流提交结果 [任务: {taskId}]");
            if (success)
            {
                Console.WriteLine($"✅ 提交成功");
                try
                {
                    var responseObj = JsonConvert.DeserializeObject<JObject>(response);
                    if (responseObj?["prompt_id"] != null)
                    {
                        var promptId = responseObj["prompt_id"].ToString();
                        Console.WriteLine($"🆔 Prompt ID: {promptId}");
                    }
                    if (responseObj?["number"] != null)
                    {
                        var queueNumber = responseObj["number"].ToString();
                        Console.WriteLine($"🔢 队列位置: {queueNumber}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️  解析响应失败: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine($"❌ 提交失败");
                Console.WriteLine($"📄 错误信息: {response}");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 记录节点开始执行
        /// </summary>
        public void LogNodeStart(string taskId, string nodeId, string nodeName, string nodeType, JObject inputs)
        {
            Console.WriteLine($"🔄 节点开始执行 [任务: {taskId}]");
            Console.WriteLine($"   🏷️  节点ID: {nodeId}");
            Console.WriteLine($"   📝 节点名称: {nodeName}");
            Console.WriteLine($"   🔧 节点类型: {nodeType}");
            Console.WriteLine($"   ⏰ 开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            
            if (inputs != null && inputs.Count > 0)
            {
                Console.WriteLine($"   📥 输入参数:");
                foreach (var input in inputs)
                {
                    var value = input.Value;
                    if (value is JArray array && array.Count == 2)
                    {
                        // 这是一个连接引用 [nodeId, outputIndex]
                        Console.WriteLine($"      {input.Key}: 连接到节点 {array[0]} 的输出 {array[1]}");
                    }
                    else
                    {
                        // 这是一个直接值
                        var valueStr = value?.ToString();
                        if (valueStr != null && valueStr.Length > 100)
                        {
                            valueStr = valueStr.Substring(0, 100) + "...";
                        }
                        Console.WriteLine($"      {input.Key}: {valueStr}");
                    }
                }
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 记录节点执行进度
        /// </summary>
        public void LogNodeProgress(string taskId, string nodeId, string nodeName, int progress, string status)
        {
            var progressBar = GenerateProgressBar(progress);
            Console.WriteLine($"⏳ 节点执行中 [任务: {taskId}] [节点: {nodeId}]");
            Console.WriteLine($"   📝 {nodeName}");
            Console.WriteLine($"   📊 进度: {progressBar} {progress}%");
            Console.WriteLine($"   📋 状态: {status}");
            Console.WriteLine($"   ⏰ 时间: {DateTime.Now:HH:mm:ss.fff}");
            Console.WriteLine();
        }

        /// <summary>
        /// 记录节点完成
        /// </summary>
        public void LogNodeComplete(string taskId, string nodeId, string nodeName, JObject outputs, TimeSpan duration)
        {
            Console.WriteLine($"✅ 节点执行完成 [任务: {taskId}]");
            Console.WriteLine($"   🏷️  节点ID: {nodeId}");
            Console.WriteLine($"   📝 节点名称: {nodeName}");
            Console.WriteLine($"   ⏱️  执行时间: {duration.TotalSeconds:F2}秒");
            Console.WriteLine($"   ⏰ 完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            
            if (outputs != null && outputs.Count > 0)
            {
                Console.WriteLine($"   📤 输出结果:");
                foreach (var output in outputs)
                {
                    var value = output.Value;
                    if (value is JArray array)
                    {
                        Console.WriteLine($"      {output.Key}: 数组 (长度: {array.Count})");
                        for (int i = 0; i < Math.Min(array.Count, 3); i++)
                        {
                            var item = array[i];
                            if (item is JObject obj && obj["filename"] != null)
                            {
                                Console.WriteLine($"         [{i}] 文件: {obj["filename"]}");
                            }
                            else
                            {
                                var itemStr = item?.ToString();
                                if (itemStr != null && itemStr.Length > 50)
                                {
                                    itemStr = itemStr.Substring(0, 50) + "...";
                                }
                                Console.WriteLine($"         [{i}] {itemStr}");
                            }
                        }
                        if (array.Count > 3)
                        {
                            Console.WriteLine($"         ... 还有 {array.Count - 3} 个项目");
                        }
                    }
                    else
                    {
                        var valueStr = value?.ToString();
                        if (valueStr != null && valueStr.Length > 100)
                        {
                            valueStr = valueStr.Substring(0, 100) + "...";
                        }
                        Console.WriteLine($"      {output.Key}: {valueStr}");
                    }
                }
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 记录节点错误
        /// </summary>
        public void LogNodeError(string taskId, string nodeId, string nodeName, string error)
        {
            Console.WriteLine($"❌ 节点执行失败 [任务: {taskId}]");
            Console.WriteLine($"   🏷️  节点ID: {nodeId}");
            Console.WriteLine($"   📝 节点名称: {nodeName}");
            Console.WriteLine($"   ⏰ 错误时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            Console.WriteLine($"   🚨 错误信息: {error}");
            Console.WriteLine();
        }

        /// <summary>
        /// 记录工作流进度
        /// </summary>
        public void LogWorkflowProgress(string taskId, int completedNodes, int totalNodes, int progress)
        {
            var progressBar = GenerateProgressBar(progress);
            Console.WriteLine($"📈 工作流执行进度 [任务: {taskId}]");
            Console.WriteLine($"   📊 进度: {progressBar} {progress}%");
            Console.WriteLine($"   🔢 节点进度: {completedNodes}/{totalNodes}");
            Console.WriteLine($"   ⏰ 时间: {DateTime.Now:HH:mm:ss.fff}");
            Console.WriteLine();
        }

        /// <summary>
        /// 记录工作流完成
        /// </summary>
        public void LogWorkflowComplete(string taskId, bool success, TimeSpan totalDuration, List<string> outputFiles)
        {
            Console.WriteLine("=".PadRight(80, '='));
            if (success)
            {
                Console.WriteLine($"🎉 ComfyUI工作流执行完成");
            }
            else
            {
                Console.WriteLine($"💥 ComfyUI工作流执行失败");
            }
            Console.WriteLine($"📋 任务ID: {taskId}");
            Console.WriteLine($"⏱️  总执行时间: {totalDuration.TotalSeconds:F2}秒");
            Console.WriteLine($"⏰ 完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            
            if (outputFiles != null && outputFiles.Count > 0)
            {
                Console.WriteLine($"📁 输出文件 ({outputFiles.Count}个):");
                foreach (var file in outputFiles)
                {
                    Console.WriteLine($"   📄 {file}");
                }
            }
            Console.WriteLine("=".PadRight(80, '='));
            Console.WriteLine();
        }

        /// <summary>
        /// 记录队列状态
        /// </summary>
        public void LogQueueStatus(string taskId, int queuePosition, int totalInQueue)
        {
            Console.WriteLine($"📋 队列状态 [任务: {taskId}]");
            Console.WriteLine($"   🔢 当前位置: {queuePosition}");
            Console.WriteLine($"   📊 队列总数: {totalInQueue}");
            Console.WriteLine($"   ⏰ 查询时间: {DateTime.Now:HH:mm:ss}");
            Console.WriteLine();
        }

        /// <summary>
        /// 记录历史查询
        /// </summary>
        public void LogHistoryQuery(string promptId, JObject historyData)
        {
            Console.WriteLine($"📚 历史查询 [Prompt: {promptId}]");
            Console.WriteLine($"   ⏰ 查询时间: {DateTime.Now:HH:mm:ss}");
            
            if (historyData != null)
            {
                var status = historyData["status"]?.ToString();
                if (!string.IsNullOrEmpty(status))
                {
                    Console.WriteLine($"   📋 状态: {status}");
                }

                var outputs = historyData["outputs"] as JObject;
                if (outputs != null)
                {
                    Console.WriteLine($"   📤 输出节点数: {outputs.Count}");
                    foreach (var output in outputs)
                    {
                        Console.WriteLine($"      节点 {output.Key}: 有输出数据");
                    }
                }
            }
            Console.WriteLine();
        }

        /// <summary>
        /// 生成进度条
        /// </summary>
        private string GenerateProgressBar(int progress, int width = 20)
        {
            var filled = (int)(progress / 100.0 * width);
            var empty = width - filled;
            return "[" + "█".PadRight(filled, '█') + "░".PadRight(empty, '░') + "]";
        }

        /// <summary>
        /// 记录通用信息
        /// </summary>
        public void LogInfo(string message)
        {
            Console.WriteLine($"ℹ️  {DateTime.Now:HH:mm:ss} - {message}");
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        public void LogWarning(string message)
        {
            Console.WriteLine($"⚠️  {DateTime.Now:HH:mm:ss} - {message}");
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        public void LogError(string message)
        {
            Console.WriteLine($"❌ {DateTime.Now:HH:mm:ss} - {message}");
        }

        /// <summary>
        /// 记录文件下载开始
        /// </summary>
        public void LogFileDownloadStart(string taskId, string filename, string fileType, string downloadUrl)
        {
            Console.WriteLine($"📥 开始下载文件 [任务: {taskId}]");
            Console.WriteLine($"   📄 文件名: {filename}");
            Console.WriteLine($"   🏷️  文件类型: {fileType}");
            Console.WriteLine($"   🔗 下载URL: {downloadUrl}");
            Console.WriteLine($"   ⏰ 开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            Console.WriteLine();
        }

        /// <summary>
        /// 记录文件下载成功
        /// </summary>
        public void LogFileDownloadSuccess(string taskId, string filename, string localPath, double fileSizeMB, TimeSpan duration)
        {
            Console.WriteLine($"✅ 文件下载成功 [任务: {taskId}]");
            Console.WriteLine($"   📄 文件名: {filename}");
            Console.WriteLine($"   📁 本地路径: {localPath}");
            Console.WriteLine($"   📊 文件大小: {fileSizeMB:F2} MB");
            Console.WriteLine($"   ⏱️  下载时间: {duration.TotalSeconds:F2}秒");
            Console.WriteLine($"   📈 下载速度: {(fileSizeMB / duration.TotalSeconds):F2} MB/s");
            Console.WriteLine($"   ⏰ 完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            Console.WriteLine();
        }

        /// <summary>
        /// 记录文件下载失败
        /// </summary>
        public void LogFileDownloadError(string taskId, string filename, string error)
        {
            Console.WriteLine($"❌ 文件下载失败 [任务: {taskId}]");
            Console.WriteLine($"   📄 文件名: {filename}");
            Console.WriteLine($"   🚨 错误信息: {error}");
            Console.WriteLine($"   ⏰ 失败时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
            Console.WriteLine();
        }

        /// <summary>
        /// 记录批量文件下载完成
        /// </summary>
        public void LogBatchDownloadComplete(string taskId, int totalFiles, int successCount, int failedCount, List<string> downloadedPaths)
        {
            Console.WriteLine($"📦 批量文件下载完成 [任务: {taskId}]");
            Console.WriteLine($"   📊 总文件数: {totalFiles}");
            Console.WriteLine($"   ✅ 成功下载: {successCount}");
            Console.WriteLine($"   ❌ 下载失败: {failedCount}");
            Console.WriteLine($"   📈 成功率: {(successCount * 100.0 / totalFiles):F1}%");

            if (downloadedPaths.Count > 0)
            {
                Console.WriteLine($"   📁 下载的文件:");
                foreach (var path in downloadedPaths)
                {
                    var filename = System.IO.Path.GetFileName(path);
                    var fileSize = new System.IO.FileInfo(path).Length / (1024.0 * 1024.0);
                    Console.WriteLine($"      📄 {filename} ({fileSize:F2} MB)");
                }
            }
            Console.WriteLine($"   ⏰ 完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();
        }
    }
}
