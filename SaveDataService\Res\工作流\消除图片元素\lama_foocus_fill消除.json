{"id": "18f8a6d2-ca15-4f90-ba87-d11b56b1a7df", "revision": 0, "last_node_id": 337, "last_link_id": 486, "nodes": [{"id": 297, "type": "CLIPTextEncodeSDXL", "pos": [-8478.8935546875, -396.4783020019531], "size": [218.59402465820312, 286], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 419}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "裁剪宽", "name": "crop_w", "type": "INT", "widget": {"name": "crop_w"}, "link": null}, {"localized_name": "裁剪高", "name": "crop_h", "type": "INT", "widget": {"name": "crop_h"}, "link": null}, {"localized_name": "目标宽度", "name": "target_width", "type": "INT", "widget": {"name": "target_width"}, "link": null}, {"localized_name": "目标高度", "name": "target_height", "type": "INT", "widget": {"name": "target_height"}, "link": null}, {"localized_name": "文本_g", "name": "text_g", "type": "STRING", "widget": {"name": "text_g"}, "link": 415}, {"localized_name": "文本_l", "name": "text_l", "type": "STRING", "widget": {"name": "text_l"}, "link": 416}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [413]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncodeSDXL"}, "widgets_values": [1024, 1024, 0, 0, 1024, 1024, "", ""]}, {"id": 298, "type": "CLIPTextEncodeSDXL", "pos": [-8491.5322265625, -315.138427734375], "size": [218.59402465820312, 286], "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 420}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "裁剪宽", "name": "crop_w", "type": "INT", "widget": {"name": "crop_w"}, "link": null}, {"localized_name": "裁剪高", "name": "crop_h", "type": "INT", "widget": {"name": "crop_h"}, "link": null}, {"localized_name": "目标宽度", "name": "target_width", "type": "INT", "widget": {"name": "target_width"}, "link": null}, {"localized_name": "目标高度", "name": "target_height", "type": "INT", "widget": {"name": "target_height"}, "link": null}, {"localized_name": "文本_g", "name": "text_g", "type": "STRING", "widget": {"name": "text_g"}, "link": 417}, {"localized_name": "文本_l", "name": "text_l", "type": "STRING", "widget": {"name": "text_l"}, "link": 418}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [414]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncodeSDXL"}, "widgets_values": [1024, 1024, 0, 0, 1024, 1024, "", ""]}, {"id": 296, "type": "INPAINT_VAEEncodeInpaintConditioning", "pos": [-8552.32421875, -235.45748901367188], "size": [259.1216735839844, 106], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 413}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 414}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 421}, {"localized_name": "pixels", "name": "pixels", "type": "IMAGE", "link": 424}, {"localized_name": "mask", "name": "mask", "type": "MASK", "link": 423}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "links": [427]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "links": [428]}, {"localized_name": "latent_inpaint", "name": "latent_inpaint", "type": "LATENT", "links": [434]}, {"localized_name": "latent_samples", "name": "latent_samples", "type": "LATENT", "links": [433]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_VAEEncodeInpaintConditioning"}, "widgets_values": []}, {"id": 311, "type": "CLIPTextEncode", "pos": [-7054.71044921875, -565.239990234375], "size": [400, 200], "flags": {"collapsed": false}, "order": 37, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 446}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 470}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [448]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 314, "type": "DualCLIPLoader", "pos": [-7473.15673828125, -449.3168029785156], "size": [315, 130], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [446, 447]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 317, "type": "VAEDecode", "pos": [-6199.41455078125, -503.4729919433594], "size": [210, 46], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 449}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 450}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [438]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 324, "type": "Mask Gaussian Region", "pos": [-6934.84423828125, -729.1620483398438], "size": [270, 58], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "masks", "name": "masks", "type": "MASK", "link": 464}, {"localized_name": "radius", "name": "radius", "type": "FLOAT", "widget": {"name": "radius"}, "link": null}], "outputs": [{"localized_name": "MASKS", "name": "MASKS", "type": "MASK", "links": [465]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Mask Gaussian Region"}, "widgets_values": [5]}, {"id": 304, "type": "VAEDecode", "pos": [-8119.67822265625, -411.1690368652344], "size": [140, 46], "flags": {}, "order": 31, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 431}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 472}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [436, 466]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 319, "type": "InpaintModelConditioning", "pos": [-6215.27099609375, -384.95831298828125], "size": [270, 138], "flags": {}, "order": 39, "mode": 0, "inputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 451}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 452}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 471}, {"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 474}, {"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 465}, {"localized_name": "噪波遮罩", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}], "outputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "links": [453]}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "links": [454]}, {"localized_name": "Latent", "name": "latent", "type": "LATENT", "links": [455]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 312, "type": "CLIPTextEncode", "pos": [-7038.76806640625, -288.07843017578125], "size": [400, 200], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 447}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [452]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 318, "type": "VAELoader", "pos": [-6589.09130859375, -687.7379760742188], "size": [270, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [450, 471]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 301, "type": "VAELoader", "pos": [-9397.09765625, -569.14892578125], "size": [270, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [421, 472]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["sdxl_vae.safetensors"]}, {"id": 295, "type": "CheckpointLoaderSimple", "pos": [-9381.49609375, -445.2140197753906], "size": [270, 98], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [425]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [419, 420]}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["juggernautXL_ragnarokBy.safetensors"]}, {"id": 309, "type": "BasicScheduler", "pos": [-6590.04345703125, -346.3194274902344], "size": [315, 106], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 439}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"label": "降噪", "localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Sigmas", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [444]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 0.8500000000000002]}, {"id": 306, "type": "InpaintStitchImproved", "pos": [-8517.640625, 110.97299194335938], "size": [215.52206420898438, 46], "flags": {}, "order": 32, "mode": 0, "inputs": [{"localized_name": "stitcher", "name": "stitcher", "type": "STITCHER", "link": 435}, {"localized_name": "inpainted_image", "name": "inpainted_image", "type": "IMAGE", "link": 436}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [437, 474]}], "properties": {"cnr_id": "comfyui-inpaint-cropand<PERSON>itch", "ver": "2.1.7", "Node name for S&R": "InpaintStitchImproved"}, "widgets_values": []}, {"id": 302, "type": "easy applyFooocusInpaint", "pos": [-8554.6630859375, -58.92759323120117], "size": [270, 102], "flags": {}, "order": 28, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 425}, {"localized_name": "LATENT", "name": "latent", "type": "LATENT", "link": 434}, {"localized_name": "head", "name": "head", "type": "COMBO", "widget": {"name": "head"}, "link": null}, {"localized_name": "patch", "name": "patch", "type": "COMBO", "widget": {"name": "patch"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "links": [475]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy applyFooocusInpaint"}, "widgets_values": ["fooocus_inpaint_head", "inpaint_v26 (1.32GB)"]}, {"id": 332, "type": "DifferentialDiffusion", "pos": [-6245.66748046875, -620.97998046875], "size": [216.77987670898438, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 478}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [477]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 308, "type": "UNETLoader", "pos": [-6611.71923828125, -187.6813201904297], "size": [315, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [439, 478]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "UNETLoader"}, "widgets_values": ["F.1-Fill-fp16_Inpaint&Outpaint_1.0.safetensors", "fp8_e4m3fn"]}, {"id": 331, "type": "DifferentialDiffusion", "pos": [-8436.6005859375, -509.6649169921875], "size": [216.77987670898438, 26], "flags": {}, "order": 29, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 475}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [476]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 321, "type": "GrowMask", "pos": [-9375.27734375, -236.64776611328125], "size": [270, 82], "flags": {"collapsed": false}, "order": 20, "mode": 0, "inputs": [{"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 458}, {"localized_name": "扩展", "name": "expand", "type": "INT", "widget": {"name": "expand"}, "link": null}, {"localized_name": "倒角", "name": "tapered_corners", "type": "BOOLEAN", "widget": {"name": "tapered_corners"}, "link": null}], "outputs": [{"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": [459]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "GrowMask"}, "widgets_values": [15, true]}, {"id": 303, "type": "KSampler //Inspire", "pos": [-8232.8720703125, -281.2507019042969], "size": [270, 426], "flags": {}, "order": 30, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 476}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 427}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 428}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 433}, {"localized_name": "scheduler_func_opt", "name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC", "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": 473}, {"localized_name": "noise_mode", "name": "noise_mode", "type": "COMBO", "widget": {"name": "noise_mode"}, "link": null}, {"localized_name": "batch_seed_mode", "name": "batch_seed_mode", "type": "COMBO", "widget": {"name": "batch_seed_mode"}, "link": null}, {"localized_name": "variation_seed", "name": "variation_seed", "type": "INT", "widget": {"name": "variation_seed"}, "link": null}, {"localized_name": "variation_strength", "name": "variation_strength", "type": "FLOAT", "widget": {"name": "variation_strength"}, "link": null}, {"localized_name": "variation_method", "name": "variation_method", "shape": 7, "type": "COMBO", "widget": {"name": "variation_method"}, "link": null}, {"localized_name": "internal_seed", "name": "internal_seed", "shape": 7, "type": "INT", "widget": {"name": "internal_seed"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [431]}], "properties": {"cnr_id": "comfyui-inspire-pack", "ver": "1.18.0", "Node name for S&R": "KSampler //Inspire"}, "widgets_values": [809230078443384, "randomize", 20, 8, "euler", "normal", 1, "GPU(=A1111)", "incremental", 0, 0, "linear", 0]}, {"id": 300, "type": "CR Text", "pos": [-9073.82421875, -536.7813110351562], "size": [400, 200], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [417, 418]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": [""]}, {"id": 305, "type": "PreviewImage", "pos": [-7902.32177734375, -211.78440856933594], "size": [380.5104675292969, 481.1038513183594], "flags": {}, "order": 34, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 437}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 288, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [-10321.4287109375, -465.619140625], "size": [363.6851501464844, 330], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 407}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 408}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": 460}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [481]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": [458, 479, 482]}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "None", 948, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 322, "type": "PrimitiveInt", "pos": [-10289.5009765625, -10.44093132019043], "size": [270, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "INT", "type": "INT", "links": [460, 461, 462]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PrimitiveInt"}, "widgets_values": [1344, "fixed"]}, {"id": 335, "type": "PreviewImage", "pos": [-9822.521484375, -154.13331604003906], "size": [235.37484741210938, 369.36529541015625], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 484}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 333, "type": "INPAINT_InpaintWithModel", "pos": [-9852.376953125, -352.6926574707031], "size": [270, 142], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "inpaint_model", "name": "inpaint_model", "type": "INPAINT_MODEL", "link": 480}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 481}, {"localized_name": "mask", "name": "mask", "type": "MASK", "link": 482}, {"localized_name": "optional_upscale_model", "name": "optional_upscale_model", "shape": 7, "type": "UPSCALE_MODEL", "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [483, 484]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_InpaintWithModel"}, "widgets_values": [635063803174382, "randomize"]}, {"id": 290, "type": "InpaintCropImproved", "pos": [-9059.0869140625, -280.57666015625], "size": [348.095703125, 626], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 483}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 459}, {"localized_name": "optional_context_mask", "name": "optional_context_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "downscale_algorithm", "name": "downscale_algorithm", "type": "COMBO", "widget": {"name": "downscale_algorithm"}, "link": null}, {"localized_name": "upscale_algorithm", "name": "upscale_algorithm", "type": "COMBO", "widget": {"name": "upscale_algorithm"}, "link": null}, {"localized_name": "preresize", "name": "preresize", "type": "BOOLEAN", "widget": {"name": "preresize"}, "link": null}, {"localized_name": "preresize_mode", "name": "preresize_mode", "type": "COMBO", "widget": {"name": "preresize_mode"}, "link": null}, {"localized_name": "preresize_min_width", "name": "preresize_min_width", "type": "INT", "widget": {"name": "preresize_min_width"}, "link": null}, {"localized_name": "preresize_min_height", "name": "preresize_min_height", "type": "INT", "widget": {"name": "preresize_min_height"}, "link": null}, {"localized_name": "preresize_max_width", "name": "preresize_max_width", "type": "INT", "widget": {"name": "preresize_max_width"}, "link": null}, {"localized_name": "preresize_max_height", "name": "preresize_max_height", "type": "INT", "widget": {"name": "preresize_max_height"}, "link": null}, {"localized_name": "mask_fill_holes", "name": "mask_fill_holes", "type": "BOOLEAN", "widget": {"name": "mask_fill_holes"}, "link": null}, {"localized_name": "mask_expand_pixels", "name": "mask_expand_pixels", "type": "INT", "widget": {"name": "mask_expand_pixels"}, "link": null}, {"localized_name": "mask_invert", "name": "mask_invert", "type": "BOOLEAN", "widget": {"name": "mask_invert"}, "link": null}, {"localized_name": "mask_blend_pixels", "name": "mask_blend_pixels", "type": "INT", "widget": {"name": "mask_blend_pixels"}, "link": null}, {"localized_name": "mask_hipass_filter", "name": "mask_hipass_filter", "type": "FLOAT", "widget": {"name": "mask_hipass_filter"}, "link": null}, {"localized_name": "extend_for_outpainting", "name": "extend_for_outpainting", "type": "BOOLEAN", "widget": {"name": "extend_for_outpainting"}, "link": null}, {"localized_name": "extend_up_factor", "name": "extend_up_factor", "type": "FLOAT", "widget": {"name": "extend_up_factor"}, "link": null}, {"localized_name": "extend_down_factor", "name": "extend_down_factor", "type": "FLOAT", "widget": {"name": "extend_down_factor"}, "link": null}, {"localized_name": "extend_left_factor", "name": "extend_left_factor", "type": "FLOAT", "widget": {"name": "extend_left_factor"}, "link": null}, {"localized_name": "extend_right_factor", "name": "extend_right_factor", "type": "FLOAT", "widget": {"name": "extend_right_factor"}, "link": null}, {"localized_name": "context_from_mask_extend_factor", "name": "context_from_mask_extend_factor", "type": "FLOAT", "widget": {"name": "context_from_mask_extend_factor"}, "link": null}, {"localized_name": "output_resize_to_target_size", "name": "output_resize_to_target_size", "type": "BOOLEAN", "widget": {"name": "output_resize_to_target_size"}, "link": null}, {"localized_name": "output_target_width", "name": "output_target_width", "type": "INT", "widget": {"name": "output_target_width"}, "link": 461}, {"localized_name": "output_target_height", "name": "output_target_height", "type": "INT", "widget": {"name": "output_target_height"}, "link": 462}, {"localized_name": "output_padding", "name": "output_padding", "type": "COMBO", "widget": {"name": "output_padding"}, "link": null}], "outputs": [{"localized_name": "stitcher", "name": "stitcher", "type": "STITCHER", "links": [435]}, {"localized_name": "cropped_image", "name": "cropped_image", "type": "IMAGE", "links": [424, 485]}, {"localized_name": "cropped_mask", "name": "cropped_mask", "type": "MASK", "links": [423]}], "properties": {"cnr_id": "comfyui-inpaint-cropand<PERSON>itch", "ver": "2.1.7", "Node name for S&R": "InpaintCropImproved"}, "widgets_values": ["bilinear", "bicubic", false, "ensure minimum resolution", 1024, 1024, 16384, 16384, true, 0, false, 32, 0.1, false, 1, 1, 1, 1, 1.2, false, 512, 512, "32"]}, {"id": 325, "type": "LayerUtility: JoyCaption2", "pos": [-7023.47412109375, 1.470901370048523], "size": [303.7271423339844, 342], "flags": {}, "order": 33, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 466}, {"localized_name": "额外选项", "name": "extra_options", "shape": 7, "type": "JoyCaption2ExtraOption", "link": null}, {"localized_name": "LLM模型", "name": "llm_model", "type": "COMBO", "widget": {"name": "llm_model"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "数据类型", "name": "dtype", "type": "COMBO", "widget": {"name": "dtype"}, "link": null}, {"localized_name": "VLM LoRA", "name": "vlm_lora", "type": "COMBO", "widget": {"name": "vlm_lora"}, "link": null}, {"localized_name": "字幕类型", "name": "caption_type", "type": "COMBO", "widget": {"name": "caption_type"}, "link": null}, {"localized_name": "字幕长度", "name": "caption_length", "type": "COMBO", "widget": {"name": "caption_length"}, "link": null}, {"localized_name": "用户提示", "name": "user_prompt", "type": "STRING", "widget": {"name": "user_prompt"}, "link": null}, {"localized_name": "最大新令牌数", "name": "max_new_tokens", "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "顶部P", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "温度", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}, {"localized_name": "使用全局模型", "name": "use_global_model", "type": "BOOLEAN", "widget": {"name": "use_global_model"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "shape": 6, "type": "STRING", "links": [467]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerUtility: JoyCaption2"}, "widgets_values": ["Orenguteng/Llama-3.1-8B-Lexi-Uncensored-V2", "cuda", "nf4", "text_model", "Descriptive", "any", "", 300, 0.9, 0.6, false, false], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 327, "type": "CR Text", "pos": [-6683.96875, 166.84469604492188], "size": [400, 200], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [469]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": [""]}, {"id": 307, "type": "PreviewImage", "pos": [-5464.51220703125, -529.0477905273438], "size": [578.784423828125, 555.8528442382812], "flags": {}, "order": 42, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 438}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 328, "type": "CR Text Concatenate", "pos": [-6241.0009765625, -22.130390167236328], "size": [270, 78], "flags": {}, "order": 36, "mode": 0, "inputs": [{"localized_name": "text1", "name": "text1", "shape": 7, "type": "STRING", "link": 468}, {"localized_name": "text2", "name": "text2", "shape": 7, "type": "STRING", "link": 469}, {"localized_name": "separator", "name": "separator", "shape": 7, "type": "STRING", "widget": {"name": "separator"}, "link": null}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [470]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": [""]}, {"id": 315, "type": "KSamplerSelect", "pos": [-6589.36083984375, -576.2373046875], "size": [315, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"label": "采样器", "localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [443]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 313, "type": "FluxGuidance", "pos": [-6595.07470703125, -459.5735168457031], "size": [315, 58], "flags": {"collapsed": false}, "order": 38, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 448}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [451]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 323, "type": "GrowMask", "pos": [-7343.423828125, -742.5155639648438], "size": [270, 82], "flags": {"collapsed": false}, "order": 21, "mode": 0, "inputs": [{"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 479}, {"localized_name": "扩展", "name": "expand", "type": "INT", "widget": {"name": "expand"}, "link": null}, {"localized_name": "倒角", "name": "tapered_corners", "type": "BOOLEAN", "widget": {"name": "tapered_corners"}, "link": null}], "outputs": [{"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": [464]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "GrowMask"}, "widgets_values": [15, true]}, {"id": 310, "type": "SamplerCustom", "pos": [-5914.6611328125, -524.************], "size": [424.28216552734375, 547.2218017578125], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 477}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 453}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 454}, {"label": "采样器", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 443}, {"label": "Sigmas", "localized_name": "Sigmas", "name": "sigmas", "type": "SIGMAS", "link": 444}, {"label": "Latent", "localized_name": "Latent", "name": "latent_image", "type": "LATENT", "link": 455}, {"localized_name": "添加噪波", "name": "add_noise", "type": "BOOLEAN", "widget": {"name": "add_noise"}, "link": null}, {"localized_name": "噪波种子", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"label": "输出", "localized_name": "Latent", "name": "output", "type": "LATENT", "links": [449]}, {"label": "降噪输出", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 1094242625305932, "randomize", 1]}, {"id": 336, "type": "PreviewImage", "pos": [-8669.9248046875, 209.97903442382812], "size": [378.33343505859375, 520.7106323242188], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 485}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 334, "type": "INPAINT_LoadInpaintModel", "pos": [-9846.9482421875, -473.70147705078125], "size": [270, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"localized_name": "INPAINT_MODEL", "name": "INPAINT_MODEL", "type": "INPAINT_MODEL", "links": [480]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_LoadInpaintModel"}, "widgets_values": ["big-lama.pt"]}, {"id": 299, "type": "CR Text", "pos": [-9065.1484375, -767.8826904296875], "size": [400, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [415, 416]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["simple background，Shining, golden starlight，1girl,purple dress,black stockings"]}, {"id": 330, "type": "PrimitiveFloat", "pos": [-8240.5869140625, 208.4508514404297], "size": [270, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "FLOAT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "浮点", "name": "FLOAT", "type": "FLOAT", "links": [473]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PrimitiveFloat"}, "widgets_values": [0.4]}, {"id": 326, "type": "ShowText|pysssss", "pos": [-6694.10595703125, -60.19473648071289], "size": [440.1817321777344, 231.95851135253906], "flags": {}, "order": 35, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 467}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [468]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["\"Mystical Maiden: A Radiant Enchantress Illuminates the Starry Expanse\n\nIn this captivating illustration, a bewitching young enchantress is depicted in a moment of ethereal triumph. Her piercing red eyes shine like rubies against the celestial backdrop, as she unfurls her delicate wings, shimmering with a soft, luminescent glow. The enchantress's raven tresses cascade down her back, adorned with a delicate silver clip, while her slender fingers grasp the hem of her majestic cloak, its deep purple hue embroidered with intricate, golden patterns.\n\nAs she rises into the air, her slender legs clad in supple, dark tights, the enchantress's slender form is accentuated by the flowing folds of her cloak. A delicate, gemstone-encrusted hat rests atop her head, its pointed tip glinting with a subtle, otherworldly light. The overall effect is one of enchanting, dreamlike beauty, as if the enchantress has stepped straight from the realm of myth and legend into the realm of the living.\n\nThe background of the illustration is a swirling vortex of starlight and glittering stardust, evoking the infinite possibilities of the cosmos. The overall atmosphere is one of wonder and enchantment, as if the very fabric of reality has been woven into a tapestry of magic and possibility.\""]}, {"id": 287, "type": "LoadImage", "pos": [-10672.619140625, -456.34423828125], "size": [270, 314], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [407, 486]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": [408]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-546642.200000003.png [input]", "image"]}, {"id": 337, "type": "PreviewImage", "pos": [-4988.80419921875, -518.2269287109375], "size": [401.7850036621094, 584.1389770507812], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 486}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}], "links": [[407, 287, 0, 288, 0, "IMAGE"], [408, 287, 1, 288, 1, "MASK"], [413, 297, 0, 296, 0, "CONDITIONING"], [414, 298, 0, 296, 1, "CONDITIONING"], [415, 299, 0, 297, 7, "STRING"], [416, 299, 0, 297, 8, "STRING"], [417, 300, 0, 298, 7, "STRING"], [418, 300, 0, 298, 8, "STRING"], [419, 295, 1, 297, 0, "CLIP"], [420, 295, 1, 298, 0, "CLIP"], [421, 301, 0, 296, 2, "VAE"], [423, 290, 2, 296, 4, "MASK"], [424, 290, 1, 296, 3, "IMAGE"], [425, 295, 0, 302, 0, "MODEL"], [427, 296, 0, 303, 1, "CONDITIONING"], [428, 296, 1, 303, 2, "CONDITIONING"], [431, 303, 0, 304, 0, "LATENT"], [433, 296, 3, 303, 3, "LATENT"], [434, 296, 2, 302, 1, "LATENT"], [435, 290, 0, 306, 0, "STITCHER"], [436, 304, 0, 306, 1, "IMAGE"], [437, 306, 0, 305, 0, "IMAGE"], [438, 317, 0, 307, 0, "IMAGE"], [439, 308, 0, 309, 0, "MODEL"], [443, 315, 0, 310, 3, "SAMPLER"], [444, 309, 0, 310, 4, "SIGMAS"], [446, 314, 0, 311, 0, "CLIP"], [447, 314, 0, 312, 0, "CLIP"], [448, 311, 0, 313, 0, "CONDITIONING"], [449, 310, 0, 317, 0, "LATENT"], [450, 318, 0, 317, 1, "VAE"], [451, 313, 0, 319, 0, "CONDITIONING"], [452, 312, 0, 319, 1, "CONDITIONING"], [453, 319, 0, 310, 1, "CONDITIONING"], [454, 319, 1, 310, 2, "CONDITIONING"], [455, 319, 2, 310, 5, "LATENT"], [458, 288, 1, 321, 0, "MASK"], [459, 321, 0, 290, 1, "MASK"], [460, 322, 0, 288, 9, "INT"], [461, 322, 0, 290, 23, "INT"], [462, 322, 0, 290, 24, "INT"], [464, 323, 0, 324, 0, "MASK"], [465, 324, 0, 319, 4, "MASK"], [466, 304, 0, 325, 0, "IMAGE"], [467, 325, 0, 326, 0, "STRING"], [468, 326, 0, 328, 0, "STRING"], [469, 327, 0, 328, 1, "STRING"], [470, 328, 0, 311, 1, "STRING"], [471, 318, 0, 319, 2, "VAE"], [472, 301, 0, 304, 1, "VAE"], [473, 330, 0, 303, 10, "FLOAT"], [474, 306, 0, 319, 3, "IMAGE"], [475, 302, 0, 331, 0, "MODEL"], [476, 331, 0, 303, 0, "MODEL"], [477, 332, 0, 310, 0, "MODEL"], [478, 308, 0, 332, 0, "MODEL"], [479, 288, 1, 323, 0, "MASK"], [480, 334, 0, 333, 0, "INPAINT_MODEL"], [481, 288, 0, 333, 1, "IMAGE"], [482, 288, 1, 333, 2, "MASK"], [483, 333, 0, 290, 0, "IMAGE"], [484, 333, 0, 335, 0, "IMAGE"], [485, 290, 1, 336, 0, "IMAGE"], [486, 287, 0, 337, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Group", "bounding": [-9441.7939453125, -826.9682006835938, 1921.51611328125, 1203.6409912109375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Group", "bounding": [-7483.15673828125, -824.5975341796875, 2594.88671875, 1212.4803466796875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Group", "bounding": [-9870.6708984375, -547.3014526367188, 400.1341857910156, 766.3132934570312], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6588450000000016, "offset": [11015.218381873117, 1097.5634092791756]}, "frontendVersion": "1.18.9", "0246.VERSION": [0, 0, 4], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}