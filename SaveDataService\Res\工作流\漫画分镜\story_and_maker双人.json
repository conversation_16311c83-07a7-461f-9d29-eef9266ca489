{"id": "1f130a8b-abf0-46c5-b96e-c3862a090d9c", "revision": 0, "last_node_id": 8, "last_link_id": 15, "nodes": [{"id": 6, "type": "StoryDiffusion_KSampler", "pos": [5142.27099609375, 82.6561050415039], "size": [315, 330], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 7}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 8}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 9}, {"localized_name": "info", "name": "info", "type": "DIFFINFO", "link": 10}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 11}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "sa32_degree", "name": "sa32_degree", "type": "FLOAT", "widget": {"name": "sa32_degree"}, "link": null}, {"localized_name": "sa64_degree", "name": "sa64_degree", "type": "FLOAT", "widget": {"name": "sa64_degree"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [5]}], "properties": {"cnr_id": "comfyui_storydiffusion", "ver": "07d1dcabbb7c26a01048dafd31507aee10327dbb", "Node name for S&R": "StoryDiffusion_KSampler", "widget_ue_connectable": {}}, "widgets_values": [1354622557, "randomize", 20, 8, "euler", "normal", 0.5, 0.5, 1]}, {"id": 3, "type": "CLIPVisionLoader", "pos": [4707.05078125, -58.720741271972656], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [4]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CLIPVisionLoader", "widget_ue_connectable": {}}, "widgets_values": ["CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"]}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [4708.4326171875, -207.49295043945312], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [2]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [12]}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [3, 6]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "CheckpointLoaderSimple", "widget_ue_connectable": {}}, "widgets_values": ["XL\\juggernautXL_ragnarokBy.safetensors"]}, {"id": 4, "type": "StoryDiffusion_Apply", "pos": [5088.943359375, -205.57427978515625], "size": [315, 218], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 2}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 3}, {"localized_name": "CLIP_VISION", "name": "CLIP_VISION", "shape": 7, "type": "CLIP_VISION", "link": 4}, {"localized_name": "infer_mode", "name": "infer_mode", "type": "COMBO", "widget": {"name": "infer_mode"}, "link": null}, {"localized_name": "photomake_ckpt", "name": "photomake_ckpt", "type": "COMBO", "widget": {"name": "photomake_ckpt"}, "link": null}, {"localized_name": "ipadapter_ckpt", "name": "ipadapter_ckpt", "type": "COMBO", "widget": {"name": "ipadapter_ckpt"}, "link": null}, {"localized_name": "quantize_mode", "name": "quantize_mode", "type": "COMBO", "widget": {"name": "quantize_mode"}, "link": null}, {"localized_name": "lora_scale", "name": "lora_scale", "type": "FLOAT", "widget": {"name": "lora_scale"}, "link": null}, {"localized_name": "extra_function", "name": "extra_function", "type": "STRING", "widget": {"name": "extra_function"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "slot_index": 0, "links": [7]}, {"localized_name": "switch", "name": "switch", "type": "DIFFCONDI", "slot_index": 1, "links": [13]}], "properties": {"cnr_id": "comfyui_storydiffusion", "ver": "07d1dcabbb7c26a01048dafd31507aee10327dbb", "Node name for S&R": "StoryDiffusion_Apply", "widget_ue_connectable": {}}, "widgets_values": ["story_and_maker", "none", "mask.bin", "fp8", 0.8, ""]}, {"id": 8, "type": "EmptyLatentImage", "pos": [5137.5712890625, 464.2717590332031], "size": [315, 106], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 14}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 15}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [11]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "EmptyLatentImage", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [512, 512, 1]}, {"id": 1, "type": "SaveImage", "pos": [5494.13525390625, 77.48656463623047], "size": [705.8995971679688, 497.97515869140625], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 1}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"]}, {"id": 5, "type": "VAEDecode", "pos": [5514.43896484375, -26.200868606567383], "size": [285.03509521484375, 46], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 5}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 6}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [1]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 7, "type": "StoryDiffusion_CLIPTextEncode", "pos": [4662.650390625, 84.11543273925781], "size": [420.00933837890625, 573.784423828125], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 12}, {"localized_name": "switch", "name": "switch", "type": "DIFFCONDI", "link": 13}, {"localized_name": "add_function", "name": "add_function", "shape": 7, "type": "STORY_CONDITIONING", "link": null}, {"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "control_image", "name": "control_image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "role_text", "name": "role_text", "type": "STRING", "widget": {"name": "role_text"}, "link": null}, {"localized_name": "scene_text", "name": "scene_text", "type": "STRING", "widget": {"name": "scene_text"}, "link": null}, {"localized_name": "pos_text", "name": "pos_text", "type": "STRING", "widget": {"name": "pos_text"}, "link": null}, {"localized_name": "neg_text", "name": "neg_text", "type": "STRING", "widget": {"name": "neg_text"}, "link": null}, {"localized_name": "lora_trigger_words", "name": "lora_trigger_words", "type": "STRING", "widget": {"name": "lora_trigger_words"}, "link": null}, {"localized_name": "add_style", "name": "add_style", "type": "COMBO", "widget": {"name": "add_style"}, "link": null}, {"localized_name": "mask_threshold", "name": "mask_threshold", "type": "FLOAT", "widget": {"name": "mask_threshold"}, "link": null}, {"localized_name": "extra_param", "name": "extra_param", "type": "STRING", "widget": {"name": "extra_param"}, "link": null}, {"localized_name": "guidance_list", "name": "guidance_list", "type": "STRING", "widget": {"name": "guidance_list"}, "link": null}], "outputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [8]}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [9]}, {"localized_name": "info", "name": "info", "type": "DIFFINFO", "slot_index": 2, "links": [10]}, {"localized_name": "width", "name": "width", "type": "INT", "slot_index": 3, "links": [14]}, {"localized_name": "height", "name": "height", "type": "INT", "slot_index": 4, "links": [15]}], "properties": {"cnr_id": "comfyui_storydiffusion", "ver": "07d1dcabbb7c26a01048dafd31507aee10327dbb", "Node name for S&R": "StoryDiffusion_CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": [1344, 768, "[<PERSON>] 1girl, wearing a white T-shirt, blue loose hair.\n[<PERSON><PERSON><PERSON>] 1boy,wearing a suit,black hair.", "[<PERSON>] have breakfast by the window;\n[<PERSON><PERSON><PERSON>] is working.\n[NC] anime stylr,a beautiful park.\n[<PERSON>] and [<PERSON><PERSON><PERSON>] dancing on the stage.", "best", "bad anatomy, bad hands, missing fingers, extra fingers,three hands, three legs, bad arms, missing legs, missing arms, poorly drawn face, bad face, fused face, cloned face, three crus, fused feet, fused thigh, extra crus, ugly fingers, horn,amputation, disconnected limbs", "best quality", "Japanese_Anime", 0.5, "", "0., 0.25, 0.4, 0.75;0.6, 0.25, 1., 0.75", [false, true], [false, true], [false, true], [false, true], [false, true]]}], "links": [[1, 5, 0, 1, 0, "IMAGE"], [2, 2, 0, 4, 0, "MODEL"], [3, 2, 2, 4, 1, "VAE"], [4, 3, 0, 4, 2, "CLIP_VISION"], [5, 6, 0, 5, 0, "LATENT"], [6, 2, 2, 5, 1, "VAE"], [7, 4, 0, 6, 0, "MODEL"], [8, 7, 0, 6, 1, "CONDITIONING"], [9, 7, 1, 6, 2, "CONDITIONING"], [10, 7, 2, 6, 3, "DIFFINFO"], [11, 8, 0, 6, 4, "LATENT"], [12, 2, 1, 7, 0, "CLIP"], [13, 4, 1, 7, 1, "DIFFCONDI"], [14, 7, 3, 8, 0, "INT"], [15, 7, 4, 8, 1, "INT"]], "groups": [], "config": {}, "extra": {"ue_links": [], "links_added_by_ue": [], "frontendVersion": "1.18.10", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}