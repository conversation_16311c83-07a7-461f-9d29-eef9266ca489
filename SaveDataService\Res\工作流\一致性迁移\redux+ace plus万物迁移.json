{"id": "7c84ef0b-48a5-47ab-acc5-ce729524260d", "revision": 0, "last_node_id": 180, "last_link_id": 277, "nodes": [{"id": 21, "type": "ImageScale", "pos": [319.697021484375, -705.8347778320312], "size": [315, 130], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 29}, {"localized_name": "缩放算法", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [30]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 0, 1024, "disabled"]}, {"id": 50, "type": "Mask Fill Holes", "pos": [344.93994140625, -513.041015625], "size": [216.07122802734375, 39.35673522949219], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "遮罩", "localized_name": "masks", "name": "masks", "type": "MASK", "link": 64}], "outputs": [{"label": "遮罩", "localized_name": "MASKS", "name": "MASKS", "type": "MASK", "slot_index": 0, "links": [65]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Mask Fill Holes"}, "widgets_values": []}, {"id": 51, "type": "GrowMaskWithBlur", "pos": [340.1573791503906, -414.9662780761719], "size": [315, 246], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 65}, {"localized_name": "expand", "name": "expand", "type": "INT", "widget": {"name": "expand"}, "link": null}, {"localized_name": "incremental_expandrate", "name": "incremental_expandrate", "type": "FLOAT", "widget": {"name": "incremental_expandrate"}, "link": null}, {"localized_name": "tapered_corners", "name": "tapered_corners", "type": "BOOLEAN", "widget": {"name": "tapered_corners"}, "link": null}, {"localized_name": "flip_input", "name": "flip_input", "type": "BOOLEAN", "widget": {"name": "flip_input"}, "link": null}, {"localized_name": "blur_radius", "name": "blur_radius", "type": "FLOAT", "widget": {"name": "blur_radius"}, "link": null}, {"localized_name": "lerp_alpha", "name": "lerp_alpha", "type": "FLOAT", "widget": {"name": "lerp_alpha"}, "link": null}, {"localized_name": "decay_factor", "name": "decay_factor", "type": "FLOAT", "widget": {"name": "decay_factor"}, "link": null}, {"localized_name": "fill_holes", "name": "fill_holes", "shape": 7, "type": "BOOLEAN", "widget": {"name": "fill_holes"}, "link": null}], "outputs": [{"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 0, "links": [67, 70]}, {"label": "反转遮罩", "localized_name": "mask_inverted", "name": "mask_inverted", "type": "MASK", "links": []}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "GrowMaskWithBlur"}, "widgets_values": [200, 0, true, false, 50, 1, 1, false], "color": "#232", "bgcolor": "#353"}, {"id": 52, "type": "ImageAndMaskPreview", "pos": [685.6597900390625, -509.9150390625], "size": [546.7041625976562, 503.2754211425781], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 66}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 67}, {"localized_name": "mask_opacity", "name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}, {"localized_name": "mask_color", "name": "mask_color", "type": "STRING", "widget": {"name": "mask_color"}, "link": null}, {"localized_name": "pass_through", "name": "pass_through", "type": "BOOLEAN", "widget": {"name": "pass_through"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "composite", "name": "composite", "type": "IMAGE", "links": []}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImageAndMaskPreview"}, "widgets_values": [1, "255, 255, 255", false]}, {"id": 57, "type": "Note", "pos": [349.1698913574219, -120.32949829101562], "size": [308.7099304199219, 106.19007110595703], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["可以自行调整，扩张，模糊半径，让融合更自然"], "color": "#232", "bgcolor": "#353"}, {"id": 138, "type": "Note", "pos": [-100.55154418945312, -1290.1029052734375], "size": [298.90667724609375, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["上传场景图，并且涂抹要被迁移的区域"], "color": "#232", "bgcolor": "#353"}, {"id": 139, "type": "Note", "pos": [238.81222534179688, -947.5996704101562], "size": [298.90667724609375, 88], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["上传产品图，最好为白底图"], "color": "#223", "bgcolor": "#335"}, {"id": 142, "type": "Florence2Run", "pos": [1461.77978515625, -1662.633544921875], "size": [400, 364], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 241}, {"label": "Florence2", "localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "link": 212}, {"localized_name": "text_input", "name": "text_input", "type": "STRING", "widget": {"name": "text_input"}, "link": null}, {"localized_name": "task", "name": "task", "type": "COMBO", "widget": {"name": "task"}, "link": null}, {"localized_name": "fill_mask", "name": "fill_mask", "type": "BOOLEAN", "widget": {"name": "fill_mask"}, "link": null}, {"localized_name": "keep_model_loaded", "name": "keep_model_loaded", "shape": 7, "type": "BOOLEAN", "widget": {"name": "keep_model_loaded"}, "link": null}, {"localized_name": "max_new_tokens", "name": "max_new_tokens", "shape": 7, "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "num_beams", "name": "num_beams", "shape": 7, "type": "INT", "widget": {"name": "num_beams"}, "link": null}, {"localized_name": "do_sample", "name": "do_sample", "shape": 7, "type": "BOOLEAN", "widget": {"name": "do_sample"}, "link": null}, {"localized_name": "output_mask_select", "name": "output_mask_select", "shape": 7, "type": "STRING", "widget": {"name": "output_mask_select"}, "link": null}, {"localized_name": "seed", "name": "seed", "shape": 7, "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "links": []}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "links": []}, {"label": "caption", "localized_name": "caption", "name": "caption", "type": "STRING", "slot_index": 2, "links": [234, 247]}, {"label": "json数据", "localized_name": "data", "name": "data", "type": "JSON", "links": []}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2Run"}, "widgets_values": ["", "more_detailed_caption", true, false, 1024, 3, true, "", 669125406226715, "fixed"]}, {"id": 143, "type": "Seed (rgthree)", "pos": [3157.375, -1644.1866455078125], "size": [210, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"dir": 4, "label": "随机种", "name": "SEED", "shape": 3, "type": "INT", "slot_index": 0, "links": [233]}], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0"}, "widgets_values": [344383173425361, null, null, null], "color": "#323", "bgcolor": "#535"}, {"id": 144, "type": "VAEDecode", "pos": [3141.1787109375, -1297.1378173828125], "size": [210, 46], "flags": {}, "order": 39, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 213}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 214}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [235, 238]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 145, "type": "StyleModelApply", "pos": [1985.4234619140625, -1012.4008178710938], "size": [315, 122], "flags": {}, "order": 36, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 215}, {"label": "风格模型", "localized_name": "风格模型", "name": "style_model", "type": "STYLE_MODEL", "link": 216}, {"label": "CLIP视觉输出", "localized_name": "clip视觉输出", "name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 217}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "强度类型", "name": "strength_type", "type": "COMBO", "widget": {"name": "strength_type"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [218]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 146, "type": "InpaintModelConditioning", "pos": [2347.685302734375, -1028.4493408203125], "size": [302.4000244140625, 138], "flags": {"collapsed": false}, "order": 37, "mode": 0, "inputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 218}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 219}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 220}, {"label": "图像", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 237}, {"label": "遮罩", "localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 236}, {"localized_name": "噪波遮罩", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}], "outputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [230]}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [231]}, {"label": "Latent", "localized_name": "Latent", "name": "latent", "type": "LATENT", "slot_index": 2, "links": [232]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [false]}, {"id": 153, "type": "CLIPTextEncode", "pos": [1470.6602783203125, -1247.40087890625], "size": [210, 88], "flags": {"collapsed": false}, "order": 30, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 222}, {"label": "文本", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 223}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [224, 225]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["The beauty is wearing this skirt."]}, {"id": 154, "type": "FluxGuidance", "pos": [1472.8402099609375, -1138.71630859375], "size": [210, 58], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 224}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [215]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "FluxGuidance"}, "widgets_values": [50]}, {"id": 155, "type": "ConditioningZeroOut", "pos": [1483.9044189453125, -1030.251953125], "size": [210, 26], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 225}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [219]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 159, "type": "Note", "pos": [1874.3701171875, -1144.3739013671875], "size": [339.0431823730469, 88], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["经过本人多次测试，有些图开启ACE++会有轻微的接缝，有些图的效果又很好，大家可以选择是否开启，刷下图看。"], "color": "#232", "bgcolor": "#353"}, {"id": 160, "type": "K<PERSON><PERSON><PERSON>", "pos": [2777.935302734375, -1654.4718017578125], "size": [313.6918640136719, 446], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 229}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 230}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 231}, {"label": "Latent", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 232}, {"label": "随机种", "localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 233}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [213]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [740469924560254, "fixed", 8, 1, "euler", "simple", 1]}, {"id": 161, "type": "StringFunction|pysssss", "pos": [2299.520263671875, -1348.5035400390625], "size": [398.4952697753906, 274], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "action", "name": "action", "type": "COMBO", "widget": {"name": "action"}, "link": null}, {"localized_name": "tidy_tags", "name": "tidy_tags", "type": "COMBO", "widget": {"name": "tidy_tags"}, "link": null}, {"label": "文本_A", "localized_name": "text_a", "name": "text_a", "shape": 7, "type": "STRING", "widget": {"name": "text_a"}, "link": null}, {"label": "文本_B", "localized_name": "text_b", "name": "text_b", "shape": 7, "type": "STRING", "widget": {"name": "text_b"}, "link": 234}, {"label": "文本_C", "localized_name": "text_c", "name": "text_c", "shape": 7, "type": "STRING", "widget": {"name": "text_c"}, "link": 250}], "outputs": [{"label": "字符串", "localized_name": "字符串", "name": "STRING", "type": "STRING", "slot_index": 0, "links": [223]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "StringFunction|pysssss"}, "widgets_values": ["append", "yes", "", "", "", "The image is a top view of a red sweater with blue and white stripes on the sleeves. The sweater has a round neckline and is laid flat on a light green background. On the front of the sweater, there is a small illustration of a white cat with a red bow on its head. The cat is wearing a red and white striped scarf around its neck. Below the cat, there are two white mittens with red snowflakes on them. The mittens are facing each other and appear to be hanging from a string. The overall style of the illustration is playful and whimsical., https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki/Other-Nodes#cr-text"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 162, "type": "Note", "pos": [1890.481689453125, -1670.0638427734375], "size": [369.23626708984375, 90.16323852539062], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["这里可以不输入，也可以输入补充提示词，物品的材质已经由倒推提示词完成。这里可以补充输入例如“这条项链佩戴在脖子上”又或者“这个图案印在衣服上”。请输入英文。"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 164, "type": "Note", "pos": [3160.561279296875, -1464.2706298828125], "size": [210, 90.17313385009766], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["点Randomiz Each Time 随机种变为-1开始刷图。要固定种子点下面的种子编号"], "color": "#323", "bgcolor": "#535"}, {"id": 165, "type": "Note", "pos": [3267.81640625, -1875.7896728515625], "size": [419.32391357421875, 91.68104553222656], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["这里可以不输入，也可以输入补充提示词，物品的材质已经由倒推提示词完成。这里可以补充输入例如“这条项链佩戴在脖子上”又或者“这个图案印在衣服上”。请输入英文。"], "color": "#432", "bgcolor": "#653"}, {"id": 169, "type": "SaveImage", "pos": [1594.5645751953125, -745.5529174804688], "size": [694.451904296875, 750.1203002929688], "flags": {}, "order": 44, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 249}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 167, "type": "Fast Bypasser (rgthree)", "pos": [2294.192138671875, -1662.783935546875], "size": [416.38311767578125, 78], "flags": {}, "order": 25, "mode": 0, "inputs": [{"dir": 3, "name": "Florence2Run", "type": "*", "link": 247}, {"dir": 3, "name": "", "type": "*", "link": null}], "outputs": [{"dir": 4, "label": "可选连接", "name": "OPT_CONNECTION", "type": "*", "links": []}], "properties": {"toggleRestriction": "default"}, "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 157, "type": "LoraLoaderModelOnly", "pos": [1878.268310546875, -1397.9893798828125], "size": [370.2679443359375, 90.75430297851562], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 227}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [226, 228]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["comfyui_subject_lora16.safetensors", 1], "color": "#232", "bgcolor": "#353"}, {"id": 156, "type": "LoraLoaderModelOnly", "pos": [1885.99853515625, -1521.7998046875], "size": [365.1975402832031, 82], "flags": {}, "order": 26, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 226}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [229]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["Flux Turbo Lora加速模型_StarAi_Flux Turbo Lora", 1]}, {"id": 53, "type": "ImageCompositeMasked", "pos": [1308.8985595703125, -678.2767944335938], "size": [245.9457550048828, 205.19923400878906], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "目标图像", "localized_name": "目标图像", "name": "destination", "type": "IMAGE", "link": 68}, {"label": "源图像", "localized_name": "来源图像", "name": "source", "type": "IMAGE", "link": 255}, {"label": "遮罩", "localized_name": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": 70}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}, {"localized_name": "缩放来源图像", "name": "resize_source", "type": "BOOLEAN", "widget": {"name": "resize_source"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [249]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 22, "type": "easy imageSize", "pos": [678.1119384765625, -674.5787963867188], "size": [210, 108], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 30}], "outputs": [{"label": "宽度", "localized_name": "宽度", "name": "width_int", "type": "INT", "slot_index": 0, "links": [54]}, {"label": "高度", "localized_name": "高度", "name": "height_int", "type": "INT", "links": []}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy imageSize"}, "widgets_values": ["Width: 1555 , Height: 1024"]}, {"id": 42, "type": "easy imageInsetCrop", "pos": [913.5155639648438, -725.9772338867188], "size": [315, 154], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 238}, {"localized_name": "测量", "name": "measurement", "type": "COMBO", "widget": {"name": "measurement"}, "link": null}, {"label": "左", "localized_name": "左", "name": "left", "type": "INT", "widget": {"name": "left"}, "link": 54}, {"localized_name": "右", "name": "right", "type": "INT", "widget": {"name": "right"}, "link": null}, {"localized_name": "上", "name": "top", "type": "INT", "widget": {"name": "top"}, "link": null}, {"localized_name": "下", "name": "bottom", "type": "INT", "widget": {"name": "bottom"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [259]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy imageInsetCrop"}, "widgets_values": ["Pixels", 0, 0, 0, 0]}, {"id": 147, "type": "CLIPVisionEncode", "pos": [1649.7327880859375, -1044.93212890625], "size": [222.43797302246094, 78], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "CLIP视觉", "localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 221}, {"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 240}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "CLIP视觉输出", "localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [217]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 172, "type": "InpaintStitchImproved", "pos": [1263.3209228515625, -786.5410766601562], "size": [215.52206420898438, 46], "flags": {}, "order": 42, "mode": 0, "inputs": [{"localized_name": "stitcher", "name": "stitcher", "type": "STITCHER", "link": 258}, {"localized_name": "inpainted_image", "name": "inpainted_image", "type": "IMAGE", "link": 259}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [255]}], "properties": {"cnr_id": "comfyui-inpaint-cropand<PERSON>itch", "ver": "2.1.7", "Node name for S&R": "InpaintStitchImproved"}}, {"id": 158, "type": "Fast Bypasser (rgthree)", "pos": [1876.0892333984375, -1256.192626953125], "size": [351.48333740234375, 78], "flags": {}, "order": 27, "mode": 0, "inputs": [{"dir": 3, "name": "LoRA加载器（仅模型）", "type": "*", "link": 228}, {"dir": 3, "name": "", "type": "*", "link": null}], "outputs": [{"dir": 4, "label": "可选连接", "name": "OPT_CONNECTION", "type": "*", "links": []}], "properties": {"toggleRestriction": "default"}, "color": "#232", "bgcolor": "#353"}, {"id": 44, "type": "LoadImage", "pos": [-110.10535430908203, -1166.04541015625], "size": [315, 314], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [29, 31, 240, 241]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_gyrlf_00006_.png", "image"], "color": "#223", "bgcolor": "#335"}, {"id": 170, "type": "CR Text", "pos": [2301.91943359375, -1528.656494140625], "size": [397.29449462890625, 108], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "*", "links": null}, {"label": "显示帮助", "localized_name": "show_help", "name": "show_help", "type": "STRING", "links": [250]}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["wearing"]}, {"id": 163, "type": "PreviewImage", "pos": [2792.959716796875, -1136.1270751953125], "size": [620.8521728515625, 306.75958251953125], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 235}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 45, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [267.15020751953125, -1619.423583984375], "size": [315, 330], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 57}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 208}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": null}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [66, 68, 273]}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 1, "links": [275]}, {"label": "原始大小", "localized_name": "original_size", "name": "original_size", "type": "BOX", "links": []}, {"localized_name": "width", "name": "width", "type": "INT", "links": []}, {"localized_name": "height", "name": "height", "type": "INT", "links": []}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "shortest", 1024, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 141, "type": "Florence2ModelLoader", "pos": [1142.7305908203125, -1662.6861572265625], "size": [315, 106], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "LoRA", "localized_name": "lora", "name": "lora", "shape": 7, "type": "PEFTLORA", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "attention", "name": "attention", "type": "COMBO", "widget": {"name": "attention"}, "link": null}], "outputs": [{"label": "Florence2", "localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "links": [212]}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2ModelLoader"}, "widgets_values": ["Florence-2-Flux-Large", "fp16", "sdpa"]}, {"id": 148, "type": "UNETLoader", "pos": [1144.03173828125, -1510.6634521484375], "size": [315, 82], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [227]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-fill-dev.safetensors", "fp8_e4m3fn"]}, {"id": 152, "type": "StyleModelLoader", "pos": [1194.3038330078125, -964.9988403320312], "size": [315, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "风格模型名称", "name": "style_model_name", "type": "COMBO", "widget": {"name": "style_model_name"}, "link": null}], "outputs": [{"label": "风格模型", "localized_name": "风格模型", "name": "STYLE_MODEL", "type": "STYLE_MODEL", "slot_index": 0, "links": [216]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 149, "type": "VAELoader", "pos": [1078.480224609375, -1363.4912109375], "size": [315, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [214, 220]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 150, "type": "DualCLIPLoader", "pos": [1080.5416259765625, -1262.9765625], "size": [315, 130], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [222]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 151, "type": "CLIPVisionLoader", "pos": [1092.6429443359375, -1073.1494140625], "size": [323.14959716796875, 58], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"label": "CLIP视觉", "localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [221]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 23, "type": "easy makeImageForICLora", "pos": [310.50445556640625, -1198.88330078125], "size": [277.6011657714844, 226], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "image_1", "localized_name": "图像1", "name": "image_1", "type": "IMAGE", "link": 31}, {"label": "image_2", "localized_name": "图像2", "name": "image_2", "shape": 7, "type": "IMAGE", "link": 256}, {"label": "mask_1", "localized_name": "遮罩1", "name": "mask_1", "shape": 7, "type": "MASK", "link": null}, {"label": "mask_2", "localized_name": "遮罩2", "name": "mask_2", "shape": 7, "type": "MASK", "link": 257}, {"localized_name": "方向", "name": "direction", "type": "COMBO", "widget": {"name": "direction"}, "link": null}, {"localized_name": "限制像素", "name": "pixels", "type": "INT", "widget": {"name": "pixels"}, "link": null}, {"localized_name": "限制方式", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}], "outputs": [{"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [237]}, {"label": "mask", "localized_name": "遮罩", "name": "mask", "type": "MASK", "slot_index": 1, "links": [236]}, {"label": "context_mask", "localized_name": "上下文遮罩", "name": "context_mask", "type": "MASK", "links": []}, {"label": "width", "localized_name": "宽", "name": "width", "type": "INT", "links": []}, {"label": "height", "localized_name": "高", "name": "height", "type": "INT", "links": []}, {"label": "x", "localized_name": "x", "name": "x", "type": "INT", "links": []}, {"label": "y", "localized_name": "y", "name": "y", "type": "INT", "links": []}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy makeImageForICLora"}, "widgets_values": ["left-right", 1024, "auto"]}, {"id": 1, "type": "LoadImage", "pos": [-134.78123474121094, -1633.664794921875], "size": [315, 314], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [57]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": [64, 208]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-4227335.099999994.png [input]", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 180, "type": "PreviewImage", "pos": [1105.0565185546875, -1882.982666015625], "size": [140, 246], "flags": {}, "order": 32, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 277}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 171, "type": "InpaintCropImproved", "pos": [611.2564697265625, -1612.4072265625], "size": [348.095703125, 626], "flags": {"collapsed": false}, "order": 29, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 273}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 275}, {"localized_name": "optional_context_mask", "name": "optional_context_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "downscale_algorithm", "name": "downscale_algorithm", "type": "COMBO", "widget": {"name": "downscale_algorithm"}, "link": null}, {"localized_name": "upscale_algorithm", "name": "upscale_algorithm", "type": "COMBO", "widget": {"name": "upscale_algorithm"}, "link": null}, {"localized_name": "preresize", "name": "preresize", "type": "BOOLEAN", "widget": {"name": "preresize"}, "link": null}, {"localized_name": "preresize_mode", "name": "preresize_mode", "type": "COMBO", "widget": {"name": "preresize_mode"}, "link": null}, {"localized_name": "preresize_min_width", "name": "preresize_min_width", "type": "INT", "widget": {"name": "preresize_min_width"}, "link": null}, {"localized_name": "preresize_min_height", "name": "preresize_min_height", "type": "INT", "widget": {"name": "preresize_min_height"}, "link": null}, {"localized_name": "preresize_max_width", "name": "preresize_max_width", "type": "INT", "widget": {"name": "preresize_max_width"}, "link": null}, {"localized_name": "preresize_max_height", "name": "preresize_max_height", "type": "INT", "widget": {"name": "preresize_max_height"}, "link": null}, {"localized_name": "mask_fill_holes", "name": "mask_fill_holes", "type": "BOOLEAN", "widget": {"name": "mask_fill_holes"}, "link": null}, {"localized_name": "mask_expand_pixels", "name": "mask_expand_pixels", "type": "INT", "widget": {"name": "mask_expand_pixels"}, "link": null}, {"localized_name": "mask_invert", "name": "mask_invert", "type": "BOOLEAN", "widget": {"name": "mask_invert"}, "link": null}, {"localized_name": "mask_blend_pixels", "name": "mask_blend_pixels", "type": "INT", "widget": {"name": "mask_blend_pixels"}, "link": null}, {"localized_name": "mask_hipass_filter", "name": "mask_hipass_filter", "type": "FLOAT", "widget": {"name": "mask_hipass_filter"}, "link": null}, {"localized_name": "extend_for_outpainting", "name": "extend_for_outpainting", "type": "BOOLEAN", "widget": {"name": "extend_for_outpainting"}, "link": null}, {"localized_name": "extend_up_factor", "name": "extend_up_factor", "type": "FLOAT", "widget": {"name": "extend_up_factor"}, "link": null}, {"localized_name": "extend_down_factor", "name": "extend_down_factor", "type": "FLOAT", "widget": {"name": "extend_down_factor"}, "link": null}, {"localized_name": "extend_left_factor", "name": "extend_left_factor", "type": "FLOAT", "widget": {"name": "extend_left_factor"}, "link": null}, {"localized_name": "extend_right_factor", "name": "extend_right_factor", "type": "FLOAT", "widget": {"name": "extend_right_factor"}, "link": null}, {"localized_name": "context_from_mask_extend_factor", "name": "context_from_mask_extend_factor", "type": "FLOAT", "widget": {"name": "context_from_mask_extend_factor"}, "link": null}, {"localized_name": "output_resize_to_target_size", "name": "output_resize_to_target_size", "type": "BOOLEAN", "widget": {"name": "output_resize_to_target_size"}, "link": null}, {"localized_name": "output_target_width", "name": "output_target_width", "type": "INT", "widget": {"name": "output_target_width"}, "link": null}, {"localized_name": "output_target_height", "name": "output_target_height", "type": "INT", "widget": {"name": "output_target_height"}, "link": null}, {"localized_name": "output_padding", "name": "output_padding", "type": "COMBO", "widget": {"name": "output_padding"}, "link": null}], "outputs": [{"localized_name": "stitcher", "name": "stitcher", "type": "STITCHER", "links": [258]}, {"localized_name": "cropped_image", "name": "cropped_image", "type": "IMAGE", "links": [256, 277]}, {"localized_name": "cropped_mask", "name": "cropped_mask", "type": "MASK", "links": [257]}], "properties": {"cnr_id": "comfyui-inpaint-cropand<PERSON>itch", "ver": "2.1.7", "Node name for S&R": "InpaintCropImproved"}, "widgets_values": ["bilinear", "bicubic", false, "ensure minimum resolution", 1024, 1024, 16384, 16384, true, 0, false, 64, 0.1, false, 1, 1, 1, 1, 1.2, true, 1024, 1024, "128"]}], "links": [[29, 44, 0, 21, 0, "IMAGE"], [30, 21, 0, 22, 0, "IMAGE"], [31, 44, 0, 23, 0, "IMAGE"], [54, 22, 0, 42, 2, "INT"], [57, 1, 0, 45, 0, "IMAGE"], [64, 1, 1, 50, 0, "MASK"], [65, 50, 0, 51, 0, "MASK"], [67, 51, 0, 52, 1, "MASK"], [70, 51, 0, 53, 2, "MASK"], [208, 1, 1, 45, 1, "MASK"], [212, 141, 0, 142, 1, "FL2MODEL"], [213, 160, 0, 144, 0, "LATENT"], [214, 149, 0, 144, 1, "VAE"], [215, 154, 0, 145, 0, "CONDITIONING"], [216, 152, 0, 145, 1, "STYLE_MODEL"], [217, 147, 0, 145, 2, "CLIP_VISION_OUTPUT"], [218, 145, 0, 146, 0, "CONDITIONING"], [219, 155, 0, 146, 1, "CONDITIONING"], [220, 149, 0, 146, 2, "VAE"], [221, 151, 0, 147, 0, "CLIP_VISION"], [222, 150, 0, 153, 0, "CLIP"], [223, 161, 0, 153, 1, "STRING"], [224, 153, 0, 154, 0, "CONDITIONING"], [225, 153, 0, 155, 0, "CONDITIONING"], [226, 157, 0, 156, 0, "MODEL"], [227, 148, 0, 157, 0, "MODEL"], [228, 157, 0, 158, 0, "*"], [229, 156, 0, 160, 0, "MODEL"], [230, 146, 0, 160, 1, "CONDITIONING"], [231, 146, 1, 160, 2, "CONDITIONING"], [232, 146, 2, 160, 3, "LATENT"], [233, 143, 0, 160, 4, "INT"], [234, 142, 2, 161, 3, "STRING"], [235, 144, 0, 163, 0, "IMAGE"], [236, 23, 1, 146, 4, "MASK"], [237, 23, 0, 146, 3, "IMAGE"], [238, 144, 0, 42, 0, "IMAGE"], [240, 44, 0, 147, 1, "IMAGE"], [241, 44, 0, 142, 0, "IMAGE"], [247, 142, 2, 167, 0, "*"], [249, 53, 0, 169, 0, "IMAGE"], [66, 45, 0, 52, 0, "IMAGE"], [68, 45, 0, 53, 0, "IMAGE"], [250, 170, 1, 161, 4, "STRING"], [255, 172, 0, 53, 1, "IMAGE"], [256, 171, 1, 23, 1, "IMAGE"], [257, 171, 2, 23, 3, "MASK"], [258, 171, 0, 172, 0, "STITCHER"], [259, 42, 0, 172, 1, "IMAGE"], [273, 45, 0, 171, 0, "IMAGE"], [275, 45, 1, 171, 1, "MASK"], [277, 171, 1, 180, 0, "IMAGE"]], "groups": [{"id": 1, "title": "局部拼接", "bounding": [-124.04780578613281, -1716.03076171875, 1100.73095703125, 856.271484375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "拼回去", "bounding": [245.60989379882812, -842.2156372070312, 1304.19775390625, 904.3150634765625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "取图", "bounding": [1570.904052734375, -843.7164306640625, 737.8073120117188, 902.4006958007812], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "物品迁移", "bounding": [1036.41748046875, -1743.17822265625, 2387.007568359375, 871.6599731445312], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.3719008264462819, "offset": [-13.073148647371031, 1602.5873507565398]}, "reroutes": [{"id": 3, "pos": [383.0778503417969, -772.109619140625], "linkIds": [66, 68]}], "node_versions": {"ComfyUI_LayerStyle": "f8439eb17f03e0fa60a35303493bfc9a7d5ab098", "ComfyUI-Florence2": "dffd12506d50f0540b8a7f4b36a05d4fb5fed2de", "comfy-core": "v0.3.9", "ComfyUI-Inpaint-CropAndStitch": "unknown", "ComfyUI-Easy-Use": "3a8fcbbcb916af5468dd9d9dca96fa79bd164b5b", "rgthree-comfy": "5d771b8b56a343c24a26e8cea1f0c87c3d58102f", "was-node-suite-comfyui": "056badacda52e88d29d6a65f9509cd3115ace0f2", "ComfyUI-KJNodes": "5b8d419c6f9c2ab6559a5758f7f504cb8b3a8412", "ComfyUI-Custom-Scripts": "bc8922deff73f59311c05cef27b9d4caaf43e87b", "ComfyUI_Custom_Nodes_AlekPet": "c7d6277086f6e3eda1fe1769d7e2cd9077c99d96"}, "ue_links": [], "0246.VERSION": [0, 0, 4], "linkExtensions": [{"id": 66, "parentId": 3}, {"id": 68, "parentId": 3}], "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}