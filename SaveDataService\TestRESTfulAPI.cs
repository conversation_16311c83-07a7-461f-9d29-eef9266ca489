using System;
using SaveDataService;
using Newtonsoft.Json;

namespace SaveDataService
{
    /// <summary>
    /// 测试新的 RESTful API 系统
    /// </summary>
    public class TestRESTfulAPI
    {
        /// <summary>
        /// 测试 RESTful API 生成功能
        /// </summary>
        public static void TestApiGeneration()
        {
            try
            {
                Console.WriteLine("=== 测试新的 RESTful API 系统 ===");
                Console.WriteLine();

                // 测试获取所有 API
                Console.WriteLine("1. 测试获取所有 API 描述:");
                var allApis = RESTfulAPIGen.GetAllHttpPostFunctions();
                Console.WriteLine("所有 API 描述:");
                Console.WriteLine(allApis);
                Console.WriteLine();

                // 测试获取特定类的 API
                Console.WriteLine("2. 测试获取 AccountManage 类的 API 描述:");
                var accountApis = RESTfulAPIGen.GetHttpPostFunction("AccountManage");
                Console.WriteLine("AccountManage API 描述:");
                Console.WriteLine(accountApis);
                Console.WriteLine();

                // 测试基类方法
                Console.WriteLine("3. 测试基类方法:");
                var baseApis = RESTfulAPIBase.GetHttpPostFunction(typeof(AccountManage));
                Console.WriteLine("通过基类获取的 API 描述:");
                Console.WriteLine(baseApis);
                Console.WriteLine();

                // 验证 JSON 格式
                try
                {
                    var parsedAll = JsonConvert.DeserializeObject(allApis);
                    var parsedAccount = JsonConvert.DeserializeObject(accountApis);
                    var parsedBase = JsonConvert.DeserializeObject(baseApis);
                    Console.WriteLine("✓ 所有 JSON 格式验证通过");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ JSON 格式验证失败: {ex.Message}");
                }

                Console.WriteLine();
                Console.WriteLine("=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试路由系统
        /// </summary>
        public static void TestRouting()
        {
            try
            {
                Console.WriteLine("=== 测试路由系统 ===");
                Console.WriteLine();

                // 显示可用的 API 类
                var availableClasses = UniversalApiHandler.GetAvailableApiClasses();
                Console.WriteLine("可用的 API 类:");
                foreach (var className in availableClasses)
                {
                    Console.WriteLine($"  - {className}");
                    Console.WriteLine($"    路由: /api/{className.ToLower()}/{{methodName}}");
                }

                Console.WriteLine();
                Console.WriteLine("示例路由:");
                Console.WriteLine("  - /api/accountmanage/register");
                Console.WriteLine("  - /api/accountmanage/loginbyusername");
                Console.WriteLine("  - /api/accountmanage/sendemailverificationcode");
                Console.WriteLine();

                Console.WriteLine("API 描述路由:");
                Console.WriteLine("  - /getrestful (获取所有 API 描述)");
                Console.WriteLine("  - /getrestful/AccountManage (获取特定类的 API 描述)");

                Console.WriteLine();
                Console.WriteLine("=== 路由测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 路由测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始运行新的 RESTful API 系统测试...");
            Console.WriteLine();

            TestApiGeneration();
            Console.WriteLine();
            TestRouting();

            Console.WriteLine();
            Console.WriteLine("所有测试完成。");
        }
    }
}
