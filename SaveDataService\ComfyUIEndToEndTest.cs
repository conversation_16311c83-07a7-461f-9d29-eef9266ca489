using System;
using System.Threading.Tasks;
using System.Linq;
using System.IO;
using System.Collections.Generic;
using Newtonsoft.Json;
using ExcelToData;
using SaveDataService.Manage;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI端到端完整流程测试
    /// 从添加服务器到实际执行任务，全流程验证数据库存储
    /// </summary>
    public static class ComfyUIEndToEndTest
    {
        /// <summary>
        /// 执行完整的端到端测试
        /// </summary>
        public static async Task RunCompleteTest()
        {
            Console.WriteLine("=== 开始ComfyUI端到端完整流程测试 ===");
            Console.WriteLine("此测试将验证从服务器添加到任务执行的完整流程\n");

            try
            {
                var testData = new EndToEndTestData();
                
                // 第一阶段：环境准备
                Console.WriteLine("📋 第一阶段：环境准备");
                await PrepareTestEnvironment(testData);
                
                // 第二阶段：添加服务器
                Console.WriteLine("\n🖥️ 第二阶段：添加ComfyUI服务器");
                await AddComfyUIServers(testData);
                
                // 第三阶段：测试服务器连接
                Console.WriteLine("\n🔗 第三阶段：测试服务器连接");
                await TestServerConnections(testData);
                
                // 第四阶段：添加工作流
                Console.WriteLine("\n📝 第四阶段：添加工作流");
                await AddWorkflows(testData);
                
                // 第五阶段：创建和执行任务
                Console.WriteLine("\n🚀 第五阶段：创建和执行任务");
                await CreateAndExecuteTasks(testData);
                
                // 第六阶段：监控任务执行
                Console.WriteLine("\n👁️ 第六阶段：监控任务执行");
                await MonitorTaskExecution(testData);
                
                // 第七阶段：验证数据库存储
                Console.WriteLine("\n✅ 第七阶段：验证数据库存储");
                await VerifyDatabaseStorage(testData);
                
                // 第八阶段：生成测试报告
                Console.WriteLine("\n📊 第八阶段：生成测试报告");
                await GenerateTestReport(testData);
                
                Console.WriteLine("\n🎉 端到端测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 准备测试环境
        /// </summary>
        private static async Task PrepareTestEnvironment(EndToEndTestData testData)
        {
            Console.WriteLine("   清理之前的测试数据...");

            var comfyUIManage = ComfyUIManage.Instance;

            // 记录测试开始时间
            testData.TestStartTime = DateTime.Now;

            // 获取当前数据库状态
            var existingServers = comfyUIManage.GetAllServers();
            var existingWorkflows = comfyUIManage.GetAllWorkflows();
            var existingTasks = comfyUIManage.GetTasks();

            testData.InitialServerCount = existingServers.Count;
            testData.InitialWorkflowCount = existingWorkflows.Count;
            testData.InitialTaskCount = existingTasks.Count;

            Console.WriteLine($"   初始状态: 服务器 {testData.InitialServerCount} 个, 工作流 {testData.InitialWorkflowCount} 个, 任务 {testData.InitialTaskCount} 个");

            // 清理之前的测试数据
            await CleanupTestData();

            Console.WriteLine("   ✅ 环境准备完成");
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        private static async Task CleanupTestData()
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 删除测试相关的任务
                var allTasks = comfyUIManage.GetTasks();
                var testTasks = allTasks.Where(t => t.taskName != null &&
                    (t.taskName.Contains("端到端测试") || t.creator == "测试用户A" || t.creator == "测试用户B")).ToList();

                foreach (var task in testTasks)
                {
                    comfyUIManage.DeleteTask(task.id);
                }

                // 删除测试相关的工作流
                var allWorkflows = comfyUIManage.GetAllWorkflows();
                var testWorkflows = allWorkflows.Where(w => w.workflowName != null &&
                    (w.workflowName.Contains("端到端测试") || w.creator == "系统测试")).ToList();

                foreach (var workflow in testWorkflows)
                {
                    comfyUIManage.DeleteWorkflow(workflow.id);
                }

                // 删除测试相关的服务器
                var allServers = comfyUIManage.GetAllServers();
                var testServers = allServers.Where(s => s.description != null &&
                    s.description.Contains("端到端测试")).ToList();

                foreach (var server in testServers)
                {
                    comfyUIManage.RemoveServer(server.id);
                }

                Console.WriteLine($"   清理完成: 删除了 {testTasks.Count} 个任务, {testWorkflows.Count} 个工作流, {testServers.Count} 个服务器");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ 清理测试数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加ComfyUI服务器
        /// </summary>
        private static async Task AddComfyUIServers(EndToEndTestData testData)
        {
            var comfyUIManage = ComfyUIManage.Instance;
            
            Console.WriteLine("   添加测试服务器...");
            
            // 添加本地ComfyUI服务器（如果有的话）
            var localServerId = comfyUIManage.AddServer(
                "本地ComfyUI服务器",
                "127.0.0.1",
                8188,
                3,
                "端到端测试用本地服务器"
            );
            testData.ServerIds.Add(localServerId);
            Console.WriteLine($"   ✅ 添加本地服务器: {localServerId}");
            
            // 添加模拟远程服务器
            var remoteServerId = comfyUIManage.AddServer(
                "模拟远程ComfyUI服务器",
                "192.168.1.100",
                8188,
                5,
                "端到端测试用模拟远程服务器"
            );
            testData.ServerIds.Add(remoteServerId);
            Console.WriteLine($"   ✅ 添加远程服务器: {remoteServerId}");
            
            // 添加高性能服务器
            var highPerfServerId = comfyUIManage.AddServer(
                "高性能ComfyUI服务器",
                "192.168.1.200",
                8188,
                10,
                "端到端测试用高性能服务器"
            );
            testData.ServerIds.Add(highPerfServerId);
            Console.WriteLine($"   ✅ 添加高性能服务器: {highPerfServerId}");
            
            Console.WriteLine($"   总共添加了 {testData.ServerIds.Count} 台服务器");
        }

        /// <summary>
        /// 测试服务器连接
        /// </summary>
        private static async Task TestServerConnections(EndToEndTestData testData)
        {
            var comfyUIManage = ComfyUIManage.Instance;
            
            Console.WriteLine("   测试所有服务器连接...");
            
            int onlineCount = 0;
            foreach (var serverId in testData.ServerIds)
            {
                var isOnline = await comfyUIManage.TestServerConnection(serverId);
                if (isOnline)
                {
                    onlineCount++;
                    testData.OnlineServerIds.Add(serverId);
                }
                
                // 获取服务器信息
                var server = comfyUIManage.GetServerById(serverId);
                if (server != null)
                {
                    Console.WriteLine($"   服务器 {server.serverName}: {(isOnline ? "在线" : "离线")}");
                }
            }
            
            testData.OnlineServerCount = onlineCount;
            Console.WriteLine($"   ✅ 连接测试完成: {onlineCount}/{testData.ServerIds.Count} 台服务器在线");
        }

        /// <summary>
        /// 添加工作流
        /// </summary>
        private static async Task AddWorkflows(EndToEndTestData testData)
        {
            var comfyUIManage = ComfyUIManage.Instance;
            
            Console.WriteLine("   添加测试工作流...");
            
            // 添加简单文本转图片工作流
            var simpleWorkflowJson = LoadSimpleWorkflow();
            var simpleWorkflowId = comfyUIManage.AddWorkflow(
                "端到端测试-简单文本转图片",
                simpleWorkflowJson,
                "text2img",
                "端到端测试用简单工作流",
                "系统测试"
            );
            testData.WorkflowIds.Add(simpleWorkflowId);
            Console.WriteLine($"   ✅ 添加简单工作流: {simpleWorkflowId}");
            
            // 添加复杂工作流
            var complexWorkflowJson = LoadComplexWorkflow();
            var complexWorkflowId = comfyUIManage.AddWorkflow(
                "端到端测试-复杂图像处理",
                complexWorkflowJson,
                "img2img",
                "端到端测试用复杂工作流",
                "系统测试"
            );
            testData.WorkflowIds.Add(complexWorkflowId);
            Console.WriteLine($"   ✅ 添加复杂工作流: {complexWorkflowId}");
            
            Console.WriteLine($"   总共添加了 {testData.WorkflowIds.Count} 个工作流");
        }

        /// <summary>
        /// 创建和执行任务
        /// </summary>
        private static async Task CreateAndExecuteTasks(EndToEndTestData testData)
        {
            var comfyUIManage = ComfyUIManage.Instance;
            
            Console.WriteLine("   创建测试任务...");
            
            // 为每个工作流创建任务
            for (int i = 0; i < testData.WorkflowIds.Count; i++)
            {
                var workflowId = testData.WorkflowIds[i];
                
                // 创建高优先级任务
                var highPriorityTaskId = comfyUIManage.CreateTask(
                    workflowId,
                    $"端到端测试-高优先级任务{i + 1}",
                    "测试用户A",
                    9
                );
                testData.TaskIds.Add(highPriorityTaskId);
                Console.WriteLine($"   ✅ 创建高优先级任务: {highPriorityTaskId}");
                
                // 创建普通优先级任务
                var normalTaskId = comfyUIManage.CreateTask(
                    workflowId,
                    $"端到端测试-普通任务{i + 1}",
                    "测试用户B",
                    5
                );
                testData.TaskIds.Add(normalTaskId);
                Console.WriteLine($"   ✅ 创建普通任务: {normalTaskId}");
            }
            
            Console.WriteLine($"   总共创建了 {testData.TaskIds.Count} 个任务");
            
            // 尝试执行任务（如果有在线服务器）
            if (testData.OnlineServerCount > 0)
            {
                Console.WriteLine("   尝试执行任务...");
                
                foreach (var taskId in testData.TaskIds.Take(2)) // 只执行前两个任务
                {
                    try
                    {
                        var task = comfyUIManage.GetTaskById(taskId);
                        if (task != null)
                        {
                            var workflow = comfyUIManage.GetWorkflowById(task.workflowId);
                            if (workflow != null)
                            {
                                Console.WriteLine($"   尝试执行任务: {task.taskName}");
                                
                                // 获取第一个在线服务器
                                var onlineServers = comfyUIManage.GetOnlineServers();
                                if (onlineServers.Count > 0)
                                {
                                    // 提交任务到服务器
                                    var resultMessage = await comfyUIManage.SubmitWorkflowToServer(
                                        onlineServers[0].id,
                                        workflow.workflowJson
                                    );
                                    var executedTaskId = taskId;

                                    if (resultMessage.Contains("prompt_id") || resultMessage.Contains("成功"))
                                    {
                                        testData.ExecutedTaskIds.Add(executedTaskId);
                                        Console.WriteLine($"   ✅ 任务提交成功: {executedTaskId}");
                                    }
                                    else
                                    {
                                        Console.WriteLine($"   ⚠️ 任务提交失败: {resultMessage}");
                                    }
                                }
                                else
                                {
                                    Console.WriteLine($"   ⚠️ 没有在线的ComfyUI服务器");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ⚠️ 执行任务失败: {ex.Message}");
                    }
                }
            }
            else
            {
                Console.WriteLine("   ⚠️ 没有在线服务器，跳过任务执行");
            }
        }

        /// <summary>
        /// 加载简单工作流JSON
        /// </summary>
        private static string LoadSimpleWorkflow()
        {
            return @"{
                ""1"": {
                    ""inputs"": {
                        ""text"": ""a beautiful landscape"",
                        ""clip"": [""4"", 1]
                    },
                    ""class_type"": ""CLIPTextEncode""
                },
                ""2"": {
                    ""inputs"": {
                        ""text"": """",
                        ""clip"": [""4"", 1]
                    },
                    ""class_type"": ""CLIPTextEncode""
                },
                ""3"": {
                    ""inputs"": {
                        ""seed"": 42,
                        ""steps"": 20,
                        ""cfg"": 8.0,
                        ""sampler_name"": ""euler"",
                        ""scheduler"": ""normal"",
                        ""denoise"": 1.0,
                        ""model"": [""4"", 0],
                        ""positive"": [""1"", 0],
                        ""negative"": [""2"", 0],
                        ""latent_image"": [""5"", 0]
                    },
                    ""class_type"": ""KSampler""
                },
                ""4"": {
                    ""inputs"": {
                        ""ckpt_name"": ""model.safetensors""
                    },
                    ""class_type"": ""CheckpointLoaderSimple""
                },
                ""5"": {
                    ""inputs"": {
                        ""width"": 512,
                        ""height"": 512,
                        ""batch_size"": 1
                    },
                    ""class_type"": ""EmptyLatentImage""
                },
                ""6"": {
                    ""inputs"": {
                        ""samples"": [""3"", 0],
                        ""vae"": [""4"", 2]
                    },
                    ""class_type"": ""VAEDecode""
                },
                ""7"": {
                    ""inputs"": {
                        ""filename_prefix"": ""ComfyUI"",
                        ""images"": [""6"", 0]
                    },
                    ""class_type"": ""SaveImage""
                }
            }";
        }

        /// <summary>
        /// 加载复杂工作流JSON
        /// </summary>
        private static string LoadComplexWorkflow()
        {
            return @"{
                ""1"": {
                    ""inputs"": {
                        ""text"": ""masterpiece, best quality, detailed"",
                        ""clip"": [""4"", 1]
                    },
                    ""class_type"": ""CLIPTextEncode""
                },
                ""2"": {
                    ""inputs"": {
                        ""text"": ""low quality, blurry"",
                        ""clip"": [""4"", 1]
                    },
                    ""class_type"": ""CLIPTextEncode""
                },
                ""3"": {
                    ""inputs"": {
                        ""seed"": 123456,
                        ""steps"": 30,
                        ""cfg"": 7.5,
                        ""sampler_name"": ""dpmpp_2m"",
                        ""scheduler"": ""karras"",
                        ""denoise"": 0.8,
                        ""model"": [""4"", 0],
                        ""positive"": [""1"", 0],
                        ""negative"": [""2"", 0],
                        ""latent_image"": [""5"", 0]
                    },
                    ""class_type"": ""KSampler""
                },
                ""4"": {
                    ""inputs"": {
                        ""ckpt_name"": ""model.safetensors""
                    },
                    ""class_type"": ""CheckpointLoaderSimple""
                },
                ""5"": {
                    ""inputs"": {
                        ""width"": 768,
                        ""height"": 768,
                        ""batch_size"": 1
                    },
                    ""class_type"": ""EmptyLatentImage""
                },
                ""6"": {
                    ""inputs"": {
                        ""samples"": [""3"", 0],
                        ""vae"": [""4"", 2]
                    },
                    ""class_type"": ""VAEDecode""
                },
                ""7"": {
                    ""inputs"": {
                        ""filename_prefix"": ""EndToEndTest"",
                        ""images"": [""6"", 0]
                    },
                    ""class_type"": ""SaveImage""
                }
            }";
        }

        /// <summary>
        /// 监控任务执行
        /// </summary>
        private static async Task MonitorTaskExecution(EndToEndTestData testData)
        {
            var comfyUIManage = ComfyUIManage.Instance;

            Console.WriteLine("   监控任务执行状态...");

            if (testData.ExecutedTaskIds.Count > 0)
            {
                Console.WriteLine("   监控真实执行的任务...");

                // 监控执行中的任务
                foreach (var taskId in testData.ExecutedTaskIds)
                {
                    try
                    {
                        var task = comfyUIManage.GetTaskById(taskId);
                        if (task != null)
                        {
                            Console.WriteLine($"   监控任务: {task.taskName} (状态: {task.status})");

                            // 模拟任务执行过程
                            await SimulateTaskExecution(taskId, testData);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ⚠️ 监控任务失败: {ex.Message}");
                        testData.TestErrors.Add($"监控任务失败: {ex.Message}");
                    }
                }
            }
            else
            {
                Console.WriteLine("   模拟任务执行过程...");

                // 模拟所有任务的执行过程
                foreach (var taskId in testData.TaskIds)
                {
                    await SimulateTaskExecution(taskId, testData);
                }
            }

            Console.WriteLine("   ✅ 任务监控完成");
        }

        /// <summary>
        /// 模拟任务执行过程
        /// </summary>
        private static async Task SimulateTaskExecution(string taskId, EndToEndTestData testData)
        {
            var comfyUIManage = ComfyUIManage.Instance;

            try
            {
                var task = comfyUIManage.GetTaskById(taskId);
                if (task == null) return;

                Console.WriteLine($"   开始执行任务: {task.taskName}");

                // 1. 更新任务为运行中
                comfyUIManage.UpdateTaskStatus(taskId, 1, 0, "node_start", "开始执行");

                // 2. 添加执行日志
                var logId1 = comfyUIManage.AddTaskLog(taskId, 1, "任务开始执行", "workflow_start", "工作流开始", "{\"timestamp\": \"" + DateTime.Now + "\"}");

                // 3. 模拟进度更新
                for (int progress = 10; progress <= 90; progress += 20)
                {
                    await Task.Delay(500); // 模拟处理时间

                    var nodeName = $"node_{progress / 20}";
                    var nodeDisplayName = GetNodeDisplayName(progress);

                    comfyUIManage.UpdateTaskStatus(taskId, 1, progress, nodeName, nodeDisplayName);

                    var logId = comfyUIManage.AddTaskLog(
                        taskId,
                        1,
                        $"执行节点: {nodeDisplayName}",
                        nodeName,
                        nodeDisplayName,
                        $"{{\"progress\": {progress}, \"timestamp\": \"{DateTime.Now}\"}}"
                    );

                    Console.WriteLine($"     进度: {progress}% - {nodeDisplayName}");
                }

                // 4. 添加输出文件
                var fileId1 = comfyUIManage.AddTaskFile(
                    taskId,
                    0, // 输出图片
                    "output_image.png",
                    "/outputs/output_image.png",
                    1024000 // 1MB
                );

                var fileId2 = comfyUIManage.AddTaskFile(
                    taskId,
                    1, // 工作流文件
                    "workflow.json",
                    "/temp/workflow.json",
                    2048 // 2KB
                );

                // 5. 完成任务
                comfyUIManage.UpdateTaskStatus(taskId, 2, 100, "workflow_end", "任务完成");

                var logId2 = comfyUIManage.AddTaskLog(taskId, 1, "任务执行完成", "workflow_end", "工作流完成", "{\"status\": \"success\", \"timestamp\": \"" + DateTime.Now + "\"}");

                // 记录任务日志和文件
                if (!testData.TaskLogs.ContainsKey(taskId))
                {
                    testData.TaskLogs[taskId] = new List<string>();
                }
                // logId1 和 logId2 是 bool 类型，转换为字符串
                testData.TaskLogs[taskId].AddRange(new string[] { logId1.ToString(), logId2.ToString() });

                if (!testData.TaskFiles.ContainsKey(taskId))
                {
                    testData.TaskFiles[taskId] = new List<string>();
                }
                // fileId1 和 fileId2 已经是 string 类型
                testData.TaskFiles[taskId].AddRange(new string[] { fileId1, fileId2 });

                Console.WriteLine($"   ✅ 任务执行完成: {task.taskName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 任务执行失败: {ex.Message}");
                testData.TestErrors.Add($"任务执行失败 {taskId}: {ex.Message}");

                // 更新任务为失败状态
                comfyUIManage.UpdateTaskStatus(taskId, 3, -1, "error", "执行失败", ex.Message);
            }
        }

        /// <summary>
        /// 获取节点显示名称
        /// </summary>
        private static string GetNodeDisplayName(int progress)
        {
            return progress switch
            {
                10 => "加载模型",
                30 => "文本编码",
                50 => "图像采样",
                70 => "VAE解码",
                90 => "保存图片",
                _ => "处理中"
            };
        }

        /// <summary>
        /// 验证数据库存储
        /// </summary>
        private static async Task VerifyDatabaseStorage(EndToEndTestData testData)
        {
            var comfyUIManage = ComfyUIManage.Instance;

            Console.WriteLine("   验证数据库存储完整性...");

            try
            {
                // 1. 验证服务器数据
                Console.WriteLine("   验证服务器数据...");

                // 直接验证我们添加的服务器ID是否存在
                int foundServers = 0;
                foreach (var serverId in testData.ServerIds)
                {
                    var server = comfyUIManage.GetServerById(serverId);
                    if (server == null)
                    {
                        throw new Exception($"服务器不存在: {serverId}");
                    }
                    foundServers++;
                    Console.WriteLine($"     ✅ 服务器验证通过: {server.serverName} (ID: {serverId})");
                }

                if (foundServers != testData.ServerIds.Count)
                {
                    throw new Exception($"服务器数量不匹配: 期望 {testData.ServerIds.Count}, 实际 {foundServers}");
                }

                // 2. 验证工作流数据
                Console.WriteLine("   验证工作流数据...");

                // 直接验证我们添加的工作流ID是否存在
                int foundWorkflows = 0;
                foreach (var workflowId in testData.WorkflowIds)
                {
                    var workflow = comfyUIManage.GetWorkflowById(workflowId);
                    if (workflow == null)
                    {
                        throw new Exception($"工作流不存在: {workflowId}");
                    }

                    // 验证工作流JSON
                    if (string.IsNullOrEmpty(workflow.workflowJson))
                    {
                        throw new Exception($"工作流JSON为空: {workflowId}");
                    }

                    try
                    {
                        JsonConvert.DeserializeObject(workflow.workflowJson);
                    }
                    catch
                    {
                        throw new Exception($"工作流JSON格式无效: {workflowId}");
                    }

                    foundWorkflows++;
                    Console.WriteLine($"     ✅ 工作流验证通过: {workflow.workflowName} (ID: {workflowId})");
                }

                if (foundWorkflows != testData.WorkflowIds.Count)
                {
                    throw new Exception($"工作流数量不匹配: 期望 {testData.WorkflowIds.Count}, 实际 {foundWorkflows}");
                }

                // 3. 验证任务数据
                Console.WriteLine("   验证任务数据...");

                // 直接验证我们添加的任务ID是否存在
                int foundTasks = 0;
                foreach (var taskId in testData.TaskIds)
                {
                    var task = comfyUIManage.GetTaskById(taskId);
                    if (task == null)
                    {
                        throw new Exception($"任务不存在: {taskId}");
                    }

                    // 验证任务关联
                    if (!testData.WorkflowIds.Contains(task.workflowId))
                    {
                        throw new Exception($"任务工作流关联错误: {taskId} -> {task.workflowId}");
                    }

                    // 注意：任务的serverId可能为空，因为在创建任务时可能还没有分配服务器
                    // 所以这里不强制验证服务器关联

                    // 验证时间戳
                    if (task.CreateTime == null || task.UpdateTime == null)
                    {
                        throw new Exception($"任务时间戳缺失: {taskId}");
                    }

                    foundTasks++;
                    Console.WriteLine($"     ✅ 任务验证通过: {task.taskName} (状态: {task.status}, 进度: {task.progress}%, ID: {taskId})");
                }

                if (foundTasks != testData.TaskIds.Count)
                {
                    throw new Exception($"任务数量不匹配: 期望 {testData.TaskIds.Count}, 实际 {foundTasks}");
                }

                // 4. 验证日志数据
                Console.WriteLine("   验证日志数据...");
                foreach (var taskId in testData.TaskIds)
                {
                    try
                    {
                        var logsJson = comfyUIManage.GetTaskLogs(taskId);
                        if (!string.IsNullOrEmpty(logsJson))
                        {
                            var logs = System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(logsJson);
                            Console.WriteLine($"     ✅ 任务 {taskId} 日志验证通过: {logs?.Count ?? 0} 条");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"     ⚠️ 任务 {taskId} 日志验证失败: {ex.Message}");
                    }
                }

                // 5. 验证文件数据
                Console.WriteLine("   验证文件数据...");
                foreach (var taskId in testData.TaskIds)
                {
                    try
                    {
                        var filesJson = comfyUIManage.GetTaskFiles(taskId);
                        if (!string.IsNullOrEmpty(filesJson))
                        {
                            var files = System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(filesJson);
                            Console.WriteLine($"     ✅ 任务 {taskId} 文件验证通过: {files?.Count ?? 0} 个");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"     ⚠️ 任务 {taskId} 文件验证失败: {ex.Message}");
                    }
                }

                testData.TestPassed = true;
                Console.WriteLine("   ✅ 数据库存储验证完成");
            }
            catch (Exception ex)
            {
                testData.TestPassed = false;
                testData.TestErrors.Add($"数据库验证失败: {ex.Message}");
                Console.WriteLine($"   ❌ 数据库验证失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        private static async Task GenerateTestReport(EndToEndTestData testData)
        {
            testData.TestEndTime = DateTime.Now;
            var testDuration = testData.TestEndTime - testData.TestStartTime;

            Console.WriteLine("   生成测试报告...");

            var report = new
            {
                测试概要 = new
                {
                    开始时间 = testData.TestStartTime,
                    结束时间 = testData.TestEndTime,
                    测试时长 = $"{testDuration.TotalSeconds:F2} 秒",
                    测试结果 = testData.TestPassed ? "通过" : "失败",
                    错误数量 = testData.TestErrors.Count
                },
                环境信息 = new
                {
                    初始服务器数 = testData.InitialServerCount,
                    初始工作流数 = testData.InitialWorkflowCount,
                    初始任务数 = testData.InitialTaskCount
                },
                测试数据 = new
                {
                    添加服务器数 = testData.ServerIds.Count,
                    在线服务器数 = testData.OnlineServerCount,
                    添加工作流数 = testData.WorkflowIds.Count,
                    创建任务数 = testData.TaskIds.Count,
                    执行任务数 = testData.ExecutedTaskIds.Count
                },
                详细信息 = new
                {
                    服务器列表 = testData.ServerIds,
                    在线服务器 = testData.OnlineServerIds,
                    工作流列表 = testData.WorkflowIds,
                    任务列表 = testData.TaskIds,
                    执行任务 = testData.ExecutedTaskIds,
                    任务日志统计 = testData.TaskLogs.ToDictionary(kv => kv.Key, kv => kv.Value.Count),
                    任务文件统计 = testData.TaskFiles.ToDictionary(kv => kv.Key, kv => kv.Value.Count)
                },
                错误信息 = testData.TestErrors
            };

            var reportJson = JsonConvert.SerializeObject(report, Formatting.Indented);
            Console.WriteLine("   📊 测试报告:");
            Console.WriteLine(reportJson);

            // 保存报告到文件
            try
            {
                var reportFileName = $"ComfyUI_EndToEnd_Test_Report_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                await File.WriteAllTextAsync(reportFileName, reportJson);
                Console.WriteLine($"   ✅ 测试报告已保存到: {reportFileName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ⚠️ 保存测试报告失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 端到端测试数据容器
    /// </summary>
    public class EndToEndTestData
    {
        public DateTime TestStartTime { get; set; }
        public int InitialServerCount { get; set; }
        public int InitialWorkflowCount { get; set; }
        public int InitialTaskCount { get; set; }

        public List<string> ServerIds { get; set; } = new List<string>();
        public List<string> OnlineServerIds { get; set; } = new List<string>();
        public int OnlineServerCount { get; set; }

        public List<string> WorkflowIds { get; set; } = new List<string>();
        public List<string> TaskIds { get; set; } = new List<string>();
        public List<string> ExecutedTaskIds { get; set; } = new List<string>();

        public Dictionary<string, List<string>> TaskLogs { get; set; } = new Dictionary<string, List<string>>();
        public Dictionary<string, List<string>> TaskFiles { get; set; } = new Dictionary<string, List<string>>();

        public DateTime TestEndTime { get; set; }
        public bool TestPassed { get; set; }
        public List<string> TestErrors { get; set; } = new List<string>();
    }
}
