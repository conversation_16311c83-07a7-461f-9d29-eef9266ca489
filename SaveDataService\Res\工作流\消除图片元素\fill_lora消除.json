{"id": "09fe08da-1b82-490b-bb7b-741d0e48614b", "revision": 0, "last_node_id": 337, "last_link_id": 493, "nodes": [{"id": 315, "type": "KSamplerSelect", "pos": [-8492.271484375, -745.3041381835938], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"label": "采样器", "localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [443]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 317, "type": "VAEDecode", "pos": [-8102.322265625, -672.5399169921875], "size": [210, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 449}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 450}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [438]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 324, "type": "Mask Gaussian Region", "pos": [-8837.7568359375, -898.2288818359375], "size": [270, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "masks", "name": "masks", "type": "MASK", "link": 488}, {"localized_name": "radius", "name": "radius", "type": "FLOAT", "widget": {"name": "radius"}, "link": null}], "outputs": [{"localized_name": "MASKS", "name": "MASKS", "type": "MASK", "links": []}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Mask Gaussian Region"}, "widgets_values": [5]}, {"id": 319, "type": "InpaintModelConditioning", "pos": [-8118.1787109375, -554.025390625], "size": [270, 138], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 451}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 452}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 471}, {"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 489}, {"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 490}, {"localized_name": "噪波遮罩", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}], "outputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "links": [453]}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "links": [454]}, {"localized_name": "Latent", "name": "latent", "type": "LATENT", "links": [455]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 318, "type": "VAELoader", "pos": [-8492.001953125, -856.8048095703125], "size": [270, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [450, 471]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 309, "type": "BasicScheduler", "pos": [-8492.9541015625, -515.3865966796875], "size": [315, 106], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 439}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"label": "降噪", "localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Sigmas", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [444]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 0.8500000000000002]}, {"id": 332, "type": "DifferentialDiffusion", "pos": [-8148.5751953125, -790.0468139648438], "size": [216.77987670898438, 26], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 493}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [477]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 288, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [-10321.4287109375, -465.619140625], "size": [363.6851501464844, 330], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 407}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 408}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": 460}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [486]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": [458]}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "None", 948, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 326, "type": "ShowText|pysssss", "pos": [-8577.2177734375, -180.28843688964844], "size": [392.3556213378906, 118.68616485595703], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 467}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [468]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": []}, {"id": 307, "type": "PreviewImage", "pos": [-7367.419921875, -698.1146240234375], "size": [578.784423828125, 555.8528442382812], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 438}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 328, "type": "CR Text Concatenate", "pos": [-8143.90869140625, -191.1977081298828], "size": [270, 78], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "text1", "name": "text1", "shape": 7, "type": "STRING", "link": 468}, {"localized_name": "text2", "name": "text2", "shape": 7, "type": "STRING", "link": 469}, {"localized_name": "separator", "name": "separator", "shape": 7, "type": "STRING", "widget": {"name": "separator"}, "link": null}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [470]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": [""]}, {"id": 322, "type": "PrimitiveInt", "pos": [-10227.341796875, 1.7208194732666016], "size": [270, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "INT", "type": "INT", "links": [460, 461, 462]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PrimitiveInt"}, "widgets_values": [1344, "fixed"]}, {"id": 321, "type": "GrowMask", "pos": [-10007.884765625, -913.2828979492188], "size": [270, 82], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 458}, {"localized_name": "扩展", "name": "expand", "type": "INT", "widget": {"name": "expand"}, "link": null}, {"localized_name": "倒角", "name": "tapered_corners", "type": "BOOLEAN", "widget": {"name": "tapered_corners"}, "link": null}], "outputs": [{"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": [459, 488]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "GrowMask"}, "widgets_values": [15, true]}, {"id": 290, "type": "InpaintCropImproved", "pos": [-9767.1015625, -712.2794189453125], "size": [348.095703125, 626], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 486}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 459}, {"localized_name": "optional_context_mask", "name": "optional_context_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "downscale_algorithm", "name": "downscale_algorithm", "type": "COMBO", "widget": {"name": "downscale_algorithm"}, "link": null}, {"localized_name": "upscale_algorithm", "name": "upscale_algorithm", "type": "COMBO", "widget": {"name": "upscale_algorithm"}, "link": null}, {"localized_name": "preresize", "name": "preresize", "type": "BOOLEAN", "widget": {"name": "preresize"}, "link": null}, {"localized_name": "preresize_mode", "name": "preresize_mode", "type": "COMBO", "widget": {"name": "preresize_mode"}, "link": null}, {"localized_name": "preresize_min_width", "name": "preresize_min_width", "type": "INT", "widget": {"name": "preresize_min_width"}, "link": null}, {"localized_name": "preresize_min_height", "name": "preresize_min_height", "type": "INT", "widget": {"name": "preresize_min_height"}, "link": null}, {"localized_name": "preresize_max_width", "name": "preresize_max_width", "type": "INT", "widget": {"name": "preresize_max_width"}, "link": null}, {"localized_name": "preresize_max_height", "name": "preresize_max_height", "type": "INT", "widget": {"name": "preresize_max_height"}, "link": null}, {"localized_name": "mask_fill_holes", "name": "mask_fill_holes", "type": "BOOLEAN", "widget": {"name": "mask_fill_holes"}, "link": null}, {"localized_name": "mask_expand_pixels", "name": "mask_expand_pixels", "type": "INT", "widget": {"name": "mask_expand_pixels"}, "link": null}, {"localized_name": "mask_invert", "name": "mask_invert", "type": "BOOLEAN", "widget": {"name": "mask_invert"}, "link": null}, {"localized_name": "mask_blend_pixels", "name": "mask_blend_pixels", "type": "INT", "widget": {"name": "mask_blend_pixels"}, "link": null}, {"localized_name": "mask_hipass_filter", "name": "mask_hipass_filter", "type": "FLOAT", "widget": {"name": "mask_hipass_filter"}, "link": null}, {"localized_name": "extend_for_outpainting", "name": "extend_for_outpainting", "type": "BOOLEAN", "widget": {"name": "extend_for_outpainting"}, "link": null}, {"localized_name": "extend_up_factor", "name": "extend_up_factor", "type": "FLOAT", "widget": {"name": "extend_up_factor"}, "link": null}, {"localized_name": "extend_down_factor", "name": "extend_down_factor", "type": "FLOAT", "widget": {"name": "extend_down_factor"}, "link": null}, {"localized_name": "extend_left_factor", "name": "extend_left_factor", "type": "FLOAT", "widget": {"name": "extend_left_factor"}, "link": null}, {"localized_name": "extend_right_factor", "name": "extend_right_factor", "type": "FLOAT", "widget": {"name": "extend_right_factor"}, "link": null}, {"localized_name": "context_from_mask_extend_factor", "name": "context_from_mask_extend_factor", "type": "FLOAT", "widget": {"name": "context_from_mask_extend_factor"}, "link": null}, {"localized_name": "output_resize_to_target_size", "name": "output_resize_to_target_size", "type": "BOOLEAN", "widget": {"name": "output_resize_to_target_size"}, "link": null}, {"localized_name": "output_target_width", "name": "output_target_width", "type": "INT", "widget": {"name": "output_target_width"}, "link": 461}, {"localized_name": "output_target_height", "name": "output_target_height", "type": "INT", "widget": {"name": "output_target_height"}, "link": 462}, {"localized_name": "output_padding", "name": "output_padding", "type": "COMBO", "widget": {"name": "output_padding"}, "link": null}], "outputs": [{"localized_name": "stitcher", "name": "stitcher", "type": "STITCHER", "links": []}, {"localized_name": "cropped_image", "name": "cropped_image", "type": "IMAGE", "links": [489]}, {"localized_name": "cropped_mask", "name": "cropped_mask", "type": "MASK", "links": [490]}], "properties": {"cnr_id": "comfyui-inpaint-cropand<PERSON>itch", "ver": "2.1.7", "Node name for S&R": "InpaintCropImproved"}, "widgets_values": ["bilinear", "bicubic", false, "ensure minimum resolution", 1024, 1024, 16384, 16384, true, 0, false, 32, 0.1, false, 1, 1, 1, 1, 1.2, false, 512, 512, "32"]}, {"id": 327, "type": "CR Text", "pos": [-8586.87890625, -2.222606897354126], "size": [400, 200], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [469]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["simple background"]}, {"id": 287, "type": "LoadImage", "pos": [-10672.619140625, -456.34423828125], "size": [270, 314], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [407, 491]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": [408]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-546642.200000003.png [input]", "image"]}, {"id": 310, "type": "SamplerCustom", "pos": [-7795.45654296875, -652.0460205078125], "size": [424.28216552734375, 547.2218017578125], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 477}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 453}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 454}, {"label": "采样器", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 443}, {"label": "Sigmas", "localized_name": "Sigmas", "name": "sigmas", "type": "SIGMAS", "link": 444}, {"label": "Latent", "localized_name": "Latent", "name": "latent_image", "type": "LATENT", "link": 455}, {"localized_name": "添加噪波", "name": "add_noise", "type": "BOOLEAN", "widget": {"name": "add_noise"}, "link": null}, {"localized_name": "噪波种子", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"label": "输出", "localized_name": "Latent", "name": "output", "type": "LATENT", "links": [449]}, {"label": "降噪输出", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 376923073380166, "randomize", 3.5]}, {"id": 308, "type": "UNETLoader", "pos": [-8514.6298828125, -356.74859619140625], "size": [315, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [439, 492]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "UNETLoader"}, "widgets_values": ["F.1-Fill-fp16_Inpaint&Outpaint_1.0.safetensors", "fp8_e4m3fn"]}, {"id": 325, "type": "LayerUtility: JoyCaption2", "pos": [-8912.5419921875, -224.81991577148438], "size": [303.7271423339844, 342], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 491}, {"localized_name": "额外选项", "name": "extra_options", "shape": 7, "type": "JoyCaption2ExtraOption", "link": null}, {"localized_name": "LLM模型", "name": "llm_model", "type": "COMBO", "widget": {"name": "llm_model"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "数据类型", "name": "dtype", "type": "COMBO", "widget": {"name": "dtype"}, "link": null}, {"localized_name": "VLM LoRA", "name": "vlm_lora", "type": "COMBO", "widget": {"name": "vlm_lora"}, "link": null}, {"localized_name": "字幕类型", "name": "caption_type", "type": "COMBO", "widget": {"name": "caption_type"}, "link": null}, {"localized_name": "字幕长度", "name": "caption_length", "type": "COMBO", "widget": {"name": "caption_length"}, "link": null}, {"localized_name": "用户提示", "name": "user_prompt", "type": "STRING", "widget": {"name": "user_prompt"}, "link": null}, {"localized_name": "最大新令牌数", "name": "max_new_tokens", "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "顶部P", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "温度", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}, {"localized_name": "使用全局模型", "name": "use_global_model", "type": "BOOLEAN", "widget": {"name": "use_global_model"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "shape": 6, "type": "STRING", "links": [467]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerUtility: JoyCaption2"}, "widgets_values": ["Orenguteng/Llama-3.1-8B-Lexi-Uncensored-V2", "cuda", "nf4", "text_model", "Descriptive", "any", "", 300, 0.9, 0.6, false, false], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 337, "type": "LoraLoaderModelOnly", "pos": [-8146.9404296875, -361.3819885253906], "size": [270, 82], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 492}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [493]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["removal_timestep_alpha-2-1740.safetensors", 1]}, {"id": 312, "type": "CLIPTextEncode", "pos": [-8953.5712890625, -492.8201599121094], "size": [400, 200], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 447}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [452]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 313, "type": "FluxGuidance", "pos": [-8497.9853515625, -628.6405639648438], "size": [315, 58], "flags": {"collapsed": false}, "order": 19, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 448}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [451]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 311, "type": "CLIPTextEncode", "pos": [-8956.2705078125, -749.171142578125], "size": [400, 200], "flags": {"collapsed": false}, "order": 18, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 446}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 470}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [448]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 314, "type": "DualCLIPLoader", "pos": [-9319.3154296875, -590.0062866210938], "size": [315, 130], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [446, 447]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}], "links": [[407, 287, 0, 288, 0, "IMAGE"], [408, 287, 1, 288, 1, "MASK"], [438, 317, 0, 307, 0, "IMAGE"], [439, 308, 0, 309, 0, "MODEL"], [443, 315, 0, 310, 3, "SAMPLER"], [444, 309, 0, 310, 4, "SIGMAS"], [446, 314, 0, 311, 0, "CLIP"], [447, 314, 0, 312, 0, "CLIP"], [448, 311, 0, 313, 0, "CONDITIONING"], [449, 310, 0, 317, 0, "LATENT"], [450, 318, 0, 317, 1, "VAE"], [451, 313, 0, 319, 0, "CONDITIONING"], [452, 312, 0, 319, 1, "CONDITIONING"], [453, 319, 0, 310, 1, "CONDITIONING"], [454, 319, 1, 310, 2, "CONDITIONING"], [455, 319, 2, 310, 5, "LATENT"], [458, 288, 1, 321, 0, "MASK"], [459, 321, 0, 290, 1, "MASK"], [460, 322, 0, 288, 9, "INT"], [461, 322, 0, 290, 23, "INT"], [462, 322, 0, 290, 24, "INT"], [467, 325, 0, 326, 0, "STRING"], [468, 326, 0, 328, 0, "STRING"], [469, 327, 0, 328, 1, "STRING"], [470, 328, 0, 311, 1, "STRING"], [471, 318, 0, 319, 2, "VAE"], [477, 332, 0, 310, 0, "MODEL"], [486, 288, 0, 290, 0, "IMAGE"], [488, 321, 0, 324, 0, "MASK"], [489, 290, 1, 319, 3, "IMAGE"], [490, 290, 2, 319, 4, "MASK"], [491, 287, 0, 325, 0, "IMAGE"], [492, 308, 0, 337, 0, "MODEL"], [493, 337, 0, 332, 0, "MODEL"]], "groups": [{"id": 3, "title": "Group", "bounding": [-9386.0693359375, -993.6643676757812, 2594.88671875, 1212.4803466796875], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6588450000000016, "offset": [11015.218381873117, 1097.5634092791756]}, "frontendVersion": "1.18.9", "0246.VERSION": [0, 0, 4], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}