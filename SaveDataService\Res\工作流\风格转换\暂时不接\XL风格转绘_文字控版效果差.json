{"id": "efd450dc-0ecd-428f-a213-c940c30fa4a4", "revision": 0, "last_node_id": 51, "last_link_id": 102, "nodes": [{"id": 12, "type": "CLIPTextEncode", "pos": [1800, 1370], "size": [260, 150], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 18}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [75]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["easynegative, badhandv4, (worst quality, low quality, normal quality), bad-artist, blurry, ugly, ((bad anatomy)),((bad hands)),((bad proportions)),((duplicate limbs)),((fused limbs)),((interlocking fingers)),((poorly drawn face))"], "color": "#322", "bgcolor": "#533"}, {"id": 11, "type": "CLIPTextEncode", "pos": [1800, 1250], "size": [260, 88], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 16}, {"label": "文本", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 88}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [74]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful scenery nature glass bottle landscape, , purple galaxy bottle,"], "color": "#232", "bgcolor": "#353"}, {"id": 34, "type": "KSamplerSelect", "pos": [2310, 1480], "size": [320, 60], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [60]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpmpp_2m"]}, {"id": 35, "type": "AlignYourStepsScheduler", "pos": [2310, 1580], "size": [320, 110], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "模型类型", "name": "model_type", "type": "COMBO", "widget": {"name": "model_type"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [61]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "AlignYourStepsScheduler"}, "widgets_values": ["SDXL", 14, 1]}, {"id": 16, "type": "VAEEncode", "pos": [2310, 1730], "size": [320, 50], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "图像", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 73}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 25}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [63]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEEncode"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 13, "type": "VAEDecode", "pos": [3180, 1210], "size": [140, 50], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 87}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 21}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [22]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 40, "type": "Note", "pos": [2290, 1880], "size": [380, 110], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["https://huggingface.co/2vXpSwA7/iroiro-lora/blob/main/test_controlnet2/CN-anytest_v4-marged.safetensors"], "color": "#432", "bgcolor": "#653"}, {"id": 39, "type": "Note", "pos": [859.0907592773438, 1836.36328125], "size": [380, 110], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["https://civitai.com/models/410737?modelVersionId=662070\n\n\nhttps://civitai.com/models/570618/gouache-illustration"], "color": "#432", "bgcolor": "#653"}, {"id": 18, "type": "ControlNetLoader", "pos": [2310, 1140], "size": [320, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"label": "ControlNet", "localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [76]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["CN-anytest_v4-marged.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 27, "type": "LoadImage", "pos": [1434.7869873046875, 508.03045654296875], "size": [400, 510], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [71]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["image.webp", "image"]}, {"id": 33, "type": "SamplerCustom", "pos": [2683.33349609375, 1256.666748046875], "size": [360, 440], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 102}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 85}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 86}, {"localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 60}, {"localized_name": "Sigmas", "name": "sigmas", "type": "SIGMAS", "link": 61}, {"localized_name": "Latent", "name": "latent_image", "type": "LATENT", "link": 63}, {"localized_name": "添加噪波", "name": "add_noise", "type": "BOOLEAN", "widget": {"name": "add_noise"}, "link": null}, {"localized_name": "噪波种子", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "output", "type": "LATENT", "slot_index": 0, "links": [87]}, {"localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "slot_index": 1, "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 1122577831189984, "randomize", 6]}, {"id": 38, "type": "ControlNetApplyAdvanced", "pos": [2310, 1240], "size": [320, 190], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 74}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 75}, {"localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 76}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 77}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": 78}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [85]}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [86]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0.7000000000000001, 0, 1]}, {"id": 48, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [606.0924072265625, 1106.5955810546875], "size": [270, 78], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "JANUS_MODEL", "links": [99]}, {"localized_name": "processor", "name": "processor", "type": "JANUS_PROCESSOR", "links": [101]}], "properties": {"cnr_id": "janus-pro", "ver": "4400129e5c33664ae6e927162a39ba4116f44b8b", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["deepseek-ai/Janus-Pro-7B"]}, {"id": 28, "type": "ImageScaleToTotalPixels", "pos": [602.8221435546875, 974.8075561523438], "size": [315, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 71}, {"localized_name": "缩放算法", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "像素数量", "name": "megapixels", "type": "FLOAT", "widget": {"name": "megapixels"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [73, 77, 96]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageScaleToTotalPixels"}, "widgets_values": ["lanc<PERSON>s", 1]}, {"id": 24, "type": "easy promptConcat", "pos": [1466.5283203125, 1393.663818359375], "size": [210, 80], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "提示词1", "localized_name": "提示词1", "name": "prompt1", "shape": 7, "type": "STRING", "link": 36}, {"label": "提示词2", "localized_name": "提示词2", "name": "prompt2", "shape": 7, "type": "STRING", "link": 98}, {"localized_name": "间隔符", "name": "separator", "shape": 7, "type": "STRING", "widget": {"name": "separator"}, "link": null}], "outputs": [{"label": "提示词", "localized_name": "提示词", "name": "prompt", "type": "STRING", "slot_index": 0, "links": [88]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy promptConcat"}, "widgets_values": [""], "color": "#233", "bgcolor": "#355"}, {"id": 21, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [906.7493286132812, 1601.4329833984375], "size": [310, 130], "flags": {}, "order": 10, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 54}, {"label": "CLIP", "localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 55}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [102]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [16, 18]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["sdxl\\sfch.safetensors", 1, 1]}, {"id": 10, "type": "CheckpointLoaderSimple", "pos": [506.6666564941406, 1625.4544677734375], "size": [280, 100], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [54]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [55]}, {"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [21, 25, 78]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["waiNSFWIllustrious_v140.safetensors"], "color": "#432", "bgcolor": "#653"}, {"id": 47, "type": "ShowText|pysssss", "pos": [953.6151733398438, 1134.828857421875], "size": [382.9752197265625, 250.10464477539062], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 97}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [98]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["The girl in the image has long, straight white hair that falls just past her shoulders. She has a pair of red eyes, which stand out against her pale complexion. She is wearing a blue coat with a white fur collar and cuffs, giving it a stylish and warm appearance. Underneath the coat, she has on a brown turtleneck sweater. Her outfit is completed with a white skirt that has a ruffled hem. She also has a red ribbon tied into her hair on the right side. The overall look is both cute and elegant, suitable for cooler weather."]}, {"id": 25, "type": "easy positive", "pos": [1025.0247802734375, 1458.2178955078125], "size": [240, 90], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "positive", "name": "positive", "type": "STRING", "widget": {"name": "positive"}, "link": null}], "outputs": [{"label": "正面提示词", "localized_name": "正面提示词", "name": "positive", "type": "STRING", "slot_index": 0, "links": [36]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy positive"}, "widgets_values": ["sfch, watercolor style,"], "color": "#232", "bgcolor": "#353"}, {"id": 46, "type": "JanusImageUnderstanding", "pos": [512.137939453125, 1231.7711181640625], "size": [400, 248], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "JANUS_MODEL", "link": 99}, {"localized_name": "processor", "name": "processor", "type": "JANUS_PROCESSOR", "link": 101}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 96}, {"localized_name": "question", "name": "question", "type": "STRING", "widget": {"name": "question"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "temperature", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "top_p", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "max_new_tokens", "name": "max_new_tokens", "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [97]}], "properties": {"cnr_id": "janus-pro", "ver": "4400129e5c33664ae6e927162a39ba4116f44b8b", "Node name for S&R": "JanusImageUnderstanding"}, "widgets_values": ["Describe this girl in detail.", 236656671829711, "fixed", 0.1, 0.95, 512]}, {"id": 14, "type": "SaveImage", "pos": [1872.178955078125, 527.365966796875], "size": [390, 510], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 22}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}], "links": [[16, 21, 1, 11, 0, "CLIP"], [18, 21, 1, 12, 0, "CLIP"], [21, 10, 2, 13, 1, "VAE"], [22, 13, 0, 14, 0, "IMAGE"], [25, 10, 2, 16, 1, "VAE"], [36, 25, 0, 24, 0, "STRING"], [54, 10, 0, 21, 0, "MODEL"], [55, 10, 1, 21, 1, "CLIP"], [60, 34, 0, 33, 3, "SAMPLER"], [61, 35, 0, 33, 4, "SIGMAS"], [63, 16, 0, 33, 5, "LATENT"], [71, 27, 0, 28, 0, "IMAGE"], [73, 28, 0, 16, 0, "IMAGE"], [74, 11, 0, 38, 0, "CONDITIONING"], [75, 12, 0, 38, 1, "CONDITIONING"], [76, 18, 0, 38, 2, "CONTROL_NET"], [77, 28, 0, 38, 3, "IMAGE"], [78, 10, 2, 38, 4, "VAE"], [85, 38, 0, 33, 1, "CONDITIONING"], [86, 38, 1, 33, 2, "CONDITIONING"], [87, 33, 0, 13, 0, "LATENT"], [88, 24, 0, 11, 1, "STRING"], [96, 28, 0, 46, 2, "IMAGE"], [97, 46, 0, 47, 0, "STRING"], [98, 47, 0, 24, 1, "STRING"], [99, 48, 0, 46, 0, "JANUS_MODEL"], [101, 48, 1, 46, 1, "JANUS_PROCESSOR"], [102, 21, 0, 33, 0, "MODEL"]], "groups": [{"id": 1, "title": "Group", "bounding": [2270, 1050, 1137, 746], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"id": 2, "title": "Group", "bounding": [1390, 1050, 860, 744], "color": "#8A8", "font_size": 24, "flags": {}}, {"id": 3, "title": "Group", "bounding": [400, 640, 960, 1160], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.385543289429532, "offset": [-20.549475028287517, -429.7747117409799]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}