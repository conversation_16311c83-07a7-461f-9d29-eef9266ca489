{"id": "206247b6-9fec-4ed2-8927-e4f388c674d4", "revision": 0, "last_node_id": 86, "last_link_id": 122, "nodes": [{"id": 46, "type": "WanVideoTextEmbedBridge", "pos": [1015.870849609375, 941.0142822265625], "size": [315, 46], "flags": {}, "order": 32, "mode": 2, "inputs": [{"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 54}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 55}], "outputs": [{"localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "links": null}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTextEmbedBridge"}, "widgets_values": []}, {"id": 50, "type": "CLIPTextEncode", "pos": [565.871337890625, 1201.014404296875], "size": [400, 200], "flags": {}, "order": 26, "mode": 2, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 53}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [55]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"]}, {"id": 48, "type": "CLIPLoader", "pos": [205.87127685546875, 951.0142822265625], "size": [315, 106], "flags": {}, "order": 0, "mode": 2, "inputs": [{"localized_name": "CLIP名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [52, 53]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CLIPLoader"}, "widgets_values": ["umt5_xxl_fp16.safetensors", "wan", "default"]}, {"id": 49, "type": "CLIPTextEncode", "pos": [565.871337890625, 951.0142822265625], "size": [400, 200], "flags": {}, "order": 25, "mode": 2, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 52}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [54]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["high quality nature video featuring a red panda balancing on a bamboo stem while a bird lands on it's head, on the background there is a waterfall"]}, {"id": 42, "type": "Note", "pos": [-1010, -1250], "size": [314.96246337890625, 152.77333068847656], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Adjust the blocks to swap based on your VRAM, this is a tradeoff between speed and memory usage.\n\nAlternatively there's option to use VRAM management introduced in DiffSynt-Studios. This is usually slower, but saves even more VRAM compared to BlockSwap"], "color": "#432", "bgcolor": "#653"}, {"id": 45, "type": "WanVideoVRAMManagement", "pos": [-640, -1070], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "offload_percent", "name": "offload_percent", "type": "FLOAT", "widget": {"name": "offload_percent"}, "link": null}], "outputs": [{"localized_name": "vram_management_args", "name": "vram_management_args", "type": "VRAM_MANAGEMENTARGS", "links": []}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoVRAMManagement"}, "widgets_values": [1], "color": "#223", "bgcolor": "#335"}, {"id": 33, "type": "Note", "pos": [-260, -1640], "size": [359.0753479003906, 88], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Models:\nhttps://huggingface.co/Kijai/WanVideo_comfy/tree/main"], "color": "#432", "bgcolor": "#653"}, {"id": 51, "type": "Note", "pos": [235.87120056152344, 781.0138549804688], "size": [253.16725158691406, 88], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["You can also use native ComfyUI text encoding with these nodes instead of the original, the models are node specific and can't otherwise be mixed."], "color": "#432", "bgcolor": "#653"}, {"id": 44, "type": "Note", "pos": [-1050, -1540], "size": [303.0501403808594, 88], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["If you have Triton installed, connect this for ~30% speed increase"], "color": "#432", "bgcolor": "#653"}, {"id": 59, "type": "CLIPVisionLoader", "pos": [-990, -160], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [70]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#233", "bgcolor": "#355"}, {"id": 28, "type": "WanVideoDecode", "pos": [1688.0194091796875, -647.6461791992188], "size": [315, 174], "flags": {}, "order": 39, "mode": 0, "inputs": [{"localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 118}, {"localized_name": "samples", "name": "samples", "type": "LATENT", "link": 90}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}], "outputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [36]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoDecode"}, "widgets_values": [false, 272, 272, 144, 128], "color": "#322", "bgcolor": "#533"}, {"id": 65, "type": "WanVideoClipVisionEncode", "pos": [-590, -160], "size": [327.5999755859375, 262], "flags": {}, "order": 34, "mode": 0, "inputs": [{"localized_name": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 70}, {"localized_name": "image_1", "name": "image_1", "type": "IMAGE", "link": 107}, {"localized_name": "image_2", "name": "image_2", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "negative_image", "name": "negative_image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "strength_1", "name": "strength_1", "type": "FLOAT", "widget": {"name": "strength_1"}, "link": null}, {"localized_name": "strength_2", "name": "strength_2", "type": "FLOAT", "widget": {"name": "strength_2"}, "link": null}, {"localized_name": "crop", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}, {"localized_name": "combine_embeds", "name": "combine_embeds", "type": "COMBO", "widget": {"name": "combine_embeds"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}, {"localized_name": "tiles", "name": "tiles", "shape": 7, "type": "INT", "widget": {"name": "tiles"}, "link": null}, {"localized_name": "ratio", "name": "ratio", "shape": 7, "type": "FLOAT", "widget": {"name": "ratio"}, "link": null}], "outputs": [{"localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_CLIPEMBEDS", "links": [82]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoClipVisionEncode"}, "widgets_values": [1, 1, "center", "average", true, 0, 0.20000000000000004], "color": "#233", "bgcolor": "#355"}, {"id": 69, "type": "WanVideoSampler", "pos": [1315.2401123046875, -401.48028564453125], "size": [317.4000244140625, 869.4000244140625], "flags": {}, "order": 38, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 85}, {"localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 86}, {"localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 87}, {"localized_name": "samples", "name": "samples", "shape": 7, "type": "LATENT", "link": null}, {"localized_name": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS", "link": null}, {"localized_name": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT", "link": null}, {"localized_name": "teacache_args", "name": "teacache_args", "shape": 7, "type": "TEACACHEARGS", "link": 89}, {"localized_name": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS", "link": null}, {"localized_name": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS", "link": null}, {"localized_name": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS", "link": null}, {"localized_name": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS", "link": null}, {"localized_name": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS", "link": null}, {"localized_name": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE", "link": null}, {"localized_name": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS", "link": 101}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 115}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "riflex_freq_index", "name": "riflex_freq_index", "type": "INT", "widget": {"name": "riflex_freq_index"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}, {"localized_name": "batched_cfg", "name": "batched_cfg", "shape": 7, "type": "BOOLEAN", "widget": {"name": "batched_cfg"}, "link": null}, {"localized_name": "rope_function", "name": "rope_function", "shape": 7, "type": "COMBO", "widget": {"name": "rope_function"}, "link": null}], "outputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "links": [90]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "df95c85283d7625fbdf664d0133a2e1c114ba14a", "Node name for S&R": "WanVideoSampler"}, "widgets_values": [30, 5.000000000000001, 5, 0, "fixed", true, "unipc", 0, 1, false, "comfy"]}, {"id": 35, "type": "WanVideoTorchCompileSettings", "pos": [-710, -1540], "size": [421.6000061035156, 202], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "backend", "name": "backend", "type": "COMBO", "widget": {"name": "backend"}, "link": null}, {"localized_name": "fullgraph", "name": "fullgraph", "type": "BOOLEAN", "widget": {"name": "fullgraph"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "dynamic", "name": "dynamic", "type": "BOOLEAN", "widget": {"name": "dynamic"}, "link": null}, {"localized_name": "dynamo_cache_size_limit", "name": "dynamo_cache_size_limit", "type": "INT", "widget": {"name": "dynamo_cache_size_limit"}, "link": null}, {"localized_name": "compile_transformer_blocks_only", "name": "compile_transformer_blocks_only", "type": "BOOLEAN", "widget": {"name": "compile_transformer_blocks_only"}, "link": null}, {"localized_name": "dynamo_recompile_limit", "name": "dynamo_recompile_limit", "shape": 7, "type": "INT", "widget": {"name": "dynamo_recompile_limit"}, "link": null}], "outputs": [{"localized_name": "torch_compile_args", "name": "torch_compile_args", "type": "WANCOMPILEARGS", "slot_index": 0, "links": []}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTorchCompileSettings"}, "widgets_values": ["inductor", false, "default", false, 64, true, 128], "color": "#223", "bgcolor": "#335"}, {"id": 77, "type": "Note", "pos": [-990, -820], "size": [373.65032958984375, 139.77639770507812], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Wave2vec modele is autodownloaded from:\n\nhttps://huggingface.co/facebook/wav2vec2-base-960h\n\nto:\n\nComfyUI/models/transformers/facebook/wav2vec2-base-960h\n\nThe .safetensor file + config files"], "color": "#432", "bgcolor": "#653"}, {"id": 80, "type": "PreviewImage", "pos": [-5.626028060913086, 274.6432800292969], "size": [210, 246], "flags": {}, "order": 35, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 116}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 81, "type": "SetNode", "pos": [-206.83546447753906, -771.7630004882812], "size": [210, 60], "flags": {"collapsed": true}, "order": 29, "mode": 0, "inputs": [{"name": "WANVAE", "type": "WANVAE", "link": 117}], "outputs": [{"name": "*", "type": "*", "links": null}], "title": "Set_WanVAE", "properties": {"previousName": "WanVAE"}, "widgets_values": ["WanVAE"], "color": "#322", "bgcolor": "#533"}, {"id": 82, "type": "GetNode", "pos": [1688.8663330078125, -708.3416748046875], "size": [210, 60], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "WANVAE", "type": "WANVAE", "links": [118]}], "title": "Get_WanVAE", "properties": {}, "widgets_values": ["WanVAE"], "color": "#322", "bgcolor": "#533"}, {"id": 63, "type": "WanVideoImageToVideoEncode", "pos": [-8.957036018371582, -156.54031372070312], "size": [352.79998779296875, 346], "flags": {}, "order": 37, "mode": 0, "inputs": [{"localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 119}, {"localized_name": "clip_embeds", "name": "clip_embeds", "shape": 7, "type": "WANVIDIMAGE_CLIPEMBEDS", "link": 82}, {"localized_name": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 108}, {"localized_name": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS", "link": null}, {"localized_name": "temporal_mask", "name": "temporal_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "extra_latents", "name": "extra_latents", "shape": 7, "type": "LATENT", "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 109}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 110}, {"localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 111}, {"localized_name": "noise_aug_strength", "name": "noise_aug_strength", "type": "FLOAT", "widget": {"name": "noise_aug_strength"}, "link": null}, {"localized_name": "start_latent_strength", "name": "start_latent_strength", "type": "FLOAT", "widget": {"name": "start_latent_strength"}, "link": null}, {"localized_name": "end_latent_strength", "name": "end_latent_strength", "type": "FLOAT", "widget": {"name": "end_latent_strength"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}, {"localized_name": "fun_or_fl2v_model", "name": "fun_or_fl2v_model", "shape": 7, "type": "BOOLEAN", "widget": {"name": "fun_or_fl2v_model"}, "link": null}], "outputs": [{"localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [87]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoImageToVideoEncode"}, "widgets_values": [832, 480, 81, 0.030000000000000006, 1, 1, true, false], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 83, "type": "GetNode", "pos": [-171.16091918945312, -154.3962860107422], "size": [210, 34], "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "WANVAE", "type": "WANVAE", "links": [119]}], "title": "Get_WanVAE", "properties": {}, "widgets_values": ["WanVAE"], "color": "#322", "bgcolor": "#533"}, {"id": 84, "type": "SetNode", "pos": [-589.344970703125, -313.9405517578125], "size": [210, 60], "flags": {"collapsed": true}, "order": 28, "mode": 0, "inputs": [{"name": "AUDIO", "type": "AUDIO", "link": 120}], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [121]}], "title": "Set_InputAudio", "properties": {"previousName": "InputAudio"}, "widgets_values": ["InputAudio"], "color": "#323", "bgcolor": "#535"}, {"id": 85, "type": "GetNode", "pos": [2110.023193359375, -866.8945922851562], "size": [210, 60], "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [122]}], "title": "Get_InputAudio", "properties": {}, "widgets_values": ["InputAudio"], "color": "#323", "bgcolor": "#535"}, {"id": 16, "type": "WanVideoTextEncode", "pos": [688.0745849609375, -439.3515625], "size": [420.30511474609375, 261.5306701660156], "flags": {}, "order": 36, "mode": 0, "inputs": [{"localized_name": "t5", "name": "t5", "type": "WANTEXTENCODER", "link": 15}, {"localized_name": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL", "link": 79}, {"localized_name": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": null}, {"localized_name": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "shape": 7, "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}], "outputs": [{"localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [86]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTextEncode"}, "widgets_values": ["A woman is talking.", "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", true], "color": "#332922", "bgcolor": "#593930"}, {"id": 75, "type": "INTConstant", "pos": [356.9280700683594, -347.07037353515625], "size": [210, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "value", "name": "value", "type": "INT", "links": [111, 112]}], "title": "Frames", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "c3dc82108a2a86c17094107ead61d63f8c76200e", "Node name for S&R": "INTConstant"}, "widgets_values": [81], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 79, "type": "INTConstant", "pos": [362.801513671875, -481.2966003417969], "size": [210, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "value", "name": "value", "type": "INT", "links": [114, 115]}], "title": "Steps", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "c3dc82108a2a86c17094107ead61d63f8c76200e", "Node name for S&R": "INTConstant"}, "widgets_values": [30], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 36, "type": "Note", "pos": [-270, -1500], "size": [374.3061828613281, 171.9547576904297], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["fp8_fast seems to cause huge quality degradation\n\nfp_16_fast enables \"Full FP16 Accmumulation in FP16 GEMMs\" feature available in the very latest pytorch nightly, this is around 20% speed boost. \n\nSageattn if you have it installed can be used for almost double inference speed"], "color": "#432", "bgcolor": "#653"}, {"id": 39, "type": "WanVideoBlockSwap", "pos": [-639.009033203125, -1276.7559814453125], "size": [315, 154], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "blocks_to_swap", "name": "blocks_to_swap", "type": "INT", "widget": {"name": "blocks_to_swap"}, "link": null}, {"localized_name": "offload_img_emb", "name": "offload_img_emb", "type": "BOOLEAN", "widget": {"name": "offload_img_emb"}, "link": null}, {"localized_name": "offload_txt_emb", "name": "offload_txt_emb", "type": "BOOLEAN", "widget": {"name": "offload_txt_emb"}, "link": null}, {"localized_name": "use_non_blocking", "name": "use_non_blocking", "shape": 7, "type": "BOOLEAN", "widget": {"name": "use_non_blocking"}, "link": null}, {"localized_name": "vace_blocks_to_swap", "name": "vace_blocks_to_swap", "shape": 7, "type": "INT", "widget": {"name": "vace_blocks_to_swap"}, "link": null}], "outputs": [{"localized_name": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "slot_index": 0, "links": [96]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoBlockSwap"}, "widgets_values": [15, false, false, true, 0], "color": "#223", "bgcolor": "#335"}, {"id": 73, "type": "FantasyTalkingWav2VecEmbeds", "pos": [579.4556884765625, -113.97596740722656], "size": [531.5999755859375, 170], "flags": {}, "order": 33, "mode": 0, "inputs": [{"localized_name": "wav2vec_model", "name": "wav2vec_model", "type": "WAV2VECMODEL", "link": 99}, {"localized_name": "fantasytalking_model", "name": "fantasytalking_model", "type": "FANTASYTALKINGMODEL", "link": 100}, {"localized_name": "audio", "name": "audio", "type": "AUDIO", "link": 121}, {"localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 112}, {"localized_name": "fps", "name": "fps", "type": "FLOAT", "widget": {"name": "fps"}, "link": null}, {"localized_name": "audio_scale", "name": "audio_scale", "type": "FLOAT", "widget": {"name": "audio_scale"}, "link": null}, {"localized_name": "audio_cfg_scale", "name": "audio_cfg_scale", "type": "FLOAT", "widget": {"name": "audio_cfg_scale"}, "link": 113}], "outputs": [{"localized_name": "fantasytalking_embeds", "name": "fantasytalking_embeds", "type": "FANTASYTALKING_EMBEDS", "links": [101]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "df95c85283d7625fbdf664d0133a2e1c114ba14a", "Node name for S&R": "FantasyTalkingWav2VecEmbeds"}, "widgets_values": [81, 23, 1, 1], "color": "#323", "bgcolor": "#535"}, {"id": 74, "type": "ImageResizeKJv2", "pos": [-570, 180], "size": [315, 242], "flags": {}, "order": 30, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 104}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "keep_proportion", "name": "keep_proportion", "type": "COMBO", "widget": {"name": "keep_proportion"}, "link": null}, {"localized_name": "pad_color", "name": "pad_color", "type": "STRING", "widget": {"name": "pad_color"}, "link": null}, {"localized_name": "crop_position", "name": "crop_position", "type": "COMBO", "widget": {"name": "crop_position"}, "link": null}, {"localized_name": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [107, 108, 116]}, {"localized_name": "width", "name": "width", "type": "INT", "links": [109]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [110]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "c3dc82108a2a86c17094107ead61d63f8c76200e", "Node name for S&R": "ImageResizeKJv2"}, "widgets_values": [512, 512, "lanc<PERSON>s", "crop", "0, 0, 0", "center", 2], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 30, "type": "VHS_VideoCombine", "pos": [2106.010009765625, -818.290283203125], "size": [799.9151611328125, 1127.9151611328125], "flags": {}, "order": 40, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 36}, {"localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": 122}, {"localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 23, "loop_count": 0, "filename_prefix": "WanVideoWrapper_I2V_FantasyTalking", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "WanVideoWrapper_I2V_FantasyTalking_00001-audio.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4", "frame_rate": 23, "workflow": "WanVideoWrapper_I2V_FantasyTalking_00001.png", "fullpath": "H:\\ComfyUI_WAN2.1\\ComfyUI\\output\\WanVideoWrapper_I2V_FantasyTalking_00001-audio.mp4"}}}, "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 52, "type": "WanVideoTeaCache", "pos": [1307.4208984375, -664.744384765625], "size": [315, 178], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "rel_l1_thresh", "name": "rel_l1_thresh", "type": "FLOAT", "widget": {"name": "rel_l1_thresh"}, "link": null}, {"localized_name": "start_step", "name": "start_step", "type": "INT", "widget": {"name": "start_step"}, "link": null}, {"localized_name": "end_step", "name": "end_step", "type": "INT", "widget": {"name": "end_step"}, "link": null}, {"localized_name": "cache_device", "name": "cache_device", "type": "COMBO", "widget": {"name": "cache_device"}, "link": null}, {"localized_name": "use_coefficients", "name": "use_coefficients", "type": "BOOLEAN", "widget": {"name": "use_coefficients"}, "link": null}, {"localized_name": "mode", "name": "mode", "shape": 7, "type": "COMBO", "widget": {"name": "mode"}, "link": null}], "outputs": [{"localized_name": "teacache_args", "name": "teacache_args", "type": "TEACACHEARGS", "links": [89]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTeaCache"}, "widgets_values": [0.225, 6, -1, "offload_device", "true", "e"]}, {"id": 78, "type": "CreateCFGScheduleFloatList", "pos": [690.325927734375, -689.5537719726562], "size": [403.1999816894531, 178], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 114}, {"localized_name": "cfg_scale_start", "name": "cfg_scale_start", "type": "FLOAT", "widget": {"name": "cfg_scale_start"}, "link": null}, {"localized_name": "cfg_scale_end", "name": "cfg_scale_end", "type": "FLOAT", "widget": {"name": "cfg_scale_end"}, "link": null}, {"localized_name": "interpolation", "name": "interpolation", "type": "COMBO", "widget": {"name": "interpolation"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"localized_name": "float_list", "name": "float_list", "type": "FLOAT", "links": [113]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e3afc7fc758add9ba0ca7e6e219c30f312758484", "Node name for S&R": "CreateCFGScheduleFloatList"}, "widgets_values": [30, 5, 5, "linear", 0, 0.1]}, {"id": 86, "type": "Note", "pos": [686.342529296875, -834.5337524414062], "size": [410.2611389160156, 90.62958526611328], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Applying the audio_cfg only at the beginning can give most of it's benefit while not slowing down rest of the generation."], "color": "#432", "bgcolor": "#653"}, {"id": 72, "type": "LoadAudio", "pos": [-982.36962890625, -373.3668212890625], "size": [315, 136], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "音频", "name": "audio", "type": "COMBO", "widget": {"name": "audio"}, "link": null}, {"localized_name": "音频UI", "name": "audioUI", "type": "AUDIO_UI", "widget": {"name": "audioUI"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "AUDIOUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "音频", "name": "AUDIO", "type": "AUDIO", "links": [120]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadAudio"}, "widgets_values": ["我有几只小猫咪 (7).mp3", null, null], "color": "#323", "bgcolor": "#535"}, {"id": 22, "type": "WanVideoModelLoader", "pos": [-280, -1270], "size": [477.4410095214844, 254], "flags": {}, "order": 31, "mode": 0, "inputs": [{"localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS", "link": null}, {"localized_name": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": 96}, {"localized_name": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": null}, {"localized_name": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS", "link": null}, {"localized_name": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH", "link": null}, {"localized_name": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL", "link": 84}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [79, 85]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoModelLoader"}, "widgets_values": ["Wan2_1-I2V-14B-480P_fp8_e4m3fn.safetensors", "fp16", "fp8_e4m3fn", "offload_device", "sdpa"], "color": "#223", "bgcolor": "#335"}, {"id": 38, "type": "WanVideoVAELoader", "pos": [-610, -940], "size": [372.7727966308594, 82], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "shape": 7, "type": "COMBO", "widget": {"name": "precision"}, "link": null}], "outputs": [{"localized_name": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [117]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoVAELoader"}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#322", "bgcolor": "#533"}, {"id": 76, "type": "<PERSON>downNote", "pos": [-1070, -630], "size": [446.07086181640625, 88], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [], "title": "Model link", "properties": {}, "widgets_values": ["[https://huggingface.co/Kijai/WanVideo_comfy/blob/main/fantasytalking_fp16.safetensors](https://huggingface.co/Kijai/WanVideo_comfy/blob/main/fantasytalking_fp16.safetensors)"], "color": "#432", "bgcolor": "#653"}, {"id": 58, "type": "LoadImage", "pos": [-1060, 180], "size": [413.10479736328125, 498.3180847167969], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [104]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "LoadImage"}, "widgets_values": ["7493314fgy1hbji4ks6rdj22tc480kjt.jpg", "image"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 71, "type": "DownloadAndLoadWav2VecModel", "pos": [-600, -790], "size": [355.20001220703125, 106], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}], "outputs": [{"localized_name": "wav2vec_model", "name": "wav2vec_model", "type": "WAV2VECMODEL", "links": [99]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "df95c85283d7625fbdf664d0133a2e1c114ba14a", "Node name for S&R": "DownloadAndLoadWav2VecModel"}, "widgets_values": ["facebook/wav2vec2-base-960h", "fp16", "main_device"], "color": "#323", "bgcolor": "#535"}, {"id": 11, "type": "LoadWanVideoT5TextEncoder", "pos": [-210, -950], "size": [377.1661376953125, 130], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "shape": 7, "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "shape": 7, "type": "COMBO", "widget": {"name": "quantization"}, "link": null}], "outputs": [{"localized_name": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [15]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "LoadWanVideoT5TextEncoder"}, "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "disabled"], "color": "#332922", "bgcolor": "#593930"}, {"id": 68, "type": "FantasyTalkingModelLoader", "pos": [-600, -620], "size": [340.20001220703125, 82], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "FANTASYTALKINGMODEL", "links": [84, 100]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "df95c85283d7625fbdf664d0133a2e1c114ba14a", "Node name for S&R": "FantasyTalkingModelLoader"}, "widgets_values": ["fantasytalking_fp16.safetensors", "fp16"], "color": "#323", "bgcolor": "#535"}], "links": [[15, 11, 0, 16, 0, "WANTEXTENCODER"], [36, 28, 0, 30, 0, "IMAGE"], [52, 48, 0, 49, 0, "CLIP"], [53, 48, 0, 50, 0, "CLIP"], [54, 49, 0, 46, 0, "CONDITIONING"], [55, 50, 0, 46, 1, "CONDITIONING"], [70, 59, 0, 65, 0, "CLIP_VISION"], [79, 22, 0, 16, 1, "WANVIDEOMODEL"], [82, 65, 0, 63, 1, "WANVIDIMAGE_CLIPEMBEDS"], [84, 68, 0, 22, 5, "FANTASYTALKINGMODEL"], [85, 22, 0, 69, 0, "WANVIDEOMODEL"], [86, 16, 0, 69, 1, "WANVIDEOTEXTEMBEDS"], [87, 63, 0, 69, 2, "WANVIDIMAGE_EMBEDS"], [89, 52, 0, 69, 6, "TEACACHEARGS"], [90, 69, 0, 28, 1, "LATENT"], [96, 39, 0, 22, 1, "BLOCKSWAPARGS"], [99, 71, 0, 73, 0, "WAV2VECMODEL"], [100, 68, 0, 73, 1, "FANTASYTALKINGMODEL"], [101, 73, 0, 69, 13, "FANTASYTALKING_EMBEDS"], [104, 58, 0, 74, 0, "IMAGE"], [107, 74, 0, 65, 1, "IMAGE"], [108, 74, 0, 63, 2, "IMAGE"], [109, 74, 1, 63, 7, "INT"], [110, 74, 2, 63, 8, "INT"], [111, 75, 0, 63, 9, "INT"], [112, 75, 0, 73, 3, "INT"], [113, 78, 0, 73, 6, "FLOAT"], [114, 79, 0, 78, 0, "INT"], [115, 79, 0, 69, 14, "INT"], [116, 74, 0, 80, 0, "IMAGE"], [117, 38, 0, 81, 0, "*"], [118, 82, 0, 28, 0, "WANVAE"], [119, 83, 0, 63, 0, "WANVAE"], [120, 72, 0, 84, 0, "*"], [121, 84, 0, 73, 2, "AUDIO"], [122, 85, 0, 30, 1, "AUDIO"]], "groups": [{"id": 1, "title": "ComfyUI text encoding alternative", "bounding": [143.05531311035156, 637.08056640625, 1210.621337890625, 805.9080810546875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Models", "bounding": [-1171.969482421875, -1750.5992431640625, 1456.9825439453125, 1256.2666015625], "color": "#88A", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6115909044841843, "offset": [1596.583825096773, 1433.5628523878188]}, "frontendVersion": "1.18.9", "node_versions": {"ComfyUI-WanVideoWrapper": "5a2383621a05825d0d0437781afcb8552d9590fd", "comfy-core": "0.3.26", "ComfyUI-KJNodes": "a5bd3c86c8ed6b83c55c2d0e7a59515b15a0137f", "ComfyUI-VideoHelperSuite": "0a75c7958fe320efcb052f1d9f8451fd20c730a8"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}