{"id": "1171a5b6-6e8f-4e89-bf19-434874e02887", "revision": 0, "last_node_id": 82, "last_link_id": 183, "nodes": [{"id": 21, "type": "Reroute", "pos": [1500, 50], "size": [75, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 83}], "outputs": [{"name": "MASK", "type": "MASK", "slot_index": 0, "links": []}], "properties": {"showOutputText": true, "horizontal": false}}, {"id": 59, "type": "Reroute", "pos": [2535, -145], "size": [75, 26], "flags": {}, "order": 0, "mode": 0, "inputs": [{"name": "", "type": "*", "link": null}], "outputs": [{"name": "", "type": "*", "links": null}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 65, "type": "InvertMask", "pos": [4620.89013671875, -257.2927551269531], "size": [210, 26], "flags": {"collapsed": true}, "order": 18, "mode": 4, "inputs": [{"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 157}], "outputs": [{"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 0, "links": [158]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "InvertMask"}, "widgets_values": []}, {"id": 66, "type": "LayerMask: SegmentAnythingUltra V2", "pos": [4185.96044921875, -292.46112060546875], "size": [390.8785095214844, 366], "flags": {}, "order": 15, "mode": 4, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 156}, {"localized_name": "SAM模型", "name": "sam_model", "type": "COMBO", "widget": {"name": "sam_model"}, "link": null}, {"localized_name": "Grounding Dino模型", "name": "grounding_dino_model", "type": "COMBO", "widget": {"name": "grounding_dino_model"}, "link": null}, {"localized_name": "阈值", "name": "threshold", "type": "FLOAT", "widget": {"name": "threshold"}, "link": null}, {"localized_name": "细节方法", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "细节腐蚀", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "细节膨胀", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "黑点", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "白点", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "处理细节", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "提示", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "最大百万像素", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": [157]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerMask: SegmentAnythingUltra V2"}, "widgets_values": ["sam_vit_h (2.56GB)", "GroundingDINO_SwinT_OGC (694MB)", 0.3, "VITMatte", 6, 6, 0.15, 0.99, true, "subject", "cuda", 2, false], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 67, "type": "MaskPreview+", "pos": [4831.7841796875, -184.58326721191406], "size": [150.14315795898438, 26], "flags": {}, "order": 20, "mode": 4, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 158}], "outputs": [], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 39, "type": "IPAdapterModelLoader", "pos": [1389.8421630859375, -80.26383972167969], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "ipadapter_file", "name": "ipadapter_file", "type": "COMBO", "widget": {"name": "ipadapter_file"}, "link": null}], "outputs": [{"label": "IPADAPTER", "localized_name": "IPADAPTER", "name": "IPADAPTER", "type": "IPADAPTER", "slot_index": 0, "links": [95]}], "properties": {"cnr_id": "comfyui_ipadapter_plus", "ver": "2.0.0", "Node name for S&R": "IPAdapterModelLoader"}, "widgets_values": ["ip-adapter_sdxl_vit-h.safetensors"]}, {"id": 70, "type": "CLIPVisionLoader", "pos": [1394.48046875, 40.232261657714844], "size": [270, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [160]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["CLIP-ViT-H-fp16.safetensors"]}, {"id": 55, "type": "PreviewImage", "pos": [3474.888916015625, 420.36834716796875], "size": [498.9756164550781, 505.2386779785156], "flags": {}, "order": 32, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 138}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 6, "type": "CLIPTextEncode", "pos": [668.6798095703125, 879.5601806640625], "size": [400, 200], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 128}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [78]}], "title": "Negative", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["worst quality,low quality, normal quality, monochrome, lowres, watermark, spots", [false, true]], "color": "#322", "bgcolor": "#533"}, {"id": 31, "type": "UltralyticsDetectorProvider", "pos": [826.404541015625, 360.48321533203125], "size": [315, 78], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"localized_name": "BBOX_DETECTOR", "name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "slot_index": 0, "links": [79]}, {"localized_name": "SEGM_DETECTOR", "name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null}], "properties": {"cnr_id": "comfyui-impact-subpack", "ver": "1.3.2", "Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8n_v2.pt"]}, {"id": 51, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2099.684814453125, 26.54452133178711], "size": [315, 126], "flags": {}, "order": 19, "mode": 4, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 159}, {"localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 126}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [129]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [127, 128]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["style\\StS_age_slider_v1_initial_release.safetensors", -2, 1]}, {"id": 71, "type": "MaskPreview+", "pos": [2115.19775390625, 461.3167419433594], "size": [150.14315795898438, 246.00003051757812], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 161}], "outputs": [], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 16, "type": "SAMLoader", "pos": [807.8941650390625, 483.7181701660156], "size": [314, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "device_mode", "name": "device_mode", "type": "COMBO", "widget": {"name": "device_mode"}, "link": null}], "outputs": [{"localized_name": "SAM_MODEL", "name": "SAM_MODEL", "type": "SAM_MODEL", "slot_index": 0, "links": [80]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "AUTO"], "color": "#223", "bgcolor": "#335"}, {"id": 72, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [2373.664306640625, -439.8269958496094], "size": [274.9136657714844, 330], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "shape": 7, "type": "IMAGE", "link": 163}, {"localized_name": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": 165}, {"localized_name": "宽高比", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "比例宽度", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "比例高度", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "适应", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "方法", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "四舍五入到倍数", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "缩放至边", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "缩放至长度", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": null}, {"localized_name": "背景颜色", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [164]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": [166]}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "bicubic", "8", "longest", 1024, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 73, "type": "ImageResizeKJv2", "pos": [3001.161376953125, 68.82543182373047], "size": [270, 242], "flags": {}, "order": 29, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 167}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 168}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 169}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "keep_proportion", "name": "keep_proportion", "type": "COMBO", "widget": {"name": "keep_proportion"}, "link": null}, {"localized_name": "pad_color", "name": "pad_color", "type": "STRING", "widget": {"name": "pad_color"}, "link": null}, {"localized_name": "crop_position", "name": "crop_position", "type": "COMBO", "widget": {"name": "crop_position"}, "link": null}, {"localized_name": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [170]}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImageResizeKJv2"}, "widgets_values": [512, 512, "nearest-exact", "stretch", "0, 0, 0", "center", 2]}, {"id": 74, "type": "PreviewImage", "pos": [3315.081787109375, -1.3234516382217407], "size": [257.6621398925781, 410.97216796875], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 171}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 35, "type": "IPAdapterAdvanced", "pos": [1743.643798828125, -36.47203063964844], "size": [315, 278], "flags": {}, "order": 16, "mode": 4, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 90}, {"localized_name": "ipadapter", "name": "ipadapter", "type": "IPADAPTER", "link": 95}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 162}, {"localized_name": "image_negative", "name": "image_negative", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "attn_mask", "name": "attn_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "clip_vision", "name": "clip_vision", "shape": 7, "type": "CLIP_VISION", "link": 160}, {"localized_name": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}, "link": null}, {"localized_name": "weight_type", "name": "weight_type", "type": "COMBO", "widget": {"name": "weight_type"}, "link": null}, {"localized_name": "combine_embeds", "name": "combine_embeds", "type": "COMBO", "widget": {"name": "combine_embeds"}, "link": null}, {"localized_name": "start_at", "name": "start_at", "type": "FLOAT", "widget": {"name": "start_at"}, "link": null}, {"localized_name": "end_at", "name": "end_at", "type": "FLOAT", "widget": {"name": "end_at"}, "link": null}, {"localized_name": "embeds_scaling", "name": "embeds_scaling", "type": "COMBO", "widget": {"name": "embeds_scaling"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [159]}], "properties": {"cnr_id": "comfyui_ipadapter_plus", "ver": "2.0.0", "Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [0.30000000000000004, "style transfer", "concat", 0, 1, "V only"]}, {"id": 49, "type": "ImageCompositeMasked", "pos": [3459.864013671875, -190.93768310546875], "size": [315, 146], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "目标图像", "localized_name": "目标图像", "name": "destination", "type": "IMAGE", "link": 124}, {"label": "源图像", "localized_name": "来源图像", "name": "source", "type": "IMAGE", "link": 137}, {"label": "遮罩", "localized_name": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": 166}, {"label": "X", "localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": 120}, {"label": "Y", "localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": 121}, {"localized_name": "缩放来源图像", "name": "resize_source", "type": "BOOLEAN", "widget": {"name": "resize_source"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [122]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCompositeMasked", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "widgets_values": [0, 0, false], "color": "rgba(0,0,0,.8)"}, {"id": 5, "type": "CLIPTextEncode", "pos": [661.3325805664062, 625.2675170898438], "size": [400, 200], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 127}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 183}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [77]}], "title": "Positive", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [" masterpiece, professional artwork,1girl,white hair,crying,tears,very sad,blash,brown eyes", [false, true]], "color": "#322", "bgcolor": "#533"}, {"id": 79, "type": "OllamaVision", "pos": [-154.86001586914062, 341.15185546875], "size": [400, 256], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 178}, {"localized_name": "query", "name": "query", "type": "STRING", "widget": {"name": "query"}, "link": null}, {"localized_name": "debug", "name": "debug", "type": "COMBO", "widget": {"name": "debug"}, "link": null}, {"localized_name": "url", "name": "url", "type": "STRING", "widget": {"name": "url"}, "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "keep_alive", "name": "keep_alive", "type": "INT", "widget": {"name": "keep_alive"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"localized_name": "description", "name": "description", "type": "STRING", "links": [179]}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaVision"}, "widgets_values": ["\nDescribe the clothing, hairstyle, and eye color in the image and the image style", "enable", "http://127.0.0.1:11434", "llava:13b", 5, "text", 345452218, "randomize", [false, true]]}, {"id": 47, "type": "ImageCrop+", "pos": [1922.2413330078125, -428.78753662109375], "size": [315, 194], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 106}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 107}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 108}, {"localized_name": "position", "name": "position", "type": "COMBO", "widget": {"name": "position"}, "link": null}, {"localized_name": "x_offset", "name": "x_offset", "type": "INT", "widget": {"name": "x_offset"}, "link": 111}, {"localized_name": "y_offset", "name": "y_offset", "type": "INT", "widget": {"name": "y_offset"}, "link": 110}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [112, 136, 156, 162, 163]}, {"localized_name": "x", "name": "x", "type": "INT", "links": null}, {"localized_name": "y", "name": "y", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "ImageCrop+"}, "widgets_values": [256, 256, "top-left", 0, 0]}, {"id": 46, "type": "Mask Crop Region", "pos": [1494.5421142578125, -392.4769592285156], "size": [315, 222], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 105}, {"localized_name": "padding", "name": "padding", "type": "INT", "widget": {"name": "padding"}, "link": null}, {"localized_name": "region_type", "name": "region_type", "type": "COMBO", "widget": {"name": "region_type"}, "link": null}], "outputs": [{"localized_name": "cropped_mask", "name": "cropped_mask", "type": "MASK", "links": [165]}, {"localized_name": "crop_data", "name": "crop_data", "type": "CROP_DATA", "links": null}, {"localized_name": "top_int", "name": "top_int", "type": "INT", "slot_index": 2, "links": [110, 121]}, {"localized_name": "left_int", "name": "left_int", "type": "INT", "slot_index": 3, "links": [111, 120]}, {"localized_name": "right_int", "name": "right_int", "type": "INT", "links": null}, {"localized_name": "bottom_int", "name": "bottom_int", "type": "INT", "links": null}, {"localized_name": "width_int", "name": "width_int", "type": "INT", "slot_index": 6, "links": [107, 168]}, {"localized_name": "height_int", "name": "height_int", "type": "INT", "slot_index": 7, "links": [108, 169]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Mask Crop Region"}, "widgets_values": [30, "dominant"]}, {"id": 48, "type": "PreviewImage", "pos": [2731.32080078125, -548.7149658203125], "size": [513.3944091796875, 440.0023193359375], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 112}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 7, "type": "PreviewImage", "pos": [2355.718505859375, 332.95953369140625], "size": [450.5799560546875, 458.1924133300781], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 115}], "outputs": [], "title": "1pass (rough fix)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 24, "type": "PreviewImage", "pos": [2844.801025390625, 356.450927734375], "size": [509.6493835449219, 473.2600402832031], "flags": {}, "order": 28, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 89}], "outputs": [], "title": "2pass (detailed)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 34, "type": "FaceDetailerPipe", "pos": [1694.1517333984375, 332.5194091796875], "size": [366.9083251953125, 830], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 116}, {"localized_name": "detailer_pipe", "name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 87}, {"localized_name": "scheduler_func_opt", "name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC", "link": null}, {"localized_name": "guide_size", "name": "guide_size", "type": "FLOAT", "widget": {"name": "guide_size"}, "link": null}, {"localized_name": "guide_size_for", "name": "guide_size_for", "type": "BOOLEAN", "widget": {"name": "guide_size_for"}, "link": null}, {"localized_name": "max_size", "name": "max_size", "type": "FLOAT", "widget": {"name": "max_size"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}, {"localized_name": "feather", "name": "feather", "type": "INT", "widget": {"name": "feather"}, "link": null}, {"localized_name": "noise_mask", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}, {"localized_name": "force_inpaint", "name": "force_inpaint", "type": "BOOLEAN", "widget": {"name": "force_inpaint"}, "link": null}, {"localized_name": "bbox_threshold", "name": "bbox_threshold", "type": "FLOAT", "widget": {"name": "bbox_threshold"}, "link": null}, {"localized_name": "bbox_dilation", "name": "bbox_dilation", "type": "INT", "widget": {"name": "bbox_dilation"}, "link": null}, {"localized_name": "bbox_crop_factor", "name": "bbox_crop_factor", "type": "FLOAT", "widget": {"name": "bbox_crop_factor"}, "link": null}, {"localized_name": "sam_detection_hint", "name": "sam_detection_hint", "type": "COMBO", "widget": {"name": "sam_detection_hint"}, "link": null}, {"localized_name": "sam_dilation", "name": "sam_dilation", "type": "INT", "widget": {"name": "sam_dilation"}, "link": null}, {"localized_name": "sam_threshold", "name": "sam_threshold", "type": "FLOAT", "widget": {"name": "sam_threshold"}, "link": null}, {"localized_name": "sam_bbox_expansion", "name": "sam_bbox_expansion", "type": "INT", "widget": {"name": "sam_bbox_expansion"}, "link": null}, {"localized_name": "sam_mask_hint_threshold", "name": "sam_mask_hint_threshold", "type": "FLOAT", "widget": {"name": "sam_mask_hint_threshold"}, "link": null}, {"localized_name": "sam_mask_hint_use_negative", "name": "sam_mask_hint_use_negative", "type": "COMBO", "widget": {"name": "sam_mask_hint_use_negative"}, "link": null}, {"localized_name": "drop_size", "name": "drop_size", "type": "INT", "widget": {"name": "drop_size"}, "link": null}, {"localized_name": "refiner_ratio", "name": "refiner_ratio", "type": "FLOAT", "widget": {"name": "refiner_ratio"}, "link": null}, {"localized_name": "cycle", "name": "cycle", "type": "INT", "widget": {"name": "cycle"}, "link": null}, {"localized_name": "inpaint_model", "name": "inpaint_model", "shape": 7, "type": "BOOLEAN", "widget": {"name": "inpaint_model"}, "link": null}, {"localized_name": "noise_mask_feather", "name": "noise_mask_feather", "shape": 7, "type": "INT", "widget": {"name": "noise_mask_feather"}, "link": null}, {"localized_name": "tiled_encode", "name": "tiled_encode", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_encode"}, "link": null}, {"localized_name": "tiled_decode", "name": "tiled_decode", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_decode"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [89, 167]}, {"localized_name": "cropped_refined", "name": "cropped_refined", "shape": 6, "type": "IMAGE", "links": null}, {"localized_name": "cropped_enhanced_alpha", "name": "cropped_enhanced_alpha", "shape": 6, "type": "IMAGE", "links": null}, {"localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 3, "links": []}, {"localized_name": "detailer_pipe", "name": "detailer_pipe", "type": "DETAILER_PIPE", "links": null}, {"localized_name": "cnet_images", "name": "cnet_images", "shape": 6, "type": "IMAGE", "links": null}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [360, true, 768, 704185545803683, "randomize", 20, 3, "dpmpp_2m_sde_gpu", "AYS SDXL", 0.4000000000000001, 5, true, false, 0.6, 10, 3, "center-1", 10, 0.93, 88, 0.7, "False", 10, 0.2, 1, false, 20, false, false], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 32, "type": "FaceDetailer", "pos": [1294.321044921875, 332.3020935058594], "size": [370, 960], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 164}, {"localized_name": "model", "name": "model", "type": "MODEL", "link": 129}, {"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 85}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 76}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 77}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 78}, {"localized_name": "bbox_detector", "name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 79}, {"localized_name": "sam_model_opt", "name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL", "link": 80}, {"localized_name": "segm_detector_opt", "name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR", "link": null}, {"localized_name": "detailer_hook", "name": "detailer_hook", "shape": 7, "type": "DETAILER_HOOK", "link": null}, {"localized_name": "scheduler_func_opt", "name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC", "link": null}, {"localized_name": "guide_size", "name": "guide_size", "type": "FLOAT", "widget": {"name": "guide_size"}, "link": null}, {"localized_name": "guide_size_for", "name": "guide_size_for", "type": "BOOLEAN", "widget": {"name": "guide_size_for"}, "link": null}, {"localized_name": "max_size", "name": "max_size", "type": "FLOAT", "widget": {"name": "max_size"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}, {"localized_name": "feather", "name": "feather", "type": "INT", "widget": {"name": "feather"}, "link": null}, {"localized_name": "noise_mask", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}, {"localized_name": "force_inpaint", "name": "force_inpaint", "type": "BOOLEAN", "widget": {"name": "force_inpaint"}, "link": null}, {"localized_name": "bbox_threshold", "name": "bbox_threshold", "type": "FLOAT", "widget": {"name": "bbox_threshold"}, "link": null}, {"localized_name": "bbox_dilation", "name": "bbox_dilation", "type": "INT", "widget": {"name": "bbox_dilation"}, "link": null}, {"localized_name": "bbox_crop_factor", "name": "bbox_crop_factor", "type": "FLOAT", "widget": {"name": "bbox_crop_factor"}, "link": null}, {"localized_name": "sam_detection_hint", "name": "sam_detection_hint", "type": "COMBO", "widget": {"name": "sam_detection_hint"}, "link": null}, {"localized_name": "sam_dilation", "name": "sam_dilation", "type": "INT", "widget": {"name": "sam_dilation"}, "link": null}, {"localized_name": "sam_threshold", "name": "sam_threshold", "type": "FLOAT", "widget": {"name": "sam_threshold"}, "link": null}, {"localized_name": "sam_bbox_expansion", "name": "sam_bbox_expansion", "type": "INT", "widget": {"name": "sam_bbox_expansion"}, "link": null}, {"localized_name": "sam_mask_hint_threshold", "name": "sam_mask_hint_threshold", "type": "FLOAT", "widget": {"name": "sam_mask_hint_threshold"}, "link": null}, {"localized_name": "sam_mask_hint_use_negative", "name": "sam_mask_hint_use_negative", "type": "COMBO", "widget": {"name": "sam_mask_hint_use_negative"}, "link": null}, {"localized_name": "drop_size", "name": "drop_size", "type": "INT", "widget": {"name": "drop_size"}, "link": null}, {"localized_name": "wildcard", "name": "wildcard", "type": "STRING", "widget": {"name": "wildcard"}, "link": 182}, {"localized_name": "cycle", "name": "cycle", "type": "INT", "widget": {"name": "cycle"}, "link": null}, {"localized_name": "inpaint_model", "name": "inpaint_model", "shape": 7, "type": "BOOLEAN", "widget": {"name": "inpaint_model"}, "link": null}, {"localized_name": "noise_mask_feather", "name": "noise_mask_feather", "shape": 7, "type": "INT", "widget": {"name": "noise_mask_feather"}, "link": null}, {"localized_name": "tiled_encode", "name": "tiled_encode", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_encode"}, "link": null}, {"localized_name": "tiled_decode", "name": "tiled_decode", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_decode"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [115, 116]}, {"localized_name": "cropped_refined", "name": "cropped_refined", "shape": 6, "type": "IMAGE", "links": null}, {"localized_name": "cropped_enhanced_alpha", "name": "cropped_enhanced_alpha", "shape": 6, "type": "IMAGE", "links": null}, {"localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 3, "links": [83, 161]}, {"localized_name": "detailer_pipe", "name": "detailer_pipe", "type": "DETAILER_PIPE", "slot_index": 4, "links": [87]}, {"localized_name": "cnet_images", "name": "cnet_images", "shape": 6, "type": "IMAGE", "links": null}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "FaceDetailer"}, "widgets_values": [128, true, 768, 873701482223560, "randomize", 20, 3, "dpmpp_2m_sde_gpu", "AYS SDXL", 0.5000000000000001, 5, true, true, 0.3, 20, 3, "center-1", 20, 0.93, 0, 0.7, "False", 10, "", 1, false, 20, false, false, [false, true]], "color": "#223", "bgcolor": "#335"}, {"id": 54, "type": "LayerColor: ColorAdapter", "pos": [3841.44384765625, -142.66986083984375], "size": [210, 80], "flags": {"collapsed": false, "pinned": false}, "order": 30, "mode": 0, "inputs": [{"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 170}, {"label": "color_ref_image", "localized_name": "颜色参考图像", "name": "color_ref_image", "type": "IMAGE", "link": 136}, {"localized_name": "不透明度", "name": "opacity", "type": "INT", "widget": {"name": "opacity"}, "link": null}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [137, 138]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "c0fb64d0ebcb81c6c445a8af79ecee24bc3845b0", "Node name for S&R": "LayerColor: ColorAdapter", "ttNbgOverride": {"color": "#1f1f48", "groupcolor": "#88A"}}, "widgets_values": [100], "color": "#1f1f48"}, {"id": 45, "type": "LoadImage", "pos": [1092.8424072265625, -430.89508056640625], "size": [315, 314], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [106, 124, 171, 178]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": [105]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-2768023.6000000015.png [input]", "image"]}, {"id": 81, "type": "CR Text Concatenate", "pos": [821.9347534179688, 230.41212463378906], "size": [270, 78], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "text1", "name": "text1", "shape": 7, "type": "STRING", "link": 181}, {"localized_name": "text2", "name": "text2", "shape": 7, "type": "STRING", "link": 180}, {"localized_name": "separator", "name": "separator", "shape": 7, "type": "STRING", "widget": {"name": "separator"}, "link": null}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [183]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": [""]}, {"id": 78, "type": "ShowText|pysssss", "pos": [354.6903991699219, 368.8349304199219], "size": [298.3088073730469, 559.804443359375], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 179}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [180]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": [" In the image, you see a man with fair skin. He has short brown hair and blue eyes. His expression is serious, with a hint of a slight smile on his lips. The man appears to be wearing a dark top or jacket, which might have some lighter elements, such as patterns or a color that stands out on it.\n\nThe image itself has a glitch effect that introduces a horizontal pixelated pattern across the subject's face and torso, giving it an unusual, digital art style appearance. This effect is not natural but rather added to enhance the image's visual interest or evoke a certain mood."]}, {"id": 82, "type": "CR Text", "pos": [319.3226013183594, 112.6029281616211], "size": [400, 200], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [181, 182]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["very sad,cry,tears", [false, true]]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [968.0808715820312, -58.376930236816406], "size": [315, 98], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [90]}, {"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [85, 126]}, {"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [76]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["XL\\juggernautXL_ragnarokBy.safetensors"], "color": "#222", "bgcolor": "#000"}, {"id": 50, "type": "PreviewImage", "pos": [3580.326416015625, 18.506484985351562], "size": [255.59007263183594, 364.85369873046875], "flags": {}, "order": 33, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 122}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}], "links": [[76, 4, 2, 32, 3, "VAE"], [77, 5, 0, 32, 4, "CONDITIONING"], [78, 6, 0, 32, 5, "CONDITIONING"], [79, 31, 0, 32, 6, "BBOX_DETECTOR"], [80, 16, 0, 32, 7, "SAM_MODEL"], [83, 32, 3, 21, 0, "*"], [85, 4, 1, 32, 2, "CLIP"], [87, 32, 4, 34, 1, "DETAILER_PIPE"], [89, 34, 0, 24, 0, "IMAGE"], [90, 4, 0, 35, 0, "MODEL"], [95, 39, 0, 35, 1, "IPADAPTER"], [105, 45, 1, 46, 0, "MASK"], [106, 45, 0, 47, 0, "IMAGE"], [107, 46, 6, 47, 1, "INT"], [108, 46, 7, 47, 2, "INT"], [110, 46, 2, 47, 5, "INT"], [111, 46, 3, 47, 4, "INT"], [112, 47, 0, 48, 0, "IMAGE"], [115, 32, 0, 7, 0, "IMAGE"], [116, 32, 0, 34, 0, "IMAGE"], [120, 46, 3, 49, 3, "INT"], [121, 46, 2, 49, 4, "INT"], [122, 49, 0, 50, 0, "IMAGE"], [124, 45, 0, 49, 0, "IMAGE"], [126, 4, 1, 51, 1, "CLIP"], [127, 51, 1, 5, 0, "CLIP"], [128, 51, 1, 6, 0, "CLIP"], [129, 51, 0, 32, 1, "MODEL"], [136, 47, 0, 54, 1, "IMAGE"], [137, 54, 0, 49, 1, "IMAGE"], [138, 54, 0, 55, 0, "IMAGE"], [156, 47, 0, 66, 0, "IMAGE"], [157, 66, 1, 65, 0, "MASK"], [158, 65, 0, 67, 0, "MASK"], [159, 35, 0, 51, 0, "MODEL"], [160, 70, 0, 35, 5, "CLIP_VISION"], [161, 32, 3, 71, 0, "MASK"], [162, 47, 0, 35, 2, "IMAGE"], [163, 47, 0, 72, 0, "IMAGE"], [164, 72, 0, 32, 0, "IMAGE"], [165, 46, 0, 72, 1, "MASK"], [166, 72, 1, 49, 2, "MASK"], [167, 34, 0, 73, 0, "IMAGE"], [168, 46, 6, 73, 1, "INT"], [169, 46, 7, 73, 2, "INT"], [170, 73, 0, 54, 0, "IMAGE"], [171, 45, 0, 74, 0, "IMAGE"], [178, 45, 0, 79, 0, "IMAGE"], [179, 79, 0, 78, 0, "STRING"], [180, 78, 0, 81, 1, "STRING"], [181, 82, 0, 81, 0, "STRING"], [182, 82, 0, 32, 33, "STRING"], [183, 81, 0, 5, 1, "STRING"]], "groups": [{"id": 1, "title": "Group", "bounding": [4052, -341, 1043, 455], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7972024500000241, "offset": [-1444.6730770069598, 565.0954170698552]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}