{"id": "a1c94175-56da-462d-9e82-b27baf4ad28a", "revision": 0, "last_node_id": 243, "last_link_id": 417, "nodes": [{"id": 119, "type": "easy cleanGpuUsed", "pos": [1387.1280517578125, 576.6390991210938], "size": [210, 26], "flags": {"collapsed": true}, "order": 33, "mode": 0, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 205}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy cleanGpuUsed"}, "widgets_values": []}, {"id": 116, "type": "easy showAnything", "pos": [27.222814559936523, 1372.77685546875], "size": [210, 88], "flags": {}, "order": 37, "mode": 4, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 202}], "outputs": [{"localized_name": "输出", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything"}, "widgets_values": ["2048"]}, {"id": 115, "type": "easy showAnything", "pos": [27.222814559936523, 1496.114990234375], "size": [210, 88], "flags": {}, "order": 39, "mode": 4, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 201}], "outputs": [{"localized_name": "输出", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything"}, "widgets_values": ["976"]}, {"id": 114, "type": "DualCLIPLoader", "pos": [351.30975341796875, 1220.1846923828125], "size": [371.83294677734375, 130], "flags": {}, "order": 0, "mode": 4, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [173]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 97, "type": "ModelSamplingFlux", "pos": [351.30975341796875, 1546.1466064453125], "size": [240, 130], "flags": {}, "order": 38, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 210}, {"localized_name": "最大移位", "name": "max_shift", "type": "FLOAT", "widget": {"name": "max_shift"}, "link": null}, {"localized_name": "基础移位", "name": "base_shift", "type": "FLOAT", "widget": {"name": "base_shift"}, "link": null}, {"label": "宽度", "localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 184}, {"label": "高度", "localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 185}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [180, 199]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, 1024, 1024]}, {"id": 91, "type": "CLIPTextEncode", "pos": [739.0582885742188, 1067.0660400390625], "size": [490, 250], "flags": {"collapsed": false}, "order": 43, "mode": 4, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 173}, {"label": "文本", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 211}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [182]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#232", "bgcolor": "#353"}, {"id": 96, "type": "FluxGuidance", "pos": [743.2228393554688, 1365.05712890625], "size": [211.60000610351562, 60], "flags": {}, "order": 45, "mode": 4, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 182}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [181]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5], "color": "#323", "bgcolor": "#535"}, {"id": 102, "type": "VAEEncode", "pos": [743.2228393554688, 1467.01953125], "size": [210, 46], "flags": {}, "order": 36, "mode": 4, "inputs": [{"label": "图像", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 188}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 189}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [190]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 93, "type": "KSamplerSelect", "pos": [981.4197998046875, 1371.86962890625], "size": [260, 60], "flags": {}, "order": 1, "mode": 4, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"label": "采样器", "localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [177]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 94, "type": "BasicGuider", "pos": [743.2228393554688, 1560.019775390625], "size": [210.4696502685547, 51.93691635131836], "flags": {"collapsed": false}, "order": 46, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 180}, {"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 181}], "outputs": [{"label": "引导", "localized_name": "引导器", "name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [176]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 92, "type": "SamplerCustomAdvanced", "pos": [1253.7694091796875, 1080.086669921875], "size": [240, 326], "flags": {}, "order": 47, "mode": 4, "inputs": [{"label": "噪波生成", "localized_name": "噪波", "name": "noise", "type": "NOISE", "link": 175}, {"label": "引导", "localized_name": "引导器", "name": "guider", "type": "GUIDER", "link": 176}, {"label": "采样器", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 177}, {"label": "Sigmas", "localized_name": "西格玛", "name": "sigmas", "type": "SIGMAS", "link": 178}, {"label": "Latent", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 179}], "outputs": [{"label": "输出", "localized_name": "Latent", "name": "output", "type": "LATENT", "slot_index": 0, "links": [193]}, {"label": "降噪输出", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 103, "type": "Reroute", "pos": [988.************, 1602.************], "size": [75, 26], "flags": {}, "order": 41, "mode": 4, "inputs": [{"label": "", "name": "", "type": "*", "link": 190}], "outputs": [{"name": "", "type": "LATENT", "links": [179]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 95, "type": "RandomNoise", "pos": [-395.4814147949219, 1587.5147705078125], "size": [260, 82], "flags": {"collapsed": true}, "order": 2, "mode": 4, "inputs": [{"localized_name": "噪波随机种", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"label": "噪波生成", "localized_name": "噪波", "name": "NOISE", "type": "NOISE", "links": [175]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "RandomNoise"}, "widgets_values": [641645356135932, "randomize"]}, {"id": 122, "type": "easy cleanGpuUsed", "pos": [-395.4814147949219, 1641.8155517578125], "size": [210, 26], "flags": {"collapsed": true}, "order": 44, "mode": 4, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 212}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy cleanGpuUsed"}, "widgets_values": []}, {"id": 123, "type": "easy cleanGpuUsed", "pos": [-395.4814147949219, 1692.4669189453125], "size": [210, 26], "flags": {"collapsed": true}, "order": 49, "mode": 4, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 213}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy cleanGpuUsed"}, "widgets_values": []}, {"id": 113, "type": "LoraLoaderModelOnly", "pos": [351.30975341796875, 1398.5162353515625], "size": [367.6833801269531, 106], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 401}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [210]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["FLUX\\F-yn\\flux-ynstyle2.safetensors", 1]}, {"id": 81, "type": "ImageFromBatch", "pos": [487.8229675292969, 582.6755981445312], "size": [315, 82], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 160}, {"localized_name": "批次索引", "name": "batch_index", "type": "INT", "widget": {"name": "batch_index"}, "link": null}, {"localized_name": "长度", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [249, 250, 388]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageFromBatch"}, "widgets_values": [0, 1]}, {"id": 83, "type": "ImageFromBatch", "pos": [487.8229675292969, 710.7160034179688], "size": [315, 82], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 162}, {"localized_name": "批次索引", "name": "batch_index", "type": "INT", "widget": {"name": "batch_index"}, "link": null}, {"localized_name": "长度", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [256]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageFromBatch"}, "widgets_values": [1, 1]}, {"id": 87, "type": "ImageFromBatch", "pos": [813.0369873046875, 710.7160034179688], "size": [315, 82], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 166}, {"localized_name": "批次索引", "name": "batch_index", "type": "INT", "widget": {"name": "batch_index"}, "link": null}, {"localized_name": "长度", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [268]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageFromBatch"}, "widgets_values": [3, 1]}, {"id": 146, "type": "Bounded Image Crop", "pos": [1396.06103515625, 633.2120361328125], "size": [210, 46], "flags": {"collapsed": true}, "order": 27, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 250}, {"label": "边界", "localized_name": "image_bounds", "name": "image_bounds", "type": "IMAGE_BOUNDS", "link": 247}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [325]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Bounded Image Crop"}, "widgets_values": []}, {"id": 157, "type": "Bounded Image Crop", "pos": [1396.06103515625, 688.721923828125], "size": [210, 46], "flags": {"collapsed": true}, "order": 29, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 268}, {"label": "边界", "localized_name": "image_bounds", "name": "image_bounds", "type": "IMAGE_BOUNDS", "link": 323}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [328]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Bounded Image Crop"}, "widgets_values": []}, {"id": 151, "type": "Bounded Image Crop", "pos": [1396.06103515625, 796.8027954101562], "size": [210, 46], "flags": {"collapsed": true}, "order": 28, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 256}, {"label": "边界", "localized_name": "image_bounds", "name": "image_bounds", "type": "IMAGE_BOUNDS", "link": 322}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [326]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Bounded Image Crop"}, "widgets_values": []}, {"id": 154, "type": "Bounded Image Crop", "pos": [1396.06103515625, 742.0147094726562], "size": [210, 46], "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 261}, {"label": "边界", "localized_name": "image_bounds", "name": "image_bounds", "type": "IMAGE_BOUNDS", "link": 324}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [327]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Bounded Image Crop"}, "widgets_values": []}, {"id": 117, "type": "ImageResizeKJ", "pos": [18.62264060974121, 1058.5462646484375], "size": [315, 266], "flags": {}, "order": 34, "mode": 4, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 209}, {"label": "参考图像", "localized_name": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "keep_proportion", "name": "keep_proportion", "type": "BOOLEAN", "widget": {"name": "keep_proportion"}, "link": null}, {"localized_name": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}, "link": null}, {"localized_name": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [188]}, {"label": "宽度", "localized_name": "width", "name": "width", "type": "INT", "slot_index": 1, "links": [184, 202]}, {"label": "高度", "localized_name": "height", "name": "height", "type": "INT", "slot_index": 2, "links": [185, 201]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImageResizeKJ"}, "widgets_values": [2048, 2048, "nearest-exact", true, 2, 0]}, {"id": 112, "type": "BasicScheduler", "pos": [981.4197998046875, 1477.228271484375], "size": [260, 110], "flags": {}, "order": 42, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 199}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Sigmas", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [178]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", 30, 0.4]}, {"id": 201, "type": "GetImageSize+", "pos": [427.4610900878906, 1874.6082763671875], "size": [210, 66], "flags": {}, "order": 50, "mode": 4, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 379}], "outputs": [{"label": "宽度", "localized_name": "width", "name": "width", "type": "INT", "slot_index": 0, "links": [345]}, {"label": "高度", "localized_name": "height", "name": "height", "type": "INT", "slot_index": 1, "links": [350, 353, 357, 361]}, {"localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 202, "type": "easy mathInt", "pos": [427.4610900878906, 1994.6082763671875], "size": [315, 106], "flags": {}, "order": 53, "mode": 4, "inputs": [{"label": "a", "localized_name": "a", "name": "a", "type": "INT", "widget": {"name": "a"}, "link": 345}, {"localized_name": "b", "name": "b", "type": "INT", "widget": {"name": "b"}, "link": null}, {"localized_name": "操作", "name": "operation", "type": "COMBO", "widget": {"name": "operation"}, "link": null}], "outputs": [{"label": "整数", "localized_name": "整数", "name": "INT", "type": "INT", "slot_index": 0, "links": [346, 347, 349, 352, 354, 356, 360]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy mathInt"}, "widgets_values": [0, 4, "divide"]}, {"id": 203, "type": "easy mathInt", "pos": [427.4610900878906, 2154.608154296875], "size": [315, 106], "flags": {}, "order": 55, "mode": 4, "inputs": [{"label": "a", "localized_name": "a", "name": "a", "type": "INT", "widget": {"name": "a"}, "link": 346}, {"localized_name": "b", "name": "b", "type": "INT", "widget": {"name": "b"}, "link": null}, {"localized_name": "操作", "name": "operation", "type": "COMBO", "widget": {"name": "operation"}, "link": null}], "outputs": [{"label": "整数", "localized_name": "整数", "name": "INT", "type": "INT", "slot_index": 0, "links": [358]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy mathInt"}, "widgets_values": [0, 2, "multiply"]}, {"id": 204, "type": "easy mathInt", "pos": [427.4610900878906, 2324.608154296875], "size": [315, 106], "flags": {}, "order": 56, "mode": 4, "inputs": [{"label": "a", "localized_name": "a", "name": "a", "type": "INT", "widget": {"name": "a"}, "link": 347}, {"localized_name": "b", "name": "b", "type": "INT", "widget": {"name": "b"}, "link": null}, {"localized_name": "操作", "name": "operation", "type": "COMBO", "widget": {"name": "operation"}, "link": null}], "outputs": [{"label": "整数", "localized_name": "整数", "name": "INT", "type": "INT", "slot_index": 0, "links": [362]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy mathInt"}, "widgets_values": [0, 3, "multiply"]}, {"id": 205, "type": "ImageCrop", "pos": [797.4613037109375, 1854.6082763671875], "size": [315, 130], "flags": {}, "order": 57, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 380}, {"label": "宽度", "localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 349}, {"label": "高度", "localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 350}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [367, 392]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop"}, "widgets_values": [768, 768, 0, 0]}, {"id": 213, "type": "SaveImage", "pos": [1630.282470703125, 1644.0867919921875], "size": [355.4750061035156, 373.3667907714844], "flags": {}, "order": 61, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 367}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["多视图/bg/a-"]}, {"id": 214, "type": "SaveImage", "pos": [2000.282470703125, 1644.0867919921875], "size": [355.4750061035156, 373.3667907714844], "flags": {}, "order": 63, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 368}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["多视图/bg/a-"]}, {"id": 215, "type": "SaveImage", "pos": [2368.25390625, 1644.0867919921875], "size": [355.4750061035156, 373.3667907714844], "flags": {}, "order": 65, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 369}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["多视图/bg/a-"]}, {"id": 216, "type": "SaveImage", "pos": [2736.453125, 1637.7928466796875], "size": [355.4750061035156, 373.3667907714844], "flags": {}, "order": 67, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 370}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["多视图/bg/a-"]}, {"id": 224, "type": "SaveImage", "pos": [1640.282470703125, 2064.086669921875], "size": [355.4750061035156, 373.3667907714844], "flags": {}, "order": 62, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 392}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["多视图/tm/a-"]}, {"id": 222, "type": "SaveImage", "pos": [2370.2822265625, 2064.086669921875], "size": [355.4750061035156, 373.3667907714844], "flags": {}, "order": 70, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 397}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["多视图/tm/a-"]}, {"id": 89, "type": "ImageConcatMulti", "pos": [1139.3128662109375, 578.8745727539062], "size": [210, 190], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "图像_1", "localized_name": "image_1", "name": "image_1", "type": "IMAGE", "link": 325}, {"label": "图像_2", "localized_name": "image_2", "name": "image_2", "type": "IMAGE", "link": 326}, {"localized_name": "inputcount", "name": "inputcount", "type": "INT", "widget": {"name": "inputcount"}, "link": null}, {"localized_name": "direction", "name": "direction", "type": "COMBO", "widget": {"name": "direction"}, "link": null}, {"localized_name": "match_image_size", "name": "match_image_size", "type": "BOOLEAN", "widget": {"name": "match_image_size"}, "link": null}, {"name": "image_3", "type": "IMAGE", "link": 327}, {"name": "image_4", "type": "IMAGE", "link": 328}], "outputs": [{"label": "图像", "localized_name": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [172, 205, 209, 385, 386]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0"}, "widgets_values": [4, "right", false, ""]}, {"id": 106, "type": "VAEDecode", "pos": [1253.7694091796875, 1457.766845703125], "size": [140, 50], "flags": {}, "order": 48, "mode": 4, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 193}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 194}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [213, 379, 380, 381, 382, 383, 384, 390]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 206, "type": "ImageCrop", "pos": [797.4613037109375, 2034.6083984375], "size": [315, 130], "flags": {}, "order": 58, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 381}, {"label": "宽度", "localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 352}, {"label": "高度", "localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 353}, {"label": "X", "localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": 354}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [368, 393]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop"}, "widgets_values": [768, 768, 0, 0]}, {"id": 221, "type": "SaveImage", "pos": [2011.662353515625, 2069.6064453125], "size": [355.4750061035156, 373.3667907714844], "flags": {}, "order": 69, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 394}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["多视图/tm/a-"]}, {"id": 228, "type": "LayerMask: RmBgUltra V2", "pos": [1327.4581298828125, 1588.4798583984375], "size": [270, 246], "flags": {"collapsed": true}, "order": 52, "mode": 4, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 390}, {"localized_name": "detail_method", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "detail_erode", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "detail_dilate", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "black_point", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "white_point", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "process_detail", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "device", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "max_megapixels", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [391]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": []}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerMask: RmBgUltra V2"}, "widgets_values": ["VITMatte", 6, 6, 0.01, 0.99, true, "cuda", 2], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 229, "type": "LayerMask: RmBgUltra V2", "pos": [1216.762451171875, 2006.0806884765625], "size": [270, 246], "flags": {"collapsed": true}, "order": 64, "mode": 4, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 393}, {"localized_name": "detail_method", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "detail_erode", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "detail_dilate", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "black_point", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "white_point", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "process_detail", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "device", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "max_megapixels", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [394]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": []}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerMask: RmBgUltra V2"}, "widgets_values": ["VITMatte", 6, 6, 0.01, 0.99, true, "cuda", 2], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 207, "type": "ImageCrop", "pos": [797.4613037109375, 2204.608154296875], "size": [315, 130], "flags": {}, "order": 59, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 382}, {"label": "宽度", "localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 356}, {"label": "高度", "localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 357}, {"label": "X", "localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": 358}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [369, 395]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop"}, "widgets_values": [768, 768, 0, 0]}, {"id": 208, "type": "ImageCrop", "pos": [797.4613037109375, 2384.608154296875], "size": [315, 130], "flags": {}, "order": 60, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 383}, {"label": "宽度", "localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 360}, {"label": "高度", "localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 361}, {"label": "X", "localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": 362}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [370, 396]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop"}, "widgets_values": [768, 768, 0, 0]}, {"id": 231, "type": "LayerMask: RmBgUltra V2", "pos": [1219.7486572265625, 2414.368896484375], "size": [270, 246], "flags": {"collapsed": true}, "order": 68, "mode": 4, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 396}, {"localized_name": "detail_method", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "detail_erode", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "detail_dilate", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "black_point", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "white_point", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "process_detail", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "device", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "max_megapixels", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [398]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": []}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerMask: RmBgUltra V2"}, "widgets_values": ["VITMatte", 6, 6, 0.01, 0.99, true, "cuda", 2], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 230, "type": "LayerMask: RmBgUltra V2", "pos": [1228.5972900390625, 2219.095703125], "size": [270, 246], "flags": {"collapsed": true}, "order": 66, "mode": 4, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 395}, {"localized_name": "detail_method", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "detail_erode", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "detail_dilate", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "black_point", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "white_point", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "process_detail", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "device", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "max_megapixels", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [397]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": []}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerMask: RmBgUltra V2"}, "widgets_values": ["VITMatte", 6, 6, 0.01, 0.99, true, "cuda", 2], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 223, "type": "SaveImage", "pos": [2741.662109375, 2072.36572265625], "size": [355.4750061035156, 373.3667907714844], "flags": {}, "order": 71, "mode": 4, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 398}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["多视图/tm/a-"]}, {"id": 38, "type": "easy cleanGpuUsed", "pos": [508.702880859375, 533.3240966796875], "size": [210, 26], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [{"label": "输入任何", "localized_name": "输入任何", "name": "anything", "type": "*", "link": 52}], "outputs": [{"localized_name": "output", "name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy cleanGpuUsed"}, "widgets_values": []}, {"id": 8, "type": "ImagePreprocessor", "pos": [782.7880859375, 84.04080963134766], "size": [315, 102], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "remove_bg_fn", "localized_name": "remove_bg_fn", "name": "remove_bg_fn", "type": "FUNCTION", "link": 6}, {"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 7}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [8, 9]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "ImagePreprocessor"}, "widgets_values": [768, 768]}, {"id": 39, "type": "Diffusers<PERSON>ae<PERSON><PERSON>der", "pos": [452.1628112792969, 383.1809387207031], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "vae_name", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "AUTOENCODER", "localized_name": "AUTOENCODER", "name": "AUTOENCODER", "type": "AUTOENCODER", "slot_index": 0, "links": [407]}], "properties": {"cnr_id": "comfyui-diffusers", "ver": "1.0.2", "Node name for S&R": "Diffusers<PERSON>ae<PERSON><PERSON>der"}, "widgets_values": ["sdxl_vae.safetensors"]}, {"id": 239, "type": "DiffusersMVSchedulerLoader", "pos": [467.11859130859375, 188.119384765625], "size": [327.5999755859375, 130], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "pipeline", "name": "pipeline", "type": "PIPELINE", "link": 413}, {"localized_name": "scheduler_name", "name": "scheduler_name", "type": "COMBO", "widget": {"name": "scheduler_name"}, "link": null}, {"localized_name": "shift_snr", "name": "shift_snr", "type": "BOOLEAN", "widget": {"name": "shift_snr"}, "link": null}, {"localized_name": "shift_mode", "name": "shift_mode", "type": "COMBO", "widget": {"name": "shift_mode"}, "link": null}, {"localized_name": "shift_scale", "name": "shift_scale", "type": "FLOAT", "widget": {"name": "shift_scale"}, "link": null}], "outputs": [{"localized_name": "SCHEDULER", "name": "SCHEDULER", "type": "SCHEDULER", "slot_index": 0, "links": [412]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "DiffusersMVSchedulerLoader"}, "widgets_values": ["DDPM", true, "interpolated", 8]}, {"id": 12, "type": "ViewSelector", "pos": [43.951194763183594, 299.23809814453125], "size": [315, 178], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "front_view", "name": "front_view", "type": "BOOLEAN", "widget": {"name": "front_view"}, "link": null}, {"localized_name": "front_right_view", "name": "front_right_view", "type": "BOOLEAN", "widget": {"name": "front_right_view"}, "link": null}, {"localized_name": "right_view", "name": "right_view", "type": "BOOLEAN", "widget": {"name": "right_view"}, "link": null}, {"localized_name": "back_view", "name": "back_view", "type": "BOOLEAN", "widget": {"name": "back_view"}, "link": null}, {"localized_name": "left_view", "name": "left_view", "type": "BOOLEAN", "widget": {"name": "left_view"}, "link": null}, {"localized_name": "front_left_view", "name": "front_left_view", "type": "BOOLEAN", "widget": {"name": "front_left_view"}, "link": null}], "outputs": [{"label": "LIST", "localized_name": "LIST", "name": "LIST", "type": "LIST", "slot_index": 0, "links": [11]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "ViewSelector"}, "widgets_values": [true, false, true, true, false, true]}, {"id": 98, "type": "UNETLoader", "pos": [363.8536071777344, 1080.5732421875], "size": [320, 106], "flags": {}, "order": 5, "mode": 4, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [401]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev-fp8.safetensors", "fp8_e4m3fn"]}, {"id": 101, "type": "VAELoader", "pos": [35.817710876464844, 1618.697265625], "size": [230, 82], "flags": {}, "order": 6, "mode": 4, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [189, 194]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 227, "type": "LayerMask: RmBgUltra V2", "pos": [519.4825439453125, 875.72900390625], "size": [270, 246], "flags": {"collapsed": true}, "order": 25, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 388}, {"localized_name": "detail_method", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "detail_erode", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "detail_dilate", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "black_point", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "white_point", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "process_detail", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "device", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "max_megapixels", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": [389]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerMask: RmBgUltra V2"}, "widgets_values": ["VITMatte", 6, 6, 0.01, 0.99, true, "cuda", 2], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 10, "type": "PreviewImage", "pos": [786.5856323242188, -275.7092590332031], "size": [267.6073303222656, 276.0325927734375], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 8}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 9, "type": "BiRefNet", "pos": [462.97381591796875, 85.80033111572266], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "STRING", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"localized_name": "FUNCTION", "name": "FUNCTION", "type": "FUNCTION", "slot_index": 0, "links": [6]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "BiRefNet"}, "widgets_values": ["ZhengPeng7/BiRefNet"]}, {"id": 236, "type": "DiffusersMVPipelineLoader", "pos": [73.7420883178711, 101.4625473022461], "size": [280.9566345214844, 122], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "ckpt_name", "name": "ckpt_name", "type": "STRING", "widget": {"name": "ckpt_name"}, "link": null}, {"localized_name": "pipeline_name", "name": "pipeline_name", "type": "COMBO", "widget": {"name": "pipeline_name"}, "link": null}], "outputs": [{"localized_name": "PIPELINE", "name": "PIPELINE", "type": "PIPELINE", "links": [408, 413]}, {"localized_name": "AUTOENCODER", "name": "AUTOENCODER", "type": "AUTOENCODER", "links": null}, {"localized_name": "SCHEDULER", "name": "SCHEDULER", "type": "SCHEDULER", "links": null}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "DiffusersMVPipelineLoader"}, "widgets_values": ["stabilityai/stable-diffusion-xl-base-1.0", "MVAdapterI2MVSDXLPipeline"]}, {"id": 235, "type": "DiffusersMVModelMakeup", "pos": [824.2053833007812, 238.37274169921875], "size": [275.4878845214844, 218], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "pipeline", "name": "pipeline", "type": "PIPELINE", "link": 408}, {"localized_name": "scheduler", "name": "scheduler", "type": "SCHEDULER", "link": 412}, {"localized_name": "autoencoder", "name": "autoencoder", "type": "AUTOENCODER", "link": 407}, {"localized_name": "load_mvadapter", "name": "load_mvadapter", "type": "BOOLEAN", "widget": {"name": "load_mvadapter"}, "link": null}, {"localized_name": "adapter_path", "name": "adapter_path", "type": "STRING", "widget": {"name": "adapter_path"}, "link": null}, {"localized_name": "adapter_name", "name": "adapter_name", "type": "COMBO", "widget": {"name": "adapter_name"}, "link": null}, {"localized_name": "num_views", "name": "num_views", "type": "INT", "widget": {"name": "num_views"}, "link": null}, {"localized_name": "enable_vae_slicing", "name": "enable_vae_slicing", "shape": 7, "type": "BOOLEAN", "widget": {"name": "enable_vae_slicing"}, "link": null}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "shape": 7, "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}], "outputs": [{"localized_name": "PIPELINE", "name": "PIPELINE", "type": "PIPELINE", "links": [405]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "DiffusersMVModelMakeup"}, "widgets_values": [true, "huanngzh/mv-adapter", "mvadapter_i2mv_sdxl.safetensors", 6, true, false]}, {"id": 85, "type": "ImageFromBatch", "pos": [793.72607421875, 574.951171875], "size": [315, 82], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 164}, {"localized_name": "批次索引", "name": "batch_index", "type": "INT", "widget": {"name": "batch_index"}, "link": null}, {"localized_name": "长度", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [261]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageFromBatch"}, "widgets_values": [2, 1]}, {"id": 107, "type": "SaveImage", "pos": [3529.230224609375, 148.10179138183594], "size": [1053.8526611328125, 991.3258666992188], "flags": {}, "order": 54, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 391}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["D--"]}, {"id": 225, "type": "Image Comparer (rgthree)", "pos": [3097.55126953125, 1458.6688232421875], "size": [1785.3087158203125, 1001.4569091796875], "flags": {}, "order": 51, "mode": 4, "inputs": [{"dir": 3, "label": "图像_A", "name": "image_a", "type": "IMAGE", "link": 384}, {"dir": 3, "label": "图像_B", "name": "image_b", "type": "IMAGE", "link": 385}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_vsuyp_00001_.png&type=temp&subfolder=&rand=0.23284475192460363"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_vsuyp_00002_.png&type=temp&subfolder=&rand=0.061674841640609435"}]]}, {"id": 226, "type": "LayerUtility: JoyCaption2", "pos": [-334.099609375, 1022.7433471679688], "size": [303.7271423339844, 342], "flags": {}, "order": 35, "mode": 4, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 386}, {"localized_name": "额外选项", "name": "extra_options", "shape": 7, "type": "JoyCaption2ExtraOption", "link": null}, {"localized_name": "LLM模型", "name": "llm_model", "type": "COMBO", "widget": {"name": "llm_model"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "数据类型", "name": "dtype", "type": "COMBO", "widget": {"name": "dtype"}, "link": null}, {"localized_name": "VLM LoRA", "name": "vlm_lora", "type": "COMBO", "widget": {"name": "vlm_lora"}, "link": null}, {"localized_name": "字幕类型", "name": "caption_type", "type": "COMBO", "widget": {"name": "caption_type"}, "link": null}, {"localized_name": "字幕长度", "name": "caption_length", "type": "COMBO", "widget": {"name": "caption_length"}, "link": null}, {"localized_name": "用户提示", "name": "user_prompt", "type": "STRING", "widget": {"name": "user_prompt"}, "link": null}, {"localized_name": "最大新令牌数", "name": "max_new_tokens", "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "顶部P", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "温度", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}, {"localized_name": "使用全局模型", "name": "use_global_model", "type": "BOOLEAN", "widget": {"name": "use_global_model"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "shape": 6, "type": "STRING", "links": [387]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerUtility: JoyCaption2"}, "widgets_values": ["Orenguteng/Llama-3.1-8B-Lexi-Uncensored-V2", "cuda", "nf4", "text_model", "Descriptive", "any", "", 300, 0.9, 0.6, false, false], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 7, "type": "LoadImage", "pos": [-803.653564453125, 69.9461898803711], "size": [409.74005126953125, 393.2633056640625], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [7, 414]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["D97BB410599041C84281510382F51A7F.png", "image"]}, {"id": 105, "type": "ShowText|pysssss", "pos": [-402.1961975097656, 1412.425537109375], "size": [397.51019287109375, 130.20779418945312], "flags": {}, "order": 40, "mode": 4, "inputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "STRING", "link": 387}], "outputs": [{"label": "字符串", "localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "slot_index": 0, "links": [211, 212]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["\"Multi-Angle Character Model: <PERSON> Girl in Yellow Sweater and White Skirt\n\nThis digital character model presents a young girl in a variety of poses, showcasing her from multiple angles. The subject is depicted in a bright yellow sweater with a white collar, paired with a short white skirt. Her hair is styled in loose, curly waves, and her eyes are a deep shade of red. The model's back is rendered in a single, solid color, providing a clear view of her sweater and skirt. The overall design is simple and innocent, making this character model suitable for a wide range of applications.\""]}, {"id": 232, "type": "CR Text", "pos": [-36.33052444458008, 568.7977294921875], "size": [400, 200], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 417}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [399]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["1girl，2D，anime style,short hair,white hair"]}, {"id": 242, "type": "LayerUtility: JoyCaption2", "pos": [-312.7110595703125, 111.17560577392578], "size": [303.7271423339844, 342], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 414}, {"localized_name": "额外选项", "name": "extra_options", "shape": 7, "type": "JoyCaption2ExtraOption", "link": null}, {"localized_name": "LLM模型", "name": "llm_model", "type": "COMBO", "widget": {"name": "llm_model"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "数据类型", "name": "dtype", "type": "COMBO", "widget": {"name": "dtype"}, "link": null}, {"localized_name": "VLM LoRA", "name": "vlm_lora", "type": "COMBO", "widget": {"name": "vlm_lora"}, "link": null}, {"localized_name": "字幕类型", "name": "caption_type", "type": "COMBO", "widget": {"name": "caption_type"}, "link": null}, {"localized_name": "字幕长度", "name": "caption_length", "type": "COMBO", "widget": {"name": "caption_length"}, "link": null}, {"localized_name": "用户提示", "name": "user_prompt", "type": "STRING", "widget": {"name": "user_prompt"}, "link": null}, {"localized_name": "最大新令牌数", "name": "max_new_tokens", "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "顶部P", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "温度", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}, {"localized_name": "使用全局模型", "name": "use_global_model", "type": "BOOLEAN", "widget": {"name": "use_global_model"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "shape": 6, "type": "STRING", "links": [416]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerUtility: JoyCaption2"}, "widgets_values": ["Orenguteng/Llama-3.1-8B-Lexi-Uncensored-V2", "cuda", "nf4", "text_model", "Descriptive", "any", "", 300, 0.9, 0.6, false, false], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 243, "type": "ShowText|pysssss", "pos": [-280.62939453125, 615.7542724609375], "size": [140, 26], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 416}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [417]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}}, {"id": 90, "type": "PreviewImage", "pos": [2418.497314453125, 192.8568115234375], "size": [998.0074462890625, 730.0913696289062], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 172}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 77, "type": "PreviewImage", "pos": [1670.4755859375, 96.19507598876953], "size": [820.3285522460938, 843.8599853515625], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 158}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 6, "type": "DiffusersMVSampler", "pos": [1134.226806640625, 88.04185485839844], "size": [398.4827880859375, 394], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "pipeline", "localized_name": "pipeline", "name": "pipeline", "type": "PIPELINE", "link": 405}, {"label": "reference_image", "localized_name": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 9}, {"label": "controlnet_image", "localized_name": "controlnet_image", "name": "controlnet_image", "shape": 7, "type": "IMAGE", "link": null}, {"label": "azimuth_degrees", "localized_name": "azimuth_degrees", "name": "azimuth_degrees", "shape": 7, "type": "LIST", "link": 11}, {"localized_name": "num_views", "name": "num_views", "type": "INT", "widget": {"name": "num_views"}, "link": null}, {"label": "prompt", "localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 399}, {"localized_name": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}, "link": null}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "controlnet_conditioning_scale", "name": "controlnet_conditioning_scale", "shape": 7, "type": "FLOAT", "widget": {"name": "controlnet_conditioning_scale"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [52, 158, 160, 162, 164, 166]}], "properties": {"cnr_id": "comfyui-mvadapter", "ver": "1.0.1", "Node name for S&R": "DiffusersMVSampler"}, "widgets_values": [6, "", "watermark, ugly, deformed, noisy, blurry, low contrast", 768, 768, 50, 3, 0, "fixed", 1]}, {"id": 148, "type": "Bounded Image Crop with Mask", "pos": [860.5392456054688, 876.3538818359375], "size": [315, 166], "flags": {"collapsed": true}, "order": 26, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 249}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 389}, {"localized_name": "padding_left", "name": "padding_left", "type": "INT", "widget": {"name": "padding_left"}, "link": null}, {"localized_name": "padding_right", "name": "padding_right", "type": "INT", "widget": {"name": "padding_right"}, "link": null}, {"localized_name": "padding_top", "name": "padding_top", "type": "INT", "widget": {"name": "padding_top"}, "link": null}, {"localized_name": "padding_bottom", "name": "padding_bottom", "type": "INT", "widget": {"name": "padding_bottom"}, "link": null}, {"localized_name": "return_list", "name": "return_list", "shape": 7, "type": "BOOLEAN", "widget": {"name": "return_list"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": null}, {"label": "边界", "localized_name": "IMAGE_BOUNDS", "name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "slot_index": 1, "links": [247, 322, 323, 324]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [32, 32, 32, 32, false]}], "links": [[6, 9, 0, 8, 0, "FUNCTION"], [7, 7, 0, 8, 1, "IMAGE"], [8, 8, 0, 10, 0, "IMAGE"], [9, 8, 0, 6, 1, "IMAGE"], [11, 12, 0, 6, 3, "LIST"], [52, 6, 0, 38, 0, "*"], [158, 6, 0, 77, 0, "IMAGE"], [160, 6, 0, 81, 0, "IMAGE"], [162, 6, 0, 83, 0, "IMAGE"], [164, 6, 0, 85, 0, "IMAGE"], [166, 6, 0, 87, 0, "IMAGE"], [172, 89, 0, 90, 0, "IMAGE"], [173, 114, 0, 91, 0, "CLIP"], [175, 95, 0, 92, 0, "NOISE"], [176, 94, 0, 92, 1, "GUIDER"], [177, 93, 0, 92, 2, "SAMPLER"], [178, 112, 0, 92, 3, "SIGMAS"], [179, 103, 0, 92, 4, "LATENT"], [180, 97, 0, 94, 0, "MODEL"], [181, 96, 0, 94, 1, "CONDITIONING"], [182, 91, 0, 96, 0, "CONDITIONING"], [184, 117, 1, 97, 3, "INT"], [185, 117, 2, 97, 4, "INT"], [188, 117, 0, 102, 0, "IMAGE"], [189, 101, 0, 102, 1, "VAE"], [190, 102, 0, 103, 0, "*"], [193, 92, 0, 106, 0, "LATENT"], [194, 101, 0, 106, 1, "VAE"], [199, 97, 0, 112, 0, "MODEL"], [201, 117, 2, 115, 0, "*"], [202, 117, 1, 116, 0, "*"], [205, 89, 0, 119, 0, "*"], [209, 89, 0, 117, 0, "IMAGE"], [210, 113, 0, 97, 0, "MODEL"], [211, 105, 0, 91, 1, "STRING"], [212, 105, 0, 122, 0, "*"], [213, 106, 0, 123, 0, "*"], [247, 148, 1, 146, 1, "IMAGE_BOUNDS"], [249, 81, 0, 148, 0, "IMAGE"], [250, 81, 0, 146, 0, "IMAGE"], [256, 83, 0, 151, 0, "IMAGE"], [261, 85, 0, 154, 0, "IMAGE"], [268, 87, 0, 157, 0, "IMAGE"], [322, 148, 1, 151, 1, "IMAGE_BOUNDS"], [323, 148, 1, 157, 1, "IMAGE_BOUNDS"], [324, 148, 1, 154, 1, "IMAGE_BOUNDS"], [325, 146, 0, 89, 0, "IMAGE"], [326, 151, 0, 89, 1, "IMAGE"], [327, 154, 0, 89, 5, "IMAGE"], [328, 157, 0, 89, 6, "IMAGE"], [345, 201, 0, 202, 0, "INT"], [346, 202, 0, 203, 0, "INT"], [347, 202, 0, 204, 0, "INT"], [349, 202, 0, 205, 1, "INT"], [350, 201, 1, 205, 2, "INT"], [352, 202, 0, 206, 1, "INT"], [353, 201, 1, 206, 2, "INT"], [354, 202, 0, 206, 3, "INT"], [356, 202, 0, 207, 1, "INT"], [357, 201, 1, 207, 2, "INT"], [358, 203, 0, 207, 3, "INT"], [360, 202, 0, 208, 1, "INT"], [361, 201, 1, 208, 2, "INT"], [362, 204, 0, 208, 3, "INT"], [367, 205, 0, 213, 0, "IMAGE"], [368, 206, 0, 214, 0, "IMAGE"], [369, 207, 0, 215, 0, "IMAGE"], [370, 208, 0, 216, 0, "IMAGE"], [379, 106, 0, 201, 0, "IMAGE"], [380, 106, 0, 205, 0, "IMAGE"], [381, 106, 0, 206, 0, "IMAGE"], [382, 106, 0, 207, 0, "IMAGE"], [383, 106, 0, 208, 0, "IMAGE"], [384, 106, 0, 225, 0, "IMAGE"], [385, 89, 0, 225, 1, "IMAGE"], [386, 89, 0, 226, 0, "IMAGE"], [387, 226, 0, 105, 0, "STRING"], [388, 81, 0, 227, 0, "IMAGE"], [389, 227, 1, 148, 1, "MASK"], [390, 106, 0, 228, 0, "IMAGE"], [391, 228, 0, 107, 0, "IMAGE"], [392, 205, 0, 224, 0, "IMAGE"], [393, 206, 0, 229, 0, "IMAGE"], [394, 229, 0, 221, 0, "IMAGE"], [395, 207, 0, 230, 0, "IMAGE"], [396, 208, 0, 231, 0, "IMAGE"], [397, 230, 0, 222, 0, "IMAGE"], [398, 231, 0, 223, 0, "IMAGE"], [399, 232, 0, 6, 5, "STRING"], [401, 98, 0, 113, 0, "MODEL"], [405, 235, 0, 6, 0, "PIPELINE"], [407, 39, 0, 235, 2, "AUTOENCODER"], [408, 236, 0, 235, 0, "PIPELINE"], [412, 239, 0, 235, 1, "SCHEDULER"], [413, 236, 0, 239, 0, "PIPELINE"], [414, 7, 0, 242, 0, "IMAGE"], [416, 242, 0, 243, 0, "STRING"], [417, 243, 0, 232, 0, "STRING"]], "groups": [{"id": 1, "title": "上传图片/选择信息", "bounding": [-447.0758056640625, 14.063920021057129, 879.9840087890625, 932.0968627929688], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "base1", "bounding": [447.0117492675781, 9.99242115020752, 1075.2271728515625, 466.0299377441406], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "out", "bounding": [1548.5177001953125, 24.944690704345703, 3351.929443359375, 2505.14013671875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "分", "bounding": [459.181396484375, 499.6787414550781, 1073.4404296875, 457.3753662109375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "优化", "bounding": [-419.2456970214844, 981.903076171875, 1956.1929931640625, 758.2566528320312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 11, "title": "拆分", "bounding": [392.0805358886719, 1759.4677734375, 1139.112548828125, 768.3351440429688], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.4090909090909101, "offset": [-339.8042932244723, -298.6760391455605]}, "frontendVersion": "1.18.9", "ue_links": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}