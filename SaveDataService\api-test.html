<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CafeLabs RESTful API 测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 20px;
        }
        .api-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .api-method {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .method-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .method-header:hover {
            background: #e9ecef;
        }
        .method-name {
            font-weight: bold;
            color: #495057;
        }
        .method-description {
            color: #6c757d;
            font-size: 0.9em;
        }
        .http-method {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .method-body {
            padding: 20px;
            display: none;
        }
        .method-body.active {
            display: block;
        }
        .section {
            margin-bottom: 20px;
        }
        .section h4 {
            margin: 0 0 10px 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .parameter {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .parameter-name {
            font-weight: bold;
            color: #007bff;
        }
        .parameter-type {
            color: #28a745;
            font-family: monospace;
        }
        .parameter-required {
            color: #dc3545;
            font-size: 0.8em;
        }
        .parameter-optional {
            color: #6c757d;
            font-size: 0.8em;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .test-form {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .response-area {
            margin-top: 15px;
        }
        .response-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .response-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            display: none;
            color: #007bff;
        }
        .expand-icon {
            transition: transform 0.3s ease;
        }
        .expand-icon.rotated {
            transform: rotate(180deg);
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-loading {
            background: #ffc107;
            animation: pulse 1.5s infinite;
        }
        .status-success {
            background: #28a745;
        }
        .status-error {
            background: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>CafeLabs RESTful API 测试工具</h1>
            <p>自动生成的 API 测试界面</p>
        </div>
        
        <div class="content">
            <div class="api-info">
                <h3>API 信息</h3>
                <p><strong>基础 URL:</strong> <span id="baseUrl">加载中...</span></p>
                <p><strong>版本:</strong> <span id="apiVersion">加载中...</span></p>
                <p><strong>生成时间:</strong> <span id="generatedAt">加载中...</span></p>
                <p><strong>状态:</strong> <span class="status-indicator status-loading"></span><span id="apiStatus">正在加载 API 信息...</span></p>
            </div>
            
            <div id="apiMethods">
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <div class="loading" style="display: block;">
                        <div style="font-size: 1.2em; margin-bottom: 10px;">正在加载 API 信息...</div>
                        <div>请稍候，正在从服务器获取最新的 API 描述</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API 数据
        let apiData = null;
        
        // 页面加载完成后获取 API 信息
        document.addEventListener('DOMContentLoaded', function() {
            loadApiInfo();
        });
        
        // 加载 API 信息
        async function loadApiInfo() {
            try {
                // 修改为加载所有 API 类，而不是只加载 AccountManage
                const response = await fetch('http://127.0.0.1:7778/getrestful');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const allApiData = await response.json();

                if (allApiData.error) {
                    throw new Error(allApiData.error);
                }

                // 将所有 API 类合并为一个统一的结构
                apiData = {
                    Name: "所有 RESTful API",
                    Description: "系统中所有继承 RESTfulAPIBase 的类的 API 接口",
                    BaseUrl: "http://127.0.0.1:7778",
                    Version: "1.0",
                    GeneratedAt: new Date().toISOString(),
                    Methods: []
                };

                // 检查返回的数据结构
                console.log('原始 API 数据:', allApiData);

                // 处理新的数据结构：{ GeneratedAt, TotalApiClasses, ApiClasses: { ClassName: {...}, ... } }
                const apiClasses = allApiData.ApiClasses || allApiData;

                // 遍历所有 API 类，合并方法
                for (const [className, classData] of Object.entries(apiClasses)) {
                    console.log(`处理类: ${className}`, classData);
                    if (classData && classData.Methods) {
                        // 为每个方法添加类名前缀，便于区分
                        classData.Methods.forEach(method => {
                            method.ClassName = className;
                            method.MethodName = `${className}.${method.MethodName}`;
                            apiData.Methods.push(method);
                        });
                    }
                }

                renderApiInfo();
                renderApiMethods();
                updateStatus('success', `API 信息加载成功，共找到 ${apiData.Methods.length} 个方法`);

            } catch (error) {
                console.error('加载 API 信息失败:', error);
                updateStatus('error', `加载失败: ${error.message}`);
                document.getElementById('apiMethods').innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #dc3545;">
                        <h3>加载失败</h3>
                        <p>无法从服务器获取 API 信息: ${error.message}</p>
                        <button class="btn" onclick="loadApiInfo()">重试</button>
                    </div>
                `;
            }
        }
        
        // 更新状态指示器
        function updateStatus(status, message) {
            const indicator = document.querySelector('.status-indicator');
            const statusText = document.getElementById('apiStatus');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = message;
        }
        
        // 渲染 API 基本信息
        function renderApiInfo() {
            document.getElementById('baseUrl').textContent = apiData.BaseUrl || 'N/A';
            document.getElementById('apiVersion').textContent = apiData.Version || 'N/A';
            document.getElementById('generatedAt').textContent = new Date(apiData.GeneratedAt).toLocaleString() || 'N/A';
        }
        
        // 渲染 API 方法列表
        function renderApiMethods() {
            const container = document.getElementById('apiMethods');
            
            if (!apiData.Methods || apiData.Methods.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #6c757d;">没有找到 API 方法</div>';
                return;
            }
            
            const methodsHtml = apiData.Methods.map((method, index) => `
                <div class="api-method">
                    <div class="method-header" onclick="toggleMethod(${index})">
                        <div>
                            <div class="method-name">${method.MethodName}</div>
                            <div class="method-description">${method.Description}</div>
                        </div>
                        <div>
                            <span class="http-method">${method.HttpMethod}</span>
                            <span class="expand-icon" id="icon-${index}">▼</span>
                        </div>
                    </div>
                    <div class="method-body" id="method-${index}">
                        ${renderMethodBody(method, index)}
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = methodsHtml;
        }
        
        // 渲染方法详情
        function renderMethodBody(method, index) {
            return `
                <div class="section">
                    <h4>接口信息</h4>
                    <p><strong>路径:</strong> ${method.Path}</p>
                    <p><strong>方法:</strong> ${method.HttpMethod}</p>
                </div>
                
                <div class="section">
                    <h4>参数列表</h4>
                    ${method.Parameters.length > 0 ? 
                        method.Parameters.map(param => `
                            <div class="parameter">
                                <div>
                                    <span class="parameter-name">${param.Name}</span>
                                    <span class="parameter-type">(${param.Type})</span>
                                    <span class="${param.Required ? 'parameter-required' : 'parameter-optional'}">
                                        ${param.Required ? '必需' : '可选'}
                                    </span>
                                </div>
                                <div style="margin-top: 5px; color: #6c757d;">${param.Description}</div>
                                ${param.DefaultValue !== null ? `<div style="margin-top: 5px; font-size: 0.8em; color: #6c757d;">默认值: ${param.DefaultValue}</div>` : ''}
                            </div>
                        `).join('') : 
                        '<p style="color: #6c757d;">无参数</p>'
                    }
                </div>
                
                <div class="section">
                    <h4>返回类型</h4>
                    <div class="parameter">
                        <div>
                            <span class="parameter-type">${method.ReturnType.Type}</span>
                        </div>
                        <div style="margin-top: 5px; color: #6c757d;">${method.ReturnType.Description}</div>
                        ${method.ReturnType.Properties.length > 0 ? `
                            <div style="margin-top: 10px;">
                                <strong>属性:</strong>
                                ${method.ReturnType.Properties.map(prop => `
                                    <div style="margin-left: 20px; margin-top: 5px;">
                                        <span class="parameter-name">${prop.Name}</span>
                                        <span class="parameter-type">(${prop.Type})</span>
                                        - ${prop.Description}
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                <div class="section">
                    <h4>示例请求</h4>
                    <div class="code-block">${JSON.stringify(method.ExampleRequest, null, 2)}</div>
                </div>
                
                <div class="section">
                    <h4>示例响应</h4>
                    <div class="code-block">${JSON.stringify(method.ExampleResponse, null, 2)}</div>
                </div>
                
                <div class="test-form">
                    <h4>在线测试</h4>
                    <form onsubmit="testApi(event, ${index})">
                        ${method.Parameters.map(param => `
                            <div class="form-group">
                                <label for="param-${index}-${param.Name}">
                                    ${param.Name} (${param.Type}) ${param.Required ? '*' : ''}
                                </label>
                                <input 
                                    type="text" 
                                    id="param-${index}-${param.Name}"
                                    name="${param.Name}"
                                    placeholder="${param.Description}"
                                    value="${param.ExampleValue || ''}"
                                    ${param.Required ? 'required' : ''}
                                />
                            </div>
                        `).join('')}
                        
                        <button type="submit" class="btn">发送请求</button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm(${index})">重置</button>
                    </form>
                    
                    <div class="response-area" id="response-${index}"></div>
                </div>
            `;
        }
        
        // 切换方法展开/收起
        function toggleMethod(index) {
            const methodBody = document.getElementById(`method-${index}`);
            const icon = document.getElementById(`icon-${index}`);
            
            if (methodBody.classList.contains('active')) {
                methodBody.classList.remove('active');
                icon.classList.remove('rotated');
            } else {
                methodBody.classList.add('active');
                icon.classList.add('rotated');
            }
        }
        
        // 测试 API
        async function testApi(event, methodIndex) {
            event.preventDefault();
            
            const method = apiData.Methods[methodIndex];
            const form = event.target;
            const responseArea = document.getElementById(`response-${methodIndex}`);
            
            // 收集表单数据
            const requestData = {};
            method.Parameters.forEach(param => {
                const input = form.elements[param.Name];
                if (input && input.value) {
                    // 尝试转换数据类型
                    let value = input.value;
                    if (param.Type === 'int' || param.Type === 'uint') {
                        value = parseInt(value);
                    } else if (param.Type === 'long' || param.Type === 'ulong') {
                        value = parseInt(value);
                    } else if (param.Type === 'bool') {
                        value = value.toLowerCase() === 'true';
                    } else if (param.Type === 'double' || param.Type === 'float') {
                        value = parseFloat(value);
                    } else if (param.Type.includes('[]')) {
                        // 数组类型，尝试解析 JSON
                        try {
                            value = JSON.parse(value);
                        } catch (e) {
                            // 如果不是 JSON，按逗号分割
                            value = value.split(',').map(s => s.trim());
                        }
                    }
                    requestData[param.Name] = value;
                }
            });
            
            // 显示加载状态
            responseArea.innerHTML = `
                <div class="code-block">
                    <div class="loading" style="display: block;">正在发送请求...</div>
                </div>
            `;
            
            try {
                // 构造正确的请求格式 - 直接发送参数，不需要包装
                const requestPayload = requestData;

                // 发送请求
                const response = await fetch(`http://127.0.0.1:7778${method.Path}`, {
                    method: method.HttpMethod,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestPayload)
                });

                const responseData = await response.json();
                
                // 显示响应
                responseArea.innerHTML = `
                    <h5>响应结果 (${response.status})</h5>
                    <div class="code-block ${response.ok ? 'response-success' : 'response-error'}">
                        ${JSON.stringify(responseData, null, 2)}
                    </div>
                `;
                
            } catch (error) {
                responseArea.innerHTML = `
                    <h5>请求失败</h5>
                    <div class="code-block response-error">
                        错误: ${error.message}
                    </div>
                `;
            }
        }
        
        // 重置表单
        function resetForm(methodIndex) {
            const method = apiData.Methods[methodIndex];
            method.Parameters.forEach(param => {
                const input = document.getElementById(`param-${methodIndex}-${param.Name}`);
                if (input) {
                    input.value = param.ExampleValue || '';
                }
            });
            
            // 清空响应区域
            document.getElementById(`response-${methodIndex}`).innerHTML = '';
        }
    </script>
</body>
</html>
