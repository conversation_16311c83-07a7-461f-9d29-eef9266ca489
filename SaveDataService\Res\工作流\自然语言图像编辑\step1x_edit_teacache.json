{"id": "cf4efc13-7f12-4f04-865c-fd0fc07df95b", "revision": 0, "last_node_id": 47, "last_link_id": 79, "nodes": [{"id": 4, "type": "PreviewImage", "pos": [1482.3309326171875, 213.58203125], "size": [401.82537841796875, 635.7321166992188], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 47}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 25, "type": "Step1XEditTeaCacheGenerate", "pos": [1132.9986572265625, 213.18399047851562], "size": [344.3999938964844, 282], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 44}, {"localized_name": "input_image", "name": "input_image", "type": "IMAGE", "link": 46}, {"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}, "link": null}, {"localized_name": "num_steps", "name": "num_steps", "type": "INT", "widget": {"name": "num_steps"}, "link": null}, {"localized_name": "cfg_guidance", "name": "cfg_guidance", "type": "FLOAT", "widget": {"name": "cfg_guidance"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "size_level", "name": "size_level", "type": "INT", "widget": {"name": "size_level"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [47]}], "properties": {"cnr_id": "comfyui_step1x-edit", "ver": "1.1.1", "Node name for S&R": "Step1XEditTeaCacheGenerate"}, "widgets_values": ["给这个女生的脖子上戴一个带有红宝石的吊坠。", "", 10, 6, 1939, "fixed", 768]}, {"id": 24, "type": "Step1XEditTeaCacheModelLoader", "pos": [1133.7283935546875, -49.122657775878906], "size": [749.1290893554688, 226], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "diffusion_model", "name": "diffusion_model", "type": "COMBO", "widget": {"name": "diffusion_model"}, "link": null}, {"localized_name": "vae", "name": "vae", "type": "COMBO", "widget": {"name": "vae"}, "link": null}, {"localized_name": "text_encoder", "name": "text_encoder", "type": "COMBO", "widget": {"name": "text_encoder"}, "link": null}, {"localized_name": "dtype", "name": "dtype", "type": "COMBO", "widget": {"name": "dtype"}, "link": null}, {"localized_name": "quantized", "name": "quantized", "type": "BOOLEAN", "widget": {"name": "quantized"}, "link": null}, {"localized_name": "offload", "name": "offload", "type": "BOOLEAN", "widget": {"name": "offload"}, "link": null}, {"localized_name": "teacache_threshold", "name": "teacache_threshold", "type": "COMBO", "widget": {"name": "teacache_threshold"}, "link": null}, {"localized_name": "verbose", "name": "verbose", "type": "BOOLEAN", "widget": {"name": "verbose"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "slot_index": 0, "links": [44]}], "properties": {"cnr_id": "comfyui_step1x-edit", "ver": "1.1.1", "Node name for S&R": "Step1XEditTeaCacheModelLoader"}, "widgets_values": ["step1x-edit-i1258-FP8.safetensors", "stepx1vae.safetensors", "Qwen2.5-VL-7B-Instruct", "bfloat16", true, false, "0.25", false]}, {"id": 2, "type": "LoadImage", "pos": [1129.97900390625, 530.3847045898438], "size": [346.0010681152344, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [46]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["jimeng-2025-05-06-722-一个二次元开放世界的游戏的的游戏界面.jpeg", "image"]}], "links": [[44, 24, 0, 25, 0, "MODEL"], [46, 2, 0, 25, 1, "IMAGE"], [47, 25, 0, 4, 0, "IMAGE"]], "groups": [{"id": 5, "title": "Group", "bounding": [1119.97900390625, -122.72265625, 774.177734375, 982.036865234375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.9646149645000012, "offset": [-507.5302521848004, -370.6056359226152]}}, "version": 0.4}