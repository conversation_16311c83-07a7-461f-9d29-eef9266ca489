{"id": "51c38fb2-759d-4950-8edc-e731d276573a", "revision": 0, "last_node_id": 200, "last_link_id": 220, "nodes": [{"id": 69, "type": "SaveImage", "pos": [987, 96], "size": [537.5791625976562, 858.9998168945312], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 191}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 176, "type": "CLIPTextEncode", "pos": [148, 273], "size": [400, 200], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 185}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [187]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["best quality，ultra-detailed"]}, {"id": 177, "type": "CLIPTextEncode", "pos": [151, 551], "size": [400, 200], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 186}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [188]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 59, "type": "DualCLIPLoader", "pos": [-304.4459228515625, 381.2047424316406], "size": [315, 130], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [185, 186]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 60, "type": "UNETLoader", "pos": [-348.1264953613281, 247.670654296875], "size": [315, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [199]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev-fp8.safetensors", "fp8_e5m2"]}, {"id": 196, "type": "VAELoader", "pos": [-187.8325653076172, -417.8406982421875], "size": [270, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [211]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["sdxl_vae.safetensors"]}, {"id": 192, "type": "WildcardEncode //Inspire", "pos": [222.70318603515625, -709.*************], "size": [400, 370], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 203}, {"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 204}, {"localized_name": "token_normalization", "name": "token_normalization", "type": "COMBO", "widget": {"name": "token_normalization"}, "link": null}, {"localized_name": "weight_interpretation", "name": "weight_interpretation", "type": "COMBO", "widget": {"name": "weight_interpretation"}, "link": null}, {"localized_name": "wildcard_text", "name": "wildcard_text", "type": "STRING", "widget": {"name": "wildcard_text"}, "link": null}, {"localized_name": "populated_text", "name": "populated_text", "type": "STRING", "widget": {"name": "populated_text"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "Select to add LoRA", "name": "Select to add LoRA", "type": "COMBO", "widget": {"name": "Select to add LoRA"}, "link": null}, {"localized_name": "Select to add Wildcard", "name": "Select to add Wildcard", "type": "COMBO", "widget": {"name": "Select to add Wildcard"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "links": [205]}, {"localized_name": "clip", "name": "clip", "type": "CLIP", "links": [209]}, {"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "links": [207]}, {"localized_name": "populated_text", "name": "populated_text", "type": "STRING", "links": null}], "properties": {"cnr_id": "comfyui-inspire-pack", "ver": "1.18.0", "Node name for S&R": "WildcardEncode //Inspire"}, "widgets_values": ["length+mean", "A1111", "", "", "populate", "Select the LoRA to add to the text", "Select the Wildcard to add to the text", 0, "randomize"]}, {"id": 187, "type": "CheckpointLoaderSimple", "pos": [-199.88665771484375, -282.78607177734375], "size": [385.2849426269531, 183.31101989746094], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [203]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [204]}, {"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["waiNSFWIllustrious_v140.safetensors"]}, {"id": 197, "type": "PreviewImage", "pos": [1124.7137451171875, -707.50048828125], "size": [140, 26], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 213}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}}, {"id": 194, "type": "UltimateSDUpscale", "pos": [807.3475952148438, -695.7764282226562], "size": [315, 614], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 217}, {"label": "模型", "localized_name": "model", "name": "model", "type": "MODEL", "link": 205}, {"label": "正面条件", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 207}, {"label": "负面条件", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 210}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 211}, {"label": "放大模型", "localized_name": "upscale_model", "name": "upscale_model", "type": "UPSCALE_MODEL", "link": 212}, {"localized_name": "upscale_by", "name": "upscale_by", "type": "FLOAT", "widget": {"name": "upscale_by"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": 220}, {"localized_name": "mode_type", "name": "mode_type", "type": "COMBO", "widget": {"name": "mode_type"}, "link": null}, {"localized_name": "tile_width", "name": "tile_width", "type": "INT", "widget": {"name": "tile_width"}, "link": null}, {"localized_name": "tile_height", "name": "tile_height", "type": "INT", "widget": {"name": "tile_height"}, "link": null}, {"localized_name": "mask_blur", "name": "mask_blur", "type": "INT", "widget": {"name": "mask_blur"}, "link": null}, {"localized_name": "tile_padding", "name": "tile_padding", "type": "INT", "widget": {"name": "tile_padding"}, "link": null}, {"localized_name": "seam_fix_mode", "name": "seam_fix_mode", "type": "COMBO", "widget": {"name": "seam_fix_mode"}, "link": null}, {"localized_name": "seam_fix_denoise", "name": "seam_fix_denoise", "type": "FLOAT", "widget": {"name": "seam_fix_denoise"}, "link": null}, {"localized_name": "seam_fix_width", "name": "seam_fix_width", "type": "INT", "widget": {"name": "seam_fix_width"}, "link": null}, {"localized_name": "seam_fix_mask_blur", "name": "seam_fix_mask_blur", "type": "INT", "widget": {"name": "seam_fix_mask_blur"}, "link": null}, {"localized_name": "seam_fix_padding", "name": "seam_fix_padding", "type": "INT", "widget": {"name": "seam_fix_padding"}, "link": null}, {"localized_name": "force_uniform_tiles", "name": "force_uniform_tiles", "type": "BOOLEAN", "widget": {"name": "force_uniform_tiles"}, "link": null}, {"localized_name": "tiled_decode", "name": "tiled_decode", "type": "BOOLEAN", "widget": {"name": "tiled_decode"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [213]}], "properties": {"cnr_id": "comfyui_ultimatesdupscale", "ver": "1.1.3", "Node name for S&R": "UltimateSDUpscale"}, "widgets_values": [2.0000000000000004, 263153014253114, "randomize", 30, 5, "dpmpp_2m", "sgm_uniform", 0.3, "Linear", 1024, 1024, 8, 32, "None", 1, 64, 8, 16, true, false]}, {"id": 195, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [234.87774658203125, -273.8580322265625], "size": [400, 200], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 209}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}, {"localized_name": "token_normalization", "name": "token_normalization", "type": "COMBO", "widget": {"name": "token_normalization"}, "link": null}, {"localized_name": "weight_interpretation", "name": "weight_interpretation", "type": "COMBO", "widget": {"name": "weight_interpretation"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [210]}], "properties": {"cnr_id": "ComfyUI_ADV_CLIP_emb", "ver": "63984deefb005da1ba90a1175e21d91040da38ab", "Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "length+mean", "A1111"]}, {"id": 71, "type": "LoadImage", "pos": [-1179.09130859375, -51.90321731567383], "size": [315, 314], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [216]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["D97BB410599041C84281510382F51A7F.png", "image"]}, {"id": 199, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [-793.1459350585938, -84.8319320678711], "size": [363.6851501464844, 330], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 216}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": null}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [217, 218]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "longest", 1024, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 58, "type": "VAELoader", "pos": [192.03890991210938, 793.8308715820312], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [189]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 175, "type": "UltimateSDUpscale", "pos": [639.4572143554688, 220.26327514648438], "size": [315, 614], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 218}, {"label": "模型", "localized_name": "model", "name": "model", "type": "MODEL", "link": 199}, {"label": "正面条件", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 187}, {"label": "负面条件", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 188}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 189}, {"label": "放大模型", "localized_name": "upscale_model", "name": "upscale_model", "type": "UPSCALE_MODEL", "link": 190}, {"localized_name": "upscale_by", "name": "upscale_by", "type": "FLOAT", "widget": {"name": "upscale_by"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": 219}, {"localized_name": "mode_type", "name": "mode_type", "type": "COMBO", "widget": {"name": "mode_type"}, "link": null}, {"localized_name": "tile_width", "name": "tile_width", "type": "INT", "widget": {"name": "tile_width"}, "link": null}, {"localized_name": "tile_height", "name": "tile_height", "type": "INT", "widget": {"name": "tile_height"}, "link": null}, {"localized_name": "mask_blur", "name": "mask_blur", "type": "INT", "widget": {"name": "mask_blur"}, "link": null}, {"localized_name": "tile_padding", "name": "tile_padding", "type": "INT", "widget": {"name": "tile_padding"}, "link": null}, {"localized_name": "seam_fix_mode", "name": "seam_fix_mode", "type": "COMBO", "widget": {"name": "seam_fix_mode"}, "link": null}, {"localized_name": "seam_fix_denoise", "name": "seam_fix_denoise", "type": "FLOAT", "widget": {"name": "seam_fix_denoise"}, "link": null}, {"localized_name": "seam_fix_width", "name": "seam_fix_width", "type": "INT", "widget": {"name": "seam_fix_width"}, "link": null}, {"localized_name": "seam_fix_mask_blur", "name": "seam_fix_mask_blur", "type": "INT", "widget": {"name": "seam_fix_mask_blur"}, "link": null}, {"localized_name": "seam_fix_padding", "name": "seam_fix_padding", "type": "INT", "widget": {"name": "seam_fix_padding"}, "link": null}, {"localized_name": "force_uniform_tiles", "name": "force_uniform_tiles", "type": "BOOLEAN", "widget": {"name": "force_uniform_tiles"}, "link": null}, {"localized_name": "tiled_decode", "name": "tiled_decode", "type": "BOOLEAN", "widget": {"name": "tiled_decode"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [191]}], "properties": {"cnr_id": "comfyui_ultimatesdupscale", "ver": "1.1.3", "Node name for S&R": "UltimateSDUpscale"}, "widgets_values": [2.0000000000000004, 263153014253114, "randomize", 30, 5, "dpmpp_2m", "sgm_uniform", 0.3, "Linear", 1024, 1024, 8, 32, "None", 1, 64, 8, 16, true, false]}, {"id": 200, "type": "PrimitiveFloat", "pos": [-649.4349365234375, -309.8113708496094], "size": [270, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "FLOAT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "浮点", "name": "FLOAT", "type": "FLOAT", "links": [219, 220]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PrimitiveFloat"}, "widgets_values": [0.5]}, {"id": 178, "type": "UpscaleModelLoader", "pos": [-1040.318603515625, -314.8203125], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "模型名称", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"label": "放大模型", "localized_name": "放大模型", "name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [190, 212]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UpscaleModelLoader"}, "widgets_values": ["4x-AnimeSharp.pth"]}], "links": [[185, 59, 0, 176, 0, "CLIP"], [186, 59, 0, 177, 0, "CLIP"], [187, 176, 0, 175, 2, "CONDITIONING"], [188, 177, 0, 175, 3, "CONDITIONING"], [189, 58, 0, 175, 4, "VAE"], [190, 178, 0, 175, 5, "UPSCALE_MODEL"], [191, 175, 0, 69, 0, "IMAGE"], [199, 60, 0, 175, 1, "MODEL"], [203, 187, 0, 192, 0, "MODEL"], [204, 187, 1, 192, 1, "CLIP"], [205, 192, 0, 194, 1, "MODEL"], [207, 192, 2, 194, 2, "CONDITIONING"], [209, 192, 1, 195, 0, "CLIP"], [210, 195, 0, 194, 3, "CONDITIONING"], [211, 196, 0, 194, 4, "VAE"], [212, 178, 0, 194, 5, "UPSCALE_MODEL"], [213, 194, 0, 197, 0, "IMAGE"], [216, 71, 0, 199, 0, "IMAGE"], [217, 199, 0, 194, 0, "IMAGE"], [218, 199, 0, 175, 0, "IMAGE"], [219, 200, 0, 175, 12, "FLOAT"], [220, 200, 0, 194, 12, "FLOAT"]], "groups": [{"id": 1, "title": "Group", "bounding": [-358.1264953613281, 22.399999618530273, 1892.70556640625, 966.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Group", "bounding": [-218.44989013671875, -783.038330078125, 1474.761474609375, 730.0420532226562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Group", "bounding": [-1050.318603515625, -388.4203186035156, 680.8836669921875, 146.60894775390625], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.724729500000001, "offset": [976.6904599638885, 247.8967730385992]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}