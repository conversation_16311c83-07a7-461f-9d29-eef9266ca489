{"id": "0169208f-be01-4916-83e3-6a807896f2e5", "revision": 0, "last_node_id": 31, "last_link_id": 42, "nodes": [{"id": 4, "type": "WanVideoEnhanceAVideo", "pos": [2009.7015380859375, 5391.8466796875], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "feta_args", "localized_name": "feta_args", "name": "feta_args", "type": "FETAARGS", "links": [18]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "ccca41cdb76d55b7d0f8bdd02fed1e86dd379fa2", "Node name for S&R": "WanVideoEnhanceAVideo"}, "widgets_values": [2, 0, 1]}, {"id": 5, "type": "WanVideoTeaCache", "pos": [2009.7015380859375, 5561.84619140625], "size": [315, 178], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "rel_l1_thresh", "name": "rel_l1_thresh", "type": "FLOAT", "widget": {"name": "rel_l1_thresh"}, "link": null}, {"localized_name": "start_step", "name": "start_step", "type": "INT", "widget": {"name": "start_step"}, "link": null}, {"localized_name": "end_step", "name": "end_step", "type": "INT", "widget": {"name": "end_step"}, "link": null}, {"localized_name": "cache_device", "name": "cache_device", "type": "COMBO", "widget": {"name": "cache_device"}, "link": null}, {"localized_name": "use_coefficients", "name": "use_coefficients", "type": "BOOLEAN", "widget": {"name": "use_coefficients"}, "link": null}, {"localized_name": "mode", "name": "mode", "shape": 7, "type": "COMBO", "widget": {"name": "mode"}, "link": null}], "outputs": [{"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "type": "TEACACHEARGS", "links": [19]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d20baf00247fd06553fdc9253e18732244e54172", "Node name for S&R": "WanVideoTeaCache"}, "widgets_values": [0.26000000000000006, 1, -1, "offload_device", "true", "e"]}, {"id": 6, "type": "WanVideoSLG", "pos": [2009.7015380859375, 5781.8466796875], "size": [315, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "blocks", "name": "blocks", "type": "STRING", "widget": {"name": "blocks"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "type": "SLGARGS", "links": [20]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "ccca41cdb76d55b7d0f8bdd02fed1e86dd379fa2", "Node name for S&R": "WanVideoSLG"}, "widgets_values": ["9,10", 0.7000000000000002, 0.9000000000000001]}, {"id": 7, "type": "WanVideoExperimentalArgs", "pos": [1999.7015380859375, 5971.8486328125], "size": [327.5999755859375, 226], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "video_attention_split_steps", "name": "video_attention_split_steps", "type": "STRING", "widget": {"name": "video_attention_split_steps"}, "link": null}, {"localized_name": "cfg_zero_star", "name": "cfg_zero_star", "type": "BOOLEAN", "widget": {"name": "cfg_zero_star"}, "link": null}, {"localized_name": "use_zero_init", "name": "use_zero_init", "type": "BOOLEAN", "widget": {"name": "use_zero_init"}, "link": null}, {"localized_name": "zero_star_steps", "name": "zero_star_steps", "type": "INT", "widget": {"name": "zero_star_steps"}, "link": null}, {"localized_name": "use_fresca", "name": "use_fresca", "type": "BOOLEAN", "widget": {"name": "use_fresca"}, "link": null}, {"localized_name": "fresca_scale_low", "name": "fresca_scale_low", "type": "FLOAT", "widget": {"name": "fresca_scale_low"}, "link": null}, {"localized_name": "fresca_scale_high", "name": "fresca_scale_high", "type": "FLOAT", "widget": {"name": "fresca_scale_high"}, "link": null}, {"localized_name": "fresca_freq_cutoff", "name": "fresca_freq_cutoff", "type": "INT", "widget": {"name": "fresca_freq_cutoff"}, "link": null}], "outputs": [{"label": "exp_args", "localized_name": "exp_args", "name": "exp_args", "type": "EXPERIMENTALARGS", "links": [21]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d5085d4e879b5c9d56e39f57e656255ce72e81fc", "Node name for S&R": "WanVideoExperimentalArgs"}, "widgets_values": ["", true, false, 0, false, 1, 1.25, 20]}, {"id": 14, "type": "WanVideoBlockSwap", "pos": [413.48236083984375, 5935.642578125], "size": [315, 154], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "blocks_to_swap", "name": "blocks_to_swap", "type": "INT", "widget": {"name": "blocks_to_swap"}, "link": null}, {"localized_name": "offload_img_emb", "name": "offload_img_emb", "type": "BOOLEAN", "widget": {"name": "offload_img_emb"}, "link": null}, {"localized_name": "offload_txt_emb", "name": "offload_txt_emb", "type": "BOOLEAN", "widget": {"name": "offload_txt_emb"}, "link": null}, {"localized_name": "use_non_blocking", "name": "use_non_blocking", "shape": 7, "type": "BOOLEAN", "widget": {"name": "use_non_blocking"}, "link": null}, {"localized_name": "vace_blocks_to_swap", "name": "vace_blocks_to_swap", "shape": 7, "type": "INT", "widget": {"name": "vace_blocks_to_swap"}, "link": null}], "outputs": [{"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "links": []}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "f2bc29b931983e279d25452d284b3888c8c81346", "Node name for S&R": "WanVideoBlockSwap"}, "widgets_values": [40, true, true, true, 15]}, {"id": 22, "type": "WanVideoDecode", "pos": [2459.35400390625, 6095.7841796875], "size": [315, 198], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 22}, {"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "link": 23}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}], "outputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "links": [1]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoDecode"}, "widgets_values": [false, 272, 272, 144, 128], "color": "#322", "bgcolor": "#533"}, {"id": 25, "type": "WanVideoVACEEncode", "pos": [1545.7000732421875, 6159.97607421875], "size": [343.59088134765625, 334], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 27}, {"label": "input_frames", "localized_name": "input_frames", "name": "input_frames", "shape": 7, "type": "IMAGE", "link": null}, {"label": "ref_images", "localized_name": "ref_images", "name": "ref_images", "shape": 7, "type": "IMAGE", "link": 28}, {"label": "input_masks", "localized_name": "input_masks", "name": "input_masks", "shape": 7, "type": "MASK", "link": null}, {"label": "prev_vace_embeds", "localized_name": "prev_vace_embeds", "name": "prev_vace_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS", "link": null}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 29}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 30}, {"label": "num_frames", "localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 31}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "vace_start_percent", "name": "vace_start_percent", "type": "FLOAT", "widget": {"name": "vace_start_percent"}, "link": null}, {"localized_name": "vace_end_percent", "name": "vace_end_percent", "type": "FLOAT", "widget": {"name": "vace_end_percent"}, "link": null}, {"localized_name": "tiled_vae", "name": "tiled_vae", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_vae"}, "link": null}], "outputs": [{"label": "vace_embeds", "localized_name": "vace_embeds", "name": "vace_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [17]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoVACEEncode"}, "widgets_values": [480, 832, 29, 1.0000000000000002, 0, 1, false], "color": "#322", "bgcolor": "#533"}, {"id": 28, "type": "WanVideoLoraSelect", "pos": [405.76361083984375, 5593.708984375], "size": [315, 126], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "prev_lora", "localized_name": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA", "link": null}, {"label": "blocks", "localized_name": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS", "link": null}, {"localized_name": "lora", "name": "lora", "type": "COMBO", "widget": {"name": "lora"}, "link": null}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "low_mem_load", "name": "low_mem_load", "shape": 7, "type": "BOOLEAN", "widget": {"name": "low_mem_load"}, "link": null}], "outputs": [{"label": "lora", "localized_name": "lora", "name": "lora", "type": "WANVIDLORA", "links": [33]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "f2bc29b931983e279d25452d284b3888c8c81346", "Node name for S&R": "WanVideoLoraSelect"}, "widgets_values": ["CausVid\\Wan21_CausVid_14B_T2V_lora_rank32.safetensors", 0.8000000000000002, false]}, {"id": 11, "type": "WanVideoVACEModelSelect", "pos": [411.48138427734375, 5770.10595703125], "size": [372.0574645996094, 100.53829193115234], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "vace_model", "name": "vace_model", "type": "COMBO", "widget": {"name": "vace_model"}, "link": null}], "outputs": [{"label": "vace_model", "localized_name": "vace_model", "name": "vace_model", "type": "VACEPATH", "links": [34]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "f2bc29b931983e279d25452d284b3888c8c81346", "Node name for S&R": "WanVideoVACEModelSelect"}, "widgets_values": ["vace\\Wan2_1-VACE_module_14B_fp8_e4m3fn.safetensors"]}, {"id": 2, "type": "VHS_VideoCombine", "pos": [2443.7060546875, 6375.34521484375], "size": [605.903076171875, 684.0326538085938], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "link": 1}, {"label": "audio", "localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "meta_batch", "localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"label": "Filenames", "localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_VACE_startendframe", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_VACE_startendframe_00001.mp4", "workflow": "WanVideoWrapper_VACE_startendframe_00001.png", "fullpath": "H:\\ComfyUI_124_251\\ComfyUI\\temp\\WanVideoWrapper_VACE_startendframe_00001.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "temp", "frame_rate": 16}}}}, {"id": 29, "type": "PreviewImage", "pos": [1527.509765625, 6570.73583984375], "size": [342.69537353515625, 608.6505737304688], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 35}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 24, "type": "WanVideoTextEncode", "pos": [1501.832763671875, 5822.728515625], "size": [420.30511474609375, 261.5306701660156], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "t5", "localized_name": "t5", "name": "t5", "type": "WANTEXTENCODER", "link": 24}, {"label": "model_to_offload", "localized_name": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL", "link": 25}, {"label": "positive_prompt", "localized_name": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": 26}, {"localized_name": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "shape": 7, "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}], "outputs": [{"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [16]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoTextEncode"}, "widgets_values": ["一个男人在发怒", "bad quality, blurry, messy, chaotic", true, [false, true], [false, true]], "color": "#432", "bgcolor": "#653"}, {"id": 23, "type": "CR Text", "pos": [1518.258056640625, 5545.65283203125], "size": [400, 200], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [26]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["", [false, true]]}, {"id": 27, "type": "WanVideoVAELoader", "pos": [1055.44775390625, 5880.49267578125], "size": [315, 82], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "shape": 7, "type": "COMBO", "widget": {"name": "precision"}, "link": null}], "outputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [22, 27]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoVAELoader"}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#322", "bgcolor": "#533"}, {"id": 1, "type": "LoadWanVideoT5TextEncoder", "pos": [1029.9542236328125, 6055.33203125], "size": [377.1661376953125, 130], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "shape": 7, "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "shape": 7, "type": "COMBO", "widget": {"name": "quantization"}, "link": null}], "outputs": [{"label": "wan_t5_model", "localized_name": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [24]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "LoadWanVideoT5TextEncoder"}, "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "disabled"], "color": "#432", "bgcolor": "#653"}, {"id": 21, "type": "WanVideoSampler", "pos": [2003.8924560546875, 6286.1005859375], "size": [312.8956298828125, 804.8956298828125], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 15}, {"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 16}, {"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 17}, {"label": "samples", "localized_name": "samples", "name": "samples", "shape": 7, "type": "LATENT", "link": null}, {"label": "feta_args", "localized_name": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS", "link": 18}, {"label": "context_options", "localized_name": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT", "link": null}, {"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "shape": 7, "type": "TEACACHEARGS", "link": 19}, {"label": "flowedit_args", "localized_name": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS", "link": null}, {"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS", "link": 20}, {"label": "loop_args", "localized_name": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS", "link": null}, {"label": "experimental_args", "localized_name": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS", "link": 21}, {"label": "sigmas", "localized_name": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS", "link": null}, {"label": "unianimate_poses", "localized_name": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE", "link": null}, {"label": "fantasytalking_embeds", "localized_name": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS", "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "riflex_freq_index", "name": "riflex_freq_index", "type": "INT", "widget": {"name": "riflex_freq_index"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}, {"localized_name": "batched_cfg", "name": "batched_cfg", "shape": 7, "type": "BOOLEAN", "widget": {"name": "batched_cfg"}, "link": null}, {"localized_name": "rope_function", "name": "rope_function", "shape": 7, "type": "COMBO", "widget": {"name": "rope_function"}, "link": null}], "outputs": [{"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "links": [23]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "Node name for S&R": "WanVideoSampler"}, "widgets_values": [3, 1.0000000000000002, 8.000000000000002, 128819423923042, "randomize", true, "unipc", 0, 1, false, "comfy"]}, {"id": 13, "type": "INTConstant", "pos": [384.1207580566406, 6205.373046875], "size": [210, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "value", "localized_name": "value", "name": "value", "type": "INT", "links": [7]}], "title": "高", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "Node name for S&R": "INTConstant"}, "widgets_values": [1100], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 12, "type": "INTConstant", "pos": [630.9906005859375, 6188.02685546875], "size": [210, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "value", "localized_name": "value", "name": "value", "type": "INT", "links": [6]}], "title": "宽", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "Node name for S&R": "INTConstant"}, "widgets_values": [1100], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 19, "type": "INTConstant", "pos": [157.07115173339844, 6211.77783203125], "size": [210, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "value", "localized_name": "value", "name": "value", "type": "INT", "links": [31]}], "title": "长", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "Node name for S&R": "INTConstant"}, "widgets_values": [81], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 10, "type": "LoadImage", "pos": [295.7282409667969, 6313.19921875], "size": [638.487060546875, 449.11102294921875], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [42]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_yrcht_00004_.png", "image"]}, {"id": 9, "type": "ImageResizeKJ", "pos": [1085.0482177734375, 6279.6865234375], "size": [315, 266], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 42}, {"label": "get_image_size", "localized_name": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 6}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 7}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "keep_proportion", "name": "keep_proportion", "type": "BOOLEAN", "widget": {"name": "keep_proportion"}, "link": null}, {"localized_name": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}, "link": null}, {"localized_name": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [28, 35]}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "links": [29]}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "links": [30]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "lanc<PERSON>s", true, 16, "disabled"]}, {"id": 26, "type": "WanVideoModelLoader", "pos": [997.88720703125, 5536.15966796875], "size": [477.4410095214844, 254], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "compile_args", "localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS", "link": null}, {"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": null}, {"label": "lora", "localized_name": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 33}, {"label": "vram_management_args", "localized_name": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS", "link": null}, {"label": "vace_model", "localized_name": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH", "link": 34}, {"label": "fantasytalking_model", "localized_name": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [15, 25]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoModelLoader"}, "widgets_values": ["Wan2_1-MoviiGen1_1_fp8_e4m3fn.safetensors", "bf16", "fp8_e4m3fn", "offload_device", "sageattn"], "color": "#223", "bgcolor": "#335"}], "links": [[1, 22, 0, 2, 0, "IMAGE"], [6, 12, 0, 9, 2, "INT"], [7, 13, 0, 9, 3, "INT"], [15, 26, 0, 21, 0, "WANVIDEOMODEL"], [16, 24, 0, 21, 1, "WANVIDEOTEXTEMBEDS"], [17, 25, 0, 21, 2, "WANVIDIMAGE_EMBEDS"], [18, 4, 0, 21, 4, "FETAARGS"], [19, 5, 0, 21, 6, "TEACACHEARGS"], [20, 6, 0, 21, 8, "SLGARGS"], [21, 7, 0, 21, 10, "EXPERIMENTALARGS"], [22, 27, 0, 22, 0, "WANVAE"], [23, 21, 0, 22, 1, "LATENT"], [24, 1, 0, 24, 0, "WANTEXTENCODER"], [25, 26, 0, 24, 1, "WANVIDEOMODEL"], [26, 23, 0, 24, 2, "STRING"], [27, 27, 0, 25, 0, "WANVAE"], [28, 9, 0, 25, 2, "IMAGE"], [29, 9, 1, 25, 5, "INT"], [30, 9, 2, 25, 6, "INT"], [31, 19, 0, 25, 7, "INT"], [33, 28, 0, 26, 2, "WANVIDLORA"], [34, 11, 0, 26, 4, "VACEPATH"], [35, 9, 0, 29, 0, "IMAGE"], [42, 10, 0, 9, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"frontendVersion": "1.18.10", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}