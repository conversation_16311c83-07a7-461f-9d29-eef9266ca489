{"id": "1018d694-65b0-4196-b6ac-5557e0137d3a", "revision": 0, "last_node_id": 563, "last_link_id": 889, "nodes": [{"id": 513, "type": "CLIPVisionEncode", "pos": [-939.46337890625, 270.0082092285156], "size": [380.4000244140625, 78], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "clip_vision", "localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 753}, {"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 866}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "CLIP_VISION_OUTPUT", "localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [752]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 531, "type": "easy makeImageForICLora", "pos": [-1406.4808349609375, 966.511474609375], "size": [270, 226], "flags": {}, "order": 38, "mode": 0, "inputs": [{"localized_name": "图像1", "name": "image_1", "type": "IMAGE", "link": 867}, {"localized_name": "图像2", "name": "image_2", "shape": 7, "type": "IMAGE", "link": 833}, {"localized_name": "遮罩1", "name": "mask_1", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "遮罩2", "name": "mask_2", "shape": 7, "type": "MASK", "link": 834}, {"localized_name": "方向", "name": "direction", "type": "COMBO", "widget": {"name": "direction"}, "link": null}, {"localized_name": "限制像素", "name": "pixels", "type": "INT", "widget": {"name": "pixels"}, "link": null}, {"localized_name": "限制方式", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "links": [802]}, {"localized_name": "遮罩", "name": "mask", "type": "MASK", "links": [803]}, {"localized_name": "上下文遮罩", "name": "context_mask", "type": "MASK", "links": null}, {"localized_name": "宽", "name": "width", "type": "INT", "links": null}, {"localized_name": "高", "name": "height", "type": "INT", "links": null}, {"localized_name": "x", "name": "x", "type": "INT", "links": null}, {"localized_name": "y", "name": "y", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy makeImageForICLora"}, "widgets_values": ["left-right", 1024, "auto"]}, {"id": 171, "type": "StyleModelApply", "pos": [-508.5091552734375, 170.75856018066406], "size": [313.8941650390625, 154.62265014648438], "flags": {}, "order": 49, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 183}, {"label": "style_model", "localized_name": "风格模型", "name": "style_model", "type": "STYLE_MODEL", "link": 751}, {"label": "clip_vision_output", "localized_name": "clip视觉输出", "name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 752}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "强度类型", "name": "strength_type", "type": "COMBO", "widget": {"name": "strength_type"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [784]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 514, "type": "CLIPVisionLoader", "pos": [-892.5441284179688, 168.85015869140625], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"label": "CLIP_VISION", "localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [753]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["siglip2_so400m_patch16_512.safetensors"]}, {"id": 512, "type": "StyleModelLoader", "pos": [-914.6561889648438, 61.34833908081055], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "风格模型名称", "name": "style_model_name", "type": "COMBO", "widget": {"name": "style_model_name"}, "link": null}], "outputs": [{"label": "STYLE_MODEL", "localized_name": "风格模型", "name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [751]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flex1_redux_siglip2_512.safetensors"]}, {"id": 518, "type": "StyleModelLoader", "pos": [-949.533447265625, 470.7303771972656], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "风格模型名称", "name": "style_model_name", "type": "COMBO", "widget": {"name": "style_model_name"}, "link": null}], "outputs": [{"label": "STYLE_MODEL", "localized_name": "风格模型", "name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [765]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 521, "type": "StyleModelApply", "pos": [-573.0545654296875, 466.4986877441406], "size": [313.8941650390625, 154.62265014648438], "flags": {}, "order": 50, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 772}, {"label": "style_model", "localized_name": "风格模型", "name": "style_model", "type": "STYLE_MODEL", "link": 765}, {"label": "clip_vision_output", "localized_name": "clip视觉输出", "name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 766}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "强度类型", "name": "strength_type", "type": "COMBO", "widget": {"name": "strength_type"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [785]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "StyleModelApply"}, "widgets_values": [1.0000000000000002, "multiply"]}, {"id": 519, "type": "CLIPVisionLoader", "pos": [-944.0160522460938, 588.8075561523438], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "clip名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}], "outputs": [{"label": "CLIP_VISION", "localized_name": "CLIP视觉", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [764]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 520, "type": "CLIPVisionEncode", "pos": [-950.5568237304688, 693.8109741210938], "size": [380.4000244140625, 78], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "clip_vision", "localized_name": "clip视觉", "name": "clip_vision", "type": "CLIP_VISION", "link": 764}, {"label": "image", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 869}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "CLIP_VISION_OUTPUT", "localized_name": "CLIP视觉输出", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [766]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 11, "type": "DualCLIPLoader", "pos": [-1406.2684326171875, 25.958925247192383], "size": [320, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [28]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "DualCLIPLoader", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn_scaled.safetensors", "flux", "default"]}, {"id": 10, "type": "VAELoader", "pos": [-1403.762451171875, 214.08531188964844], "size": [310, 60], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [12, 178, 744]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAELoader", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["ae.safetensors"]}, {"id": 548, "type": "Reroute", "pos": [-1023.07958984375, 1508.3265380859375], "size": [75, 26], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 845}], "outputs": [{"name": "", "type": "IMAGE", "links": [846]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 554, "type": "PreviewImage", "pos": [-1399.7314453125, 397.1235656738281], "size": [350.5749206542969, 484.44488525390625], "flags": {}, "order": 36, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 865}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 465, "type": "ImageCompositeMasked", "pos": [-2154.8017578125, 508.608642578125], "size": [315, 146], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "destination", "localized_name": "目标图像", "name": "destination", "type": "IMAGE", "link": 662}, {"label": "source", "localized_name": "来源图像", "name": "source", "type": "IMAGE", "link": 663}, {"label": "mask", "localized_name": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": 664}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}, {"localized_name": "缩放来源图像", "name": "resize_source", "type": "BOOLEAN", "widget": {"name": "resize_source"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [666]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.15", "Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 229, "type": "LoadImage", "pos": [-2999.319580078125, 480.0293273925781], "size": [419.2577209472656, 608.7006225585938], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [670]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": [674]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["084b7a22-a750-4d6f-ab96-c2bc9c0b05ef.png", "image"]}, {"id": 546, "type": "ImageAndMaskPreview", "pos": [-2030.703369140625, 1318.171875], "size": [546.7041625976562, 503.2754211425781], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 842}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 841}, {"localized_name": "mask_opacity", "name": "mask_opacity", "type": "FLOAT", "widget": {"name": "mask_opacity"}, "link": null}, {"localized_name": "mask_color", "name": "mask_color", "type": "STRING", "widget": {"name": "mask_color"}, "link": null}, {"localized_name": "pass_through", "name": "pass_through", "type": "BOOLEAN", "widget": {"name": "pass_through"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "composite", "name": "composite", "type": "IMAGE", "links": []}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImageAndMaskPreview"}, "widgets_values": [1, "255, 255, 255", false]}, {"id": 545, "type": "GrowMaskWithBlur", "pos": [-2808.317138671875, 1652.6768798828125], "size": [315, 246], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 840}, {"localized_name": "expand", "name": "expand", "type": "INT", "widget": {"name": "expand"}, "link": null}, {"localized_name": "incremental_expandrate", "name": "incremental_expandrate", "type": "FLOAT", "widget": {"name": "incremental_expandrate"}, "link": null}, {"localized_name": "tapered_corners", "name": "tapered_corners", "type": "BOOLEAN", "widget": {"name": "tapered_corners"}, "link": null}, {"localized_name": "flip_input", "name": "flip_input", "type": "BOOLEAN", "widget": {"name": "flip_input"}, "link": null}, {"localized_name": "blur_radius", "name": "blur_radius", "type": "FLOAT", "widget": {"name": "blur_radius"}, "link": null}, {"localized_name": "lerp_alpha", "name": "lerp_alpha", "type": "FLOAT", "widget": {"name": "lerp_alpha"}, "link": null}, {"localized_name": "decay_factor", "name": "decay_factor", "type": "FLOAT", "widget": {"name": "decay_factor"}, "link": null}, {"localized_name": "fill_holes", "name": "fill_holes", "shape": 7, "type": "BOOLEAN", "widget": {"name": "fill_holes"}, "link": null}], "outputs": [{"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 0, "links": [841, 844]}, {"label": "反转遮罩", "localized_name": "mask_inverted", "name": "mask_inverted", "type": "MASK", "links": []}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "GrowMaskWithBlur"}, "widgets_values": [200, 0, true, false, 50, 1, 1, false], "color": "#232", "bgcolor": "#353"}, {"id": 541, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [-2791.501953125, 1244.6585693359375], "size": [315, 330], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 822}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 825}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": null}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [827, 845]}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 1, "links": [828]}, {"label": "原始大小", "localized_name": "original_size", "name": "original_size", "type": "BOX", "links": []}, {"localized_name": "width", "name": "width", "type": "INT", "links": []}, {"localized_name": "height", "name": "height", "type": "INT", "links": []}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "shortest", 1024, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 333, "type": "LoadImage", "pos": [-3243.175048828125, 1262.157958984375], "size": [424.71136474609375, 431.0491638183594], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [822, 842]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": [825, 839]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-4645340.700000001.png [input]", "image"]}, {"id": 544, "type": "Mask Fill Holes", "pos": [-3094.801025390625, 1784.9388427734375], "size": [216.07122802734375, 39.35673522949219], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "遮罩", "localized_name": "masks", "name": "masks", "type": "MASK", "link": 839}], "outputs": [{"label": "遮罩", "localized_name": "MASKS", "name": "MASKS", "type": "MASK", "slot_index": 0, "links": [840]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Mask Fill Holes"}, "widgets_values": []}, {"id": 532, "type": "Reroute", "pos": [-909.8375244140625, 845.2691040039062], "size": [75, 26], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 802}], "outputs": [{"name": "", "type": "IMAGE", "links": [794, 796, 797]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 533, "type": "Reroute", "pos": [-581.7684326171875, 849.0587768554688], "size": [75, 26], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 803}], "outputs": [{"name": "", "type": "MASK", "links": [799, 800, 874]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 470, "type": "PreviewImage", "pos": [-1098.1182861328125, 930.17431640625], "size": [472.6982727050781, 301.3010559082031], "flags": {}, "order": 45, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 797}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 556, "type": "MaskPreview+", "pos": [-608.4578857421875, 931.9926147460938], "size": [492.7861633300781, 279.8094482421875], "flags": {}, "order": 47, "mode": 0, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 874}], "outputs": [], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 504, "type": "ControlNetLoader", "pos": [267.13623046875, 292.16741943359375], "size": [393.89288330078125, 65.90673065185547], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"label": "CONTROL_NET", "localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [737]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.31", "Node name for S&R": "ControlNetLoader", "widget_ue_connectable": {}}, "widgets_values": ["flux\\flux_controlnet_union.safetensors"]}, {"id": 503, "type": "SetUnionControlNetType", "pos": [287.0527038574219, 408.3436279296875], "size": [320, 60], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "control_net", "localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 737}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}], "outputs": [{"label": "CONTROL_NET", "localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [736]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.31", "Node name for S&R": "SetUnionControlNetType", "widget_ue_connectable": {}}, "widgets_values": ["depth"]}, {"id": 511, "type": "ConditioningZeroOut", "pos": [289.35076904296875, 523.1832885742188], "size": [317.4000244140625, 26], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 749}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [750]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 220, "type": "InpaintModelConditioning", "pos": [307.4998474121094, 604.8908081054688], "size": [315, 138], "flags": {}, "order": 46, "mode": 0, "inputs": [{"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 181}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 750}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 178}, {"label": "pixels", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 794}, {"label": "mask", "localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 800}, {"localized_name": "噪波遮罩", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}], "outputs": [{"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [183, 772]}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [184]}, {"label": "latent", "localized_name": "Latent", "name": "latent", "type": "LATENT", "slot_index": 2, "links": [185]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 502, "type": "ACN_AdvancedControlNetApplySingle_v2", "pos": [355.3926086425781, 875.3838500976562], "size": [364.9880065917969, 362.2419738769531], "flags": {}, "order": 52, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 786}, {"label": "control_net", "localized_name": "control_net", "name": "control_net", "type": "CONTROL_NET", "link": 736}, {"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 740}, {"label": "mask_optional", "localized_name": "mask_optional", "name": "mask_optional", "shape": 7, "type": "MASK", "link": 799}, {"label": "timestep_kf", "localized_name": "timestep_kf", "name": "timestep_kf", "shape": 7, "type": "TIMESTEP_KEYFRAME"}, {"label": "latent_kf_override", "localized_name": "latent_kf_override", "name": "latent_kf_override", "shape": 7, "type": "LATENT_KEYFRAME"}, {"label": "weights_override", "localized_name": "weights_override", "name": "weights_override", "shape": 7, "type": "CONTROL_NET_WEIGHTS"}, {"label": "vae_optional", "localized_name": "vae_optional", "name": "vae_optional", "shape": 7, "type": "VAE", "link": 744}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [787]}, {"label": "model_opt", "localized_name": "model_opt", "name": "model_opt", "type": "MODEL"}], "properties": {"cnr_id": "comfyui-advanced-controlnet", "ver": "da254b700db562a22e03358b933c85a9a3392540", "Node name for S&R": "ACN_AdvancedControlNetApplySingle_v2", "widget_ue_connectable": {}}, "widgets_values": [0.7000000000000002, 0, 0.7000000000000002]}, {"id": 507, "type": "PreviewImage", "pos": [-8.947202682495117, 1001.3242797851562], "size": [341.0999450683594, 258], "flags": {}, "order": 48, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 743}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 505, "type": "AIO_Preprocessor", "pos": [-0.3597356379032135, 869.8676147460938], "size": [315, 82], "flags": {}, "order": 44, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 796}, {"localized_name": "preprocessor", "name": "preprocessor", "shape": 7, "type": "COMBO", "widget": {"name": "preprocessor"}, "link": null}, {"localized_name": "resolution", "name": "resolution", "shape": 7, "type": "INT", "widget": {"name": "resolution"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [740, 743]}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "1.0.7", "Node name for S&R": "AIO_Preprocessor"}, "widgets_values": ["DepthAnythingPreprocessor", 960]}, {"id": 457, "type": "LoraLoaderModelOnly", "pos": [-88.892822265625, 505.27783203125], "size": [329.7673645019531, 82], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 652}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [653]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["comfyui_subject_lora16.safetensors", 1]}, {"id": 439, "type": "UNETLoader", "pos": [-86.84393310546875, 370.7333068847656], "size": [315, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [652]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-fill-dev.safetensors", "fp8_e4m3fn_fast"]}, {"id": 102, "type": "K<PERSON><PERSON><PERSON>", "pos": [809.3798217773438, 620.5902099609375], "size": [315.9761962890625, 474], "flags": {}, "order": 55, "mode": 0, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 653}, {"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 187}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 184}, {"label": "latent_image", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 185}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [11]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [807930127588807, "randomize", 25, 1, "euler", "beta", 1]}, {"id": 223, "type": "FluxGuidance", "pos": [810.8990478515625, 488.41717529296875], "size": [317.4000244140625, 58], "flags": {}, "order": 54, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 789}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [187]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "FluxGuidance"}, "widgets_values": [50]}, {"id": 530, "type": "easy ifElse", "pos": [807.1528930664062, 351.5199890136719], "size": [315, 78], "flags": {}, "order": 53, "mode": 0, "inputs": [{"label": "on_true", "localized_name": "真流程", "name": "on_true", "type": "*", "link": 788}, {"label": "on_false", "localized_name": "假流程", "name": "on_false", "type": "*", "link": 787}, {"localized_name": "是否为真", "name": "boolean", "type": "BOOLEAN", "widget": {"name": "boolean"}, "link": null}], "outputs": [{"label": "*", "localized_name": "*", "name": "*", "type": "*", "links": [789]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 106, "type": "VAEDecode", "pos": [1156.9266357421875, 393.3568420410156], "size": [210, 50], "flags": {}, "order": 56, "mode": 0, "inputs": [{"label": "samples", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 11}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 12}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [283, 816]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "VAEDecode", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": []}, {"id": 252, "type": "PreviewImage", "pos": [1162.285400390625, 490.2696533203125], "size": [571.4942626953125, 594.7518310546875], "flags": {}, "order": 57, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 283}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 537, "type": "easy imageInsetCrop", "pos": [-977.1788940429688, 1606.3455810546875], "size": [315, 154], "flags": {}, "order": 58, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 816}, {"localized_name": "测量", "name": "measurement", "type": "COMBO", "widget": {"name": "measurement"}, "link": null}, {"label": "左", "localized_name": "左", "name": "left", "type": "INT", "widget": {"name": "left"}, "link": 819}, {"localized_name": "右", "name": "right", "type": "INT", "widget": {"name": "right"}, "link": null}, {"localized_name": "上", "name": "top", "type": "INT", "widget": {"name": "top"}, "link": null}, {"localized_name": "下", "name": "bottom", "type": "INT", "widget": {"name": "bottom"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [821, 836]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy imageInsetCrop"}, "widgets_values": ["Pixels", 0, 0, 0, 0]}, {"id": 538, "type": "easy imageSize", "pos": [-957.9330444335938, 1457.7669677734375], "size": [266.7224426269531, 108], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 818}], "outputs": [{"label": "宽度", "localized_name": "宽度", "name": "width_int", "type": "INT", "slot_index": 0, "links": [819]}, {"label": "高度", "localized_name": "高度", "name": "height_int", "type": "INT", "links": []}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy imageSize"}, "widgets_values": ["Width: 862 , Height: 1024"]}, {"id": 539, "type": "ImageScale", "pos": [-1315.3743896484375, 1445.1004638671875], "size": [315, 130], "flags": {}, "order": 39, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 868}, {"localized_name": "缩放算法", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [818]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 0, 1024, "disabled"]}, {"id": 543, "type": "InpaintStitchImproved", "pos": [-1264.8851318359375, 1650.8414306640625], "size": [215.52206420898438, 46], "flags": {}, "order": 60, "mode": 0, "inputs": [{"localized_name": "stitcher", "name": "stitcher", "type": "STITCHER", "link": 835}, {"localized_name": "inpainted_image", "name": "inpainted_image", "type": "IMAGE", "link": 836}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [838, 843]}], "properties": {"cnr_id": "comfyui-inpaint-cropand<PERSON>itch", "ver": "2.1.7", "Node name for S&R": "InpaintStitchImproved"}, "widgets_values": []}, {"id": 547, "type": "ImageCompositeMasked", "pos": [-645.1486206054688, 1489.3934326171875], "size": [245.9457550048828, 205.19923400878906], "flags": {}, "order": 62, "mode": 0, "inputs": [{"label": "目标图像", "localized_name": "目标图像", "name": "destination", "type": "IMAGE", "link": 846}, {"label": "源图像", "localized_name": "来源图像", "name": "source", "type": "IMAGE", "link": 843}, {"label": "遮罩", "localized_name": "遮罩", "name": "mask", "shape": 7, "type": "MASK", "link": 844}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}, {"localized_name": "缩放来源图像", "name": "resize_source", "type": "BOOLEAN", "widget": {"name": "resize_source"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [847]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 540, "type": "PreviewImage", "pos": [-346.9541015625, 1390.420166015625], "size": [782.1515502929688, 405.7925109863281], "flags": {}, "order": 59, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 821}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 461, "type": "SaveImage", "pos": [475.7027282714844, 1341.868408203125], "size": [608.6260375976562, 418.7809753417969], "flags": {}, "order": 61, "mode": 0, "inputs": [{"label": "images", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 838}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 549, "type": "PreviewImage", "pos": [1105.4697265625, 1347.0738525390625], "size": [677.1642456054688, 414.7059631347656], "flags": {}, "order": 63, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 847}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 464, "type": "Image Blank", "pos": [-2144.09326171875, 729.5308837890625], "size": [315, 154], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "width", "localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 660}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 661}, {"localized_name": "red", "name": "red", "type": "INT", "widget": {"name": "red"}, "link": null}, {"localized_name": "green", "name": "green", "type": "INT", "widget": {"name": "green"}, "link": null}, {"localized_name": "blue", "name": "blue", "type": "INT", "widget": {"name": "blue"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [662, 880]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "3ed45af34a14551dc28cb3127235cc7197d4633f", "Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 255, 255, 255]}, {"id": 467, "type": "PreviewImage", "pos": [-1786.4832763671875, 795.8641967773438], "size": [210, 246], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 666}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 529, "type": "easy ifElse", "pos": [-107.60494232177734, 204.73980712890625], "size": [315, 78], "flags": {}, "order": 51, "mode": 0, "inputs": [{"label": "on_true", "localized_name": "真流程", "name": "on_true", "type": "*", "link": 784}, {"label": "on_false", "localized_name": "假流程", "name": "on_false", "type": "*", "link": 785}, {"localized_name": "是否为真", "name": "boolean", "type": "BOOLEAN", "widget": {"name": "boolean"}, "link": null}], "outputs": [{"label": "*", "localized_name": "*", "name": "*", "type": "*", "links": [786, 788]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy ifElse"}, "widgets_values": [true]}, {"id": 103, "type": "CLIPTextEncode", "pos": [-84.30577087402344, 638.6163330078125], "size": [363.9454345703125, 112.47201538085938], "flags": {"collapsed": false}, "order": 12, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 28}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [181, 749]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A girl holding a teacup"]}, {"id": 555, "type": "ImpactSwitch", "pos": [-1802.5584716796875, 531.3893432617188], "size": [270, 142], "flags": {}, "order": 34, "mode": 0, "inputs": [{"localized_name": "input1", "name": "input1", "shape": 7, "type": "*", "link": 879}, {"localized_name": "select", "name": "select", "type": "INT", "widget": {"name": "select"}, "link": null}, {"localized_name": "sel_mode", "name": "sel_mode", "type": "BOOLEAN", "widget": {"name": "sel_mode"}, "link": null}, {"name": "input2", "type": "IMAGE", "link": 880}, {"name": "input3", "type": "IMAGE", "link": null}, {"name": "input4", "type": "IMAGE", "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "selected_value", "name": "selected_value", "type": "*", "links": [865, 866, 867, 868, 869]}, {"localized_name": "selected_label", "name": "selected_label", "type": "STRING", "links": null}, {"localized_name": "selected_index", "name": "selected_index", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "ImpactSwitch"}, "widgets_values": [1, false]}, {"id": 558, "type": "PreviewImage", "pos": [-2011.913818359375, 1228.72412109375], "size": [292.075927734375, 258.48394775390625], "flags": {}, "order": 28, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 882}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 542, "type": "InpaintCropImproved", "pos": [-2451.251953125, 1226.123779296875], "size": [348.095703125, 626], "flags": {"collapsed": false}, "order": 22, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 827}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 828}, {"localized_name": "optional_context_mask", "name": "optional_context_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "downscale_algorithm", "name": "downscale_algorithm", "type": "COMBO", "widget": {"name": "downscale_algorithm"}, "link": null}, {"localized_name": "upscale_algorithm", "name": "upscale_algorithm", "type": "COMBO", "widget": {"name": "upscale_algorithm"}, "link": null}, {"localized_name": "preresize", "name": "preresize", "type": "BOOLEAN", "widget": {"name": "preresize"}, "link": null}, {"localized_name": "preresize_mode", "name": "preresize_mode", "type": "COMBO", "widget": {"name": "preresize_mode"}, "link": null}, {"localized_name": "preresize_min_width", "name": "preresize_min_width", "type": "INT", "widget": {"name": "preresize_min_width"}, "link": null}, {"localized_name": "preresize_min_height", "name": "preresize_min_height", "type": "INT", "widget": {"name": "preresize_min_height"}, "link": null}, {"localized_name": "preresize_max_width", "name": "preresize_max_width", "type": "INT", "widget": {"name": "preresize_max_width"}, "link": null}, {"localized_name": "preresize_max_height", "name": "preresize_max_height", "type": "INT", "widget": {"name": "preresize_max_height"}, "link": null}, {"localized_name": "mask_fill_holes", "name": "mask_fill_holes", "type": "BOOLEAN", "widget": {"name": "mask_fill_holes"}, "link": null}, {"localized_name": "mask_expand_pixels", "name": "mask_expand_pixels", "type": "INT", "widget": {"name": "mask_expand_pixels"}, "link": null}, {"localized_name": "mask_invert", "name": "mask_invert", "type": "BOOLEAN", "widget": {"name": "mask_invert"}, "link": null}, {"localized_name": "mask_blend_pixels", "name": "mask_blend_pixels", "type": "INT", "widget": {"name": "mask_blend_pixels"}, "link": null}, {"localized_name": "mask_hipass_filter", "name": "mask_hipass_filter", "type": "FLOAT", "widget": {"name": "mask_hipass_filter"}, "link": null}, {"localized_name": "extend_for_outpainting", "name": "extend_for_outpainting", "type": "BOOLEAN", "widget": {"name": "extend_for_outpainting"}, "link": null}, {"localized_name": "extend_up_factor", "name": "extend_up_factor", "type": "FLOAT", "widget": {"name": "extend_up_factor"}, "link": null}, {"localized_name": "extend_down_factor", "name": "extend_down_factor", "type": "FLOAT", "widget": {"name": "extend_down_factor"}, "link": null}, {"localized_name": "extend_left_factor", "name": "extend_left_factor", "type": "FLOAT", "widget": {"name": "extend_left_factor"}, "link": null}, {"localized_name": "extend_right_factor", "name": "extend_right_factor", "type": "FLOAT", "widget": {"name": "extend_right_factor"}, "link": null}, {"localized_name": "context_from_mask_extend_factor", "name": "context_from_mask_extend_factor", "type": "FLOAT", "widget": {"name": "context_from_mask_extend_factor"}, "link": null}, {"localized_name": "output_resize_to_target_size", "name": "output_resize_to_target_size", "type": "BOOLEAN", "widget": {"name": "output_resize_to_target_size"}, "link": null}, {"localized_name": "output_target_width", "name": "output_target_width", "type": "INT", "widget": {"name": "output_target_width"}, "link": null}, {"localized_name": "output_target_height", "name": "output_target_height", "type": "INT", "widget": {"name": "output_target_height"}, "link": null}, {"localized_name": "output_padding", "name": "output_padding", "type": "COMBO", "widget": {"name": "output_padding"}, "link": null}], "outputs": [{"localized_name": "stitcher", "name": "stitcher", "type": "STITCHER", "links": [835]}, {"localized_name": "cropped_image", "name": "cropped_image", "type": "IMAGE", "links": [833, 882]}, {"localized_name": "cropped_mask", "name": "cropped_mask", "type": "MASK", "links": [834]}], "properties": {"cnr_id": "comfyui-inpaint-cropand<PERSON>itch", "ver": "2.1.7", "Node name for S&R": "InpaintCropImproved"}, "widgets_values": ["lanc<PERSON>s", "bicubic", false, "ensure minimum resolution", 1024, 1024, 16384, 16384, true, 0, false, 64, 0.1, false, 1, 1, 1, 1, 1.2, false, 1024, 1024, "128"]}, {"id": 516, "type": "LoadImage", "pos": [-2900.07861328125, -43.59148025512695], "size": [422.3544921875, 438.38287353515625], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [881]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": [876]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.28", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-4356791.300000001.png [input]", "image"]}, {"id": 552, "type": "PreviewImage", "pos": [-1863.0152587890625, 45.43642807006836], "size": [263.83087158203125, 246], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 877}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 559, "type": "LayerMask: SegmentAnythingUltra", "pos": [-2401.568115234375, -337.4686279296875], "size": [369.8648376464844, 270], "flags": {}, "order": 25, "mode": 2, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 884}, {"localized_name": "SAM模型", "name": "sam_model", "type": "COMBO", "widget": {"name": "sam_model"}, "link": null}, {"localized_name": "Grounding Dino模型", "name": "grounding_dino_model", "type": "COMBO", "widget": {"name": "grounding_dino_model"}, "link": null}, {"localized_name": "阈值", "name": "threshold", "type": "FLOAT", "widget": {"name": "threshold"}, "link": null}, {"localized_name": "细节范围", "name": "detail_range", "type": "INT", "widget": {"name": "detail_range"}, "link": null}, {"localized_name": "黑点", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "白点", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "处理细节", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "提示", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerMask: SegmentAnythingUltra"}, "widgets_values": ["sam_vit_h (2.56GB)", "GroundingDINO_SwinT_OGC (694MB)", 0.3, 16, 0.15, 0.99, true, "subject", false], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 557, "type": "LayerUtility: CropByMask V2", "pos": [-2340.84765625, 26.152488708496094], "size": [314.82220458984375, 262], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 881}, {"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 876}, {"label": "crop_box", "localized_name": "crop_box", "name": "crop_box", "shape": 7, "type": "BOX", "link": null}, {"localized_name": "invert_mask", "name": "invert_mask", "type": "BOOLEAN", "widget": {"name": "invert_mask"}, "link": null}, {"localized_name": "detect", "name": "detect", "type": "COMBO", "widget": {"name": "detect"}, "link": null}, {"localized_name": "top_reserve", "name": "top_reserve", "type": "INT", "widget": {"name": "top_reserve"}, "link": null}, {"localized_name": "bottom_reserve", "name": "bottom_reserve", "type": "INT", "widget": {"name": "bottom_reserve"}, "link": null}, {"localized_name": "left_reserve", "name": "left_reserve", "type": "INT", "widget": {"name": "left_reserve"}, "link": null}, {"localized_name": "right_reserve", "name": "right_reserve", "type": "INT", "widget": {"name": "right_reserve"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}], "outputs": [{"label": "croped_image", "localized_name": "croped_image", "name": "croped_image", "type": "IMAGE", "slot_index": 0, "links": [877, 879, 884, 885]}, {"label": "croped_mask", "localized_name": "croped_mask", "name": "croped_mask", "type": "MASK", "slot_index": 1, "links": []}, {"label": "crop_box", "localized_name": "crop_box", "name": "crop_box", "type": "BOX", "slot_index": 2, "links": []}, {"label": "box_preview", "localized_name": "box_preview", "name": "box_preview", "type": "IMAGE"}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "a085d8ca3cf3e3e8624ac0bb37a474d9d329e60e", "Node name for S&R": "LayerUtility: CropByMask V2"}, "widgets_values": [false, "mask_area", 0, 0, 0, 0, "8"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 560, "type": "LayerUtility: ColorImage V2", "pos": [-1988.251220703125, -347.92437744140625], "size": [315, 130], "flags": {}, "order": 30, "mode": 2, "inputs": [{"localized_name": "size_as", "name": "size_as", "shape": 7, "type": "*", "link": null}, {"localized_name": "size", "name": "size", "type": "COMBO", "widget": {"name": "size"}, "link": null}, {"localized_name": "custom_width", "name": "custom_width", "type": "INT", "widget": {"name": "custom_width"}, "link": 886}, {"localized_name": "custom_height", "name": "custom_height", "type": "INT", "widget": {"name": "custom_height"}, "link": 887}, {"localized_name": "color", "name": "color", "type": "STRING", "widget": {"name": "color"}, "link": 888}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [889]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ColorImage V2"}, "widgets_values": ["custom", 512, 512, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 562, "type": "LayerUtility: ColorPicker", "pos": [-1843.8079833984375, -164.9952392578125], "size": [210, 94], "flags": {}, "order": 11, "mode": 2, "inputs": [{"localized_name": "color", "name": "color", "type": "COLOR", "widget": {"name": "color"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}], "outputs": [{"localized_name": "value", "name": "value", "type": "STRING", "links": [888]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ColorPicker"}, "widgets_values": ["#FFFFFF", "HEX"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 561, "type": "GetImageSize+", "pos": [-2017.881591796875, -150.1995391845703], "size": [159.50155639648438, 66], "flags": {}, "order": 26, "mode": 2, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 885}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": [886, 887]}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}, {"localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}}, {"id": 563, "type": "PreviewImage", "pos": [-1622.8121337890625, -329.0784912109375], "size": [337.3399658203125, 255.40777587890625], "flags": {}, "order": 32, "mode": 2, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 889}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}}, {"id": 463, "type": "GetImageSize+", "pos": [-2130.615234375, 932.7966918945312], "size": [214.20001220703125, 66], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 659}], "outputs": [{"label": "width", "localized_name": "width", "name": "width", "type": "INT", "links": [660]}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "links": [661]}, {"label": "count", "localized_name": "count", "name": "count", "type": "INT"}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 462, "type": "LayerMask: Mask<PERSON>row", "pos": [-2504.906982421875, 903.3331909179688], "size": [315, 106], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 674}, {"localized_name": "invert_mask", "name": "invert_mask", "type": "BOOLEAN", "widget": {"name": "invert_mask"}, "link": null}, {"localized_name": "grow", "name": "grow", "type": "INT", "widget": {"name": "grow"}, "link": null}, {"localized_name": "blur", "name": "blur", "type": "INT", "widget": {"name": "blur"}, "link": null}], "outputs": [{"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 0, "links": [665]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "a085d8ca3cf3e3e8624ac0bb37a474d9d329e60e", "Node name for S&R": "LayerMask: Mask<PERSON>row"}, "widgets_values": [false, 35, 10], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 466, "type": "LayerUtility: CropByMask V2", "pos": [-2521.411376953125, 581.75439453125], "size": [314.82220458984375, 262], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 670}, {"label": "mask", "localized_name": "mask", "name": "mask", "type": "MASK", "link": 665}, {"label": "crop_box", "localized_name": "crop_box", "name": "crop_box", "shape": 7, "type": "BOX"}, {"localized_name": "invert_mask", "name": "invert_mask", "type": "BOOLEAN", "widget": {"name": "invert_mask"}, "link": null}, {"localized_name": "detect", "name": "detect", "type": "COMBO", "widget": {"name": "detect"}, "link": null}, {"localized_name": "top_reserve", "name": "top_reserve", "type": "INT", "widget": {"name": "top_reserve"}, "link": null}, {"localized_name": "bottom_reserve", "name": "bottom_reserve", "type": "INT", "widget": {"name": "bottom_reserve"}, "link": null}, {"localized_name": "left_reserve", "name": "left_reserve", "type": "INT", "widget": {"name": "left_reserve"}, "link": null}, {"localized_name": "right_reserve", "name": "right_reserve", "type": "INT", "widget": {"name": "right_reserve"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}], "outputs": [{"label": "croped_image", "localized_name": "croped_image", "name": "croped_image", "type": "IMAGE", "slot_index": 0, "links": [659, 663]}, {"label": "croped_mask", "localized_name": "croped_mask", "name": "croped_mask", "type": "MASK", "slot_index": 1, "links": [664]}, {"label": "crop_box", "localized_name": "crop_box", "name": "crop_box", "type": "BOX", "slot_index": 2, "links": []}, {"label": "box_preview", "localized_name": "box_preview", "name": "box_preview", "type": "IMAGE"}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "a085d8ca3cf3e3e8624ac0bb37a474d9d329e60e", "Node name for S&R": "LayerUtility: CropByMask V2"}, "widgets_values": [false, "mask_area", 100, 64, 64, 64, "None"], "color": "rgba(38, 73, 116, 0.7)"}], "links": [[11, 102, 0, 106, 0, "LATENT"], [12, 10, 0, 106, 1, "VAE"], [28, 11, 0, 103, 0, "CLIP"], [178, 10, 0, 220, 2, "VAE"], [181, 103, 0, 220, 0, "CONDITIONING"], [183, 220, 0, 171, 0, "CONDITIONING"], [184, 220, 1, 102, 2, "CONDITIONING"], [185, 220, 2, 102, 3, "LATENT"], [187, 223, 0, 102, 1, "CONDITIONING"], [283, 106, 0, 252, 0, "IMAGE"], [652, 439, 0, 457, 0, "MODEL"], [653, 457, 0, 102, 0, "MODEL"], [659, 466, 0, 463, 0, "IMAGE"], [660, 463, 0, 464, 0, "INT"], [661, 463, 1, 464, 1, "INT"], [662, 464, 0, 465, 0, "IMAGE"], [663, 466, 0, 465, 1, "IMAGE"], [664, 466, 1, 465, 2, "MASK"], [665, 462, 0, 466, 1, "MASK"], [666, 465, 0, 467, 0, "IMAGE"], [670, 229, 0, 466, 0, "IMAGE"], [674, 229, 1, 462, 0, "MASK"], [736, 503, 0, 502, 1, "CONTROL_NET"], [737, 504, 0, 503, 0, "CONTROL_NET"], [740, 505, 0, 502, 2, "IMAGE"], [743, 505, 0, 507, 0, "IMAGE"], [744, 10, 0, 502, 7, "VAE"], [749, 103, 0, 511, 0, "CONDITIONING"], [750, 511, 0, 220, 1, "CONDITIONING"], [751, 512, 0, 171, 1, "STYLE_MODEL"], [752, 513, 0, 171, 2, "CLIP_VISION_OUTPUT"], [753, 514, 0, 513, 0, "CLIP_VISION"], [764, 519, 0, 520, 0, "CLIP_VISION"], [765, 518, 0, 521, 1, "STYLE_MODEL"], [766, 520, 0, 521, 2, "CLIP_VISION_OUTPUT"], [772, 220, 0, 521, 0, "CONDITIONING"], [784, 171, 0, 529, 0, "*"], [785, 521, 0, 529, 1, "*"], [786, 529, 0, 502, 0, "CONDITIONING"], [787, 502, 0, 530, 1, "*"], [788, 529, 0, 530, 0, "*"], [789, 530, 0, 223, 0, "CONDITIONING"], [794, 532, 0, 220, 3, "IMAGE"], [796, 532, 0, 505, 0, "IMAGE"], [797, 532, 0, 470, 0, "IMAGE"], [799, 533, 0, 502, 3, "MASK"], [800, 533, 0, 220, 4, "MASK"], [802, 531, 0, 532, 0, "*"], [803, 531, 1, 533, 0, "*"], [816, 106, 0, 537, 0, "IMAGE"], [818, 539, 0, 538, 0, "IMAGE"], [819, 538, 0, 537, 2, "INT"], [821, 537, 0, 540, 0, "IMAGE"], [822, 333, 0, 541, 0, "IMAGE"], [825, 333, 1, 541, 1, "MASK"], [827, 541, 0, 542, 0, "IMAGE"], [828, 541, 1, 542, 1, "MASK"], [833, 542, 1, 531, 1, "IMAGE"], [834, 542, 2, 531, 3, "MASK"], [835, 542, 0, 543, 0, "STITCHER"], [836, 537, 0, 543, 1, "IMAGE"], [838, 543, 0, 461, 0, "IMAGE"], [839, 333, 1, 544, 0, "MASK"], [840, 544, 0, 545, 0, "MASK"], [841, 545, 0, 546, 1, "MASK"], [842, 333, 0, 546, 0, "IMAGE"], [843, 543, 0, 547, 1, "IMAGE"], [844, 545, 0, 547, 2, "MASK"], [845, 541, 0, 548, 0, "*"], [846, 548, 0, 547, 0, "IMAGE"], [847, 547, 0, 549, 0, "IMAGE"], [865, 555, 0, 554, 0, "IMAGE"], [866, 555, 0, 513, 1, "IMAGE"], [867, 555, 0, 531, 0, "IMAGE"], [868, 555, 0, 539, 0, "IMAGE"], [869, 555, 0, 520, 1, "IMAGE"], [874, 533, 0, 556, 0, "MASK"], [876, 516, 1, 557, 1, "MASK"], [877, 557, 0, 552, 0, "IMAGE"], [879, 557, 0, 555, 0, "IMAGE"], [880, 464, 0, 555, 3, "IMAGE"], [881, 516, 0, 557, 0, "IMAGE"], [882, 542, 1, 558, 0, "IMAGE"], [884, 557, 0, 559, 0, "IMAGE"], [885, 557, 0, 561, 0, "IMAGE"], [886, 561, 0, 560, 2, "INT"], [887, 561, 0, 560, 3, "INT"], [888, 562, 0, 560, 4, "STRING"], [889, 560, 0, 563, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Group", "bounding": [-949.46337890625, -12.251632690429688, 764.8485107421875, 370.2598571777344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Group", "bounding": [-960.5568237304688, 392.8987121582031, 749.852294921875, 418.7158203125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Group", "bounding": [-2410.2880859375, -25.01799201965332, 907.816650390625, 329.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Group", "bounding": [-3009.319580078125, 406.4293212890625, 1509.2977294921875, 716.2216796875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Group", "bounding": [-3253.175048828125, 1152.5238037109375, 1779.176513671875, 756.153076171875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Group", "bounding": [-18.947200775146484, 796.2676391601562, 749.327880859375, 473.0566101074219], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Group", "bounding": [-1325.3743896484375, 1316.8201904296875, 1770.57177734375, 489.3924865722656], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "Group", "bounding": [-2411.568115234375, -421.5243835449219, 1128.3355712890625, 364.0557556152344], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.12472010928316991, "offset": [3246.3234069388177, 520.6255549040599]}, "frontendVersion": "1.18.9", "VHS_KeepIntermediate": true, "workspace_info": {"saveLock": false, "id": "PpSQDIgnsCwzbb2Minf_g"}, "ue_links": [], "VHS_MetadataImage": true, "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false}, "version": 0.4}