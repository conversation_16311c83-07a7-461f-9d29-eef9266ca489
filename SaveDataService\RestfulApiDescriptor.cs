using System;
using System.Collections.Generic;
using System.Reflection;
using System.Linq;
using Newtonsoft.Json;

namespace SaveDataService
{
    /// <summary>
    /// RESTful API 描述器
    /// </summary>
    public class RestfulApiDescriptor
    {
        /// <summary>
        /// API 方法信息
        /// </summary>
        public class ApiMethodInfo
        {
            /// <summary>
            /// 方法名称
            /// </summary>
            public string MethodName { get; set; } = string.Empty;

            /// <summary>
            /// 方法描述
            /// </summary>
            public string Description { get; set; } = string.Empty;

            /// <summary>
            /// HTTP 方法类型
            /// </summary>
            public string HttpMethod { get; set; } = "POST";

            /// <summary>
            /// API 路径
            /// </summary>
            public string Path { get; set; } = string.Empty;

            /// <summary>
            /// 参数列表
            /// </summary>
            public List<ApiParameterInfo> Parameters { get; set; } = new List<ApiParameterInfo>();

            /// <summary>
            /// 返回类型信息
            /// </summary>
            public ApiReturnInfo ReturnType { get; set; } = new ApiReturnInfo();

            /// <summary>
            /// 示例请求
            /// </summary>
            public object? ExampleRequest { get; set; }

            /// <summary>
            /// 示例响应
            /// </summary>
            public object? ExampleResponse { get; set; }
        }

        /// <summary>
        /// API 参数信息
        /// </summary>
        public class ApiParameterInfo
        {
            /// <summary>
            /// 参数名称
            /// </summary>
            public string Name { get; set; } = string.Empty;

            /// <summary>
            /// 参数类型
            /// </summary>
            public string Type { get; set; } = string.Empty;

            /// <summary>
            /// 是否必需
            /// </summary>
            public bool Required { get; set; } = true;

            /// <summary>
            /// 参数描述
            /// </summary>
            public string Description { get; set; } = string.Empty;

            /// <summary>
            /// 默认值
            /// </summary>
            public object? DefaultValue { get; set; }

            /// <summary>
            /// 示例值
            /// </summary>
            public object? ExampleValue { get; set; }
        }

        /// <summary>
        /// API 返回类型信息
        /// </summary>
        public class ApiReturnInfo
        {
            /// <summary>
            /// 返回类型
            /// </summary>
            public string Type { get; set; } = string.Empty;

            /// <summary>
            /// 返回描述
            /// </summary>
            public string Description { get; set; } = string.Empty;

            /// <summary>
            /// 返回类型的属性列表
            /// </summary>
            public List<ApiPropertyInfo> Properties { get; set; } = new List<ApiPropertyInfo>();
        }

        /// <summary>
        /// API 属性信息
        /// </summary>
        public class ApiPropertyInfo
        {
            /// <summary>
            /// 属性名称
            /// </summary>
            public string Name { get; set; } = string.Empty;

            /// <summary>
            /// 属性类型
            /// </summary>
            public string Type { get; set; } = string.Empty;

            /// <summary>
            /// 属性描述
            /// </summary>
            public string Description { get; set; } = string.Empty;
        }

        /// <summary>
        /// API 集合信息
        /// </summary>
        public class ApiCollectionInfo
        {
            /// <summary>
            /// API 集合名称
            /// </summary>
            public string Name { get; set; } = string.Empty;

            /// <summary>
            /// API 集合描述
            /// </summary>
            public string Description { get; set; } = string.Empty;

            /// <summary>
            /// 基础 URL
            /// </summary>
            public string BaseUrl { get; set; } = string.Empty;

            /// <summary>
            /// API 版本
            /// </summary>
            public string Version { get; set; } = "1.0";

            /// <summary>
            /// 生成时间
            /// </summary>
            public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

            /// <summary>
            /// API 方法列表
            /// </summary>
            public List<ApiMethodInfo> Methods { get; set; } = new List<ApiMethodInfo>();
        }

        /// <summary>
        /// 获取类型的友好名称
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>友好名称</returns>
        public static string GetFriendlyTypeName(Type type)
        {
            if (type == null) return "unknown";

            // 处理可空类型
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                return GetFriendlyTypeName(type.GetGenericArguments()[0]) + "?";
            }

            // 处理数组类型
            if (type.IsArray)
            {
                return GetFriendlyTypeName(type.GetElementType()!) + "[]";
            }

            // 处理泛型类型
            if (type.IsGenericType)
            {
                var genericTypeName = type.Name.Split('`')[0];
                var genericArgs = type.GetGenericArguments();
                var genericArgsStr = string.Join(", ", genericArgs.Select(GetFriendlyTypeName));
                return $"{genericTypeName}<{genericArgsStr}>";
            }

            // 基本类型映射
            var typeMap = new Dictionary<Type, string>
            {
                { typeof(string), "string" },
                { typeof(int), "int" },
                { typeof(long), "long" },
                { typeof(bool), "bool" },
                { typeof(double), "double" },
                { typeof(float), "float" },
                { typeof(decimal), "decimal" },
                { typeof(DateTime), "DateTime" },
                { typeof(DateTimeOffset), "DateTimeOffset" },
                { typeof(Guid), "Guid" },
                { typeof(byte), "byte" },
                { typeof(short), "short" },
                { typeof(uint), "uint" },
                { typeof(ulong), "ulong" },
                { typeof(ushort), "ushort" },
                { typeof(object), "object" },
                { typeof(void), "void" }
            };

            return typeMap.ContainsKey(type) ? typeMap[type] : type.Name;
        }

        /// <summary>
        /// 生成示例值
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="parameterName">参数名称</param>
        /// <returns>示例值</returns>
        public static object? GenerateExampleValue(Type type, string parameterName = "")
        {
            if (type == null) return null;

            // 处理可空类型
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                return GenerateExampleValue(type.GetGenericArguments()[0], parameterName);
            }

            // 处理数组类型
            if (type.IsArray)
            {
                var elementType = type.GetElementType()!;
                var exampleElement = GenerateExampleValue(elementType, parameterName);
                return new object[] { exampleElement! };
            }

            // 基本类型示例值
            if (type == typeof(string))
            {
                if (parameterName.ToLower().Contains("email"))
                    return "<EMAIL>";
                if (parameterName.ToLower().Contains("mobile") || parameterName.ToLower().Contains("phone"))
                    return "13800138000";
                if (parameterName.ToLower().Contains("password"))
                    return "password123";
                if (parameterName.ToLower().Contains("username") || parameterName.ToLower().Contains("name"))
                    return "testuser";
                if (parameterName.ToLower().Contains("id"))
                    return "12345";
                return "example_string";
            }

            if (type == typeof(int) || type == typeof(uint))
                return 123;
            if (type == typeof(long) || type == typeof(ulong))
                return 123456789L;
            if (type == typeof(bool))
                return true;
            if (type == typeof(double))
                return 123.45;
            if (type == typeof(float))
                return 123.45f;
            if (type == typeof(decimal))
                return 123.45m;
            if (type == typeof(DateTime))
                return DateTime.Now;
            if (type == typeof(DateTimeOffset))
                return DateTimeOffset.Now;
            if (type == typeof(Guid))
                return Guid.NewGuid();
            if (type == typeof(byte))
                return (byte)1;
            if (type == typeof(short) || type == typeof(ushort))
                return (short)123;

            return null;
        }
    }
}
