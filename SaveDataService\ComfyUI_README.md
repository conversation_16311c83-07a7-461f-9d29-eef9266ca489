# ComfyUI管理系统

这是一个完整的ComfyUI管理系统，可以管理多台ComfyUI服务器、工作流配置、任务执行和文件管理。

## 系统架构

### 数据层 (Database Layer)
- **ComfyUIServer** - ComfyUI服务器信息管理
- **ComfyUIWorkflow** - 工作流配置管理  
- **ComfyUITask** - 任务执行信息管理
- **ComfyUITaskLog** - 任务执行日志管理
- **ComfyUITaskFile** - 任务相关文件管理

### 业务逻辑层 (Business Logic Layer)
- **ComfyUIManage** - 主要管理类，继承RESTfulAPIBase，提供所有管理功能
- **ComfyUIData** - 数据处理类，处理workflow解析和任务执行上下文

### 模型层 (Model Layer)
- **ComfyUIModels.cs** - 包含所有相关的数据模型和枚举

## 主要功能

### 1. 服务器管理
- 添加/删除ComfyUI服务器
- 更新服务器状态 (在线/离线/忙碌/维护)
- 监控服务器负载和任务数量
- 服务器心跳检测

### 2. 工作流管理
- 添加/管理workflow JSON配置
- 按类型分类工作流
- 工作流版本控制
- 工作流启用/禁用

### 3. 任务管理
- 创建和分配任务到最佳服务器
- 实时跟踪任务执行进度
- 记录每个节点的执行状态
- 任务队列管理
- 任务优先级控制

### 4. 进度跟踪
- 实时监控任务执行到哪个节点
- 记录节点名称、输入输出数据
- 计算任务完成百分比
- 记录执行时间和性能数据

### 5. 日志系统
- 详细的任务执行日志
- 多级别日志记录 (Debug/Info/Warning/Error)
- 节点级别的日志跟踪
- 错误信息记录

### 6. 文件管理
- 输入文件管理 (图片、prompt等)
- 输出文件管理 (生成的图片、视频等)
- 文件MD5校验
- 文件大小和路径管理

## 核心类说明

### ComfyUIManage
主要管理类，提供以下功能：
- `AddServer()` - 添加服务器
- `RemoveServer()` - 删除服务器
- `GetAllServers()` - 获取服务器列表
- `UpdateServerStatus()` - 更新服务器状态
- `AddWorkflow()` - 添加工作流
- `GetAllWorkflows()` - 获取工作流列表
- `CreateTask()` - 创建任务
- `GetTasks()` - 获取任务列表
- `UpdateTaskStatus()` - 更新任务状态
- `AddTaskLog()` - 添加任务日志
- `AddTaskFile()` - 添加任务文件
- `GetServerStatistics()` - 获取统计信息

### ComfyUIData
数据处理类，提供以下功能：
- `ParseWorkflow()` - 解析workflow JSON
- `CreateTaskContext()` - 创建任务执行上下文
- `UpdateTaskProgress()` - 更新任务进度
- `CompleteTaskStep()` - 完成任务步骤
- `CompleteTask()` - 完成整个任务
- `SaveTaskOutputFile()` - 保存输出文件
- `GetTaskExecutionReport()` - 获取执行报告

## 数据库表结构

### ComfyUIServer (服务器表)
- `id` - 服务器ID
- `serverName` - 服务器名称
- `serverUrl` - 服务器地址
- `port` - 端口
- `status` - 状态 (0:离线, 1:在线, 2:忙碌, 3:维护)
- `maxConcurrentTasks` - 最大并发任务数
- `currentTasks` - 当前任务数
- `supportedWorkflows` - 支持的工作流类型
- `lastHeartbeat` - 最后心跳时间

### ComfyUIWorkflow (工作流表)
- `id` - 工作流ID
- `workflowName` - 工作流名称
- `workflowJson` - 工作流JSON配置
- `workflowType` - 工作流类型
- `description` - 描述
- `version` - 版本
- `creator` - 创建者
- `isEnabled` - 是否启用

### ComfyUITask (任务表)
- `id` - 任务ID
- `serverId` - 服务器ID
- `workflowId` - 工作流ID
- `taskName` - 任务名称
- `status` - 状态 (0:等待, 1:运行中, 2:完成, 3:失败, 4:取消)
- `currentNode` - 当前执行节点
- `currentNodeName` - 当前节点名称
- `progress` - 进度百分比
- `queuePosition` - 队列位置
- `priority` - 优先级
- `startTime` - 开始时间
- `endTime` - 结束时间

### ComfyUITaskLog (任务日志表)
- `id` - 日志ID
- `taskId` - 任务ID
- `logLevel` - 日志级别
- `message` - 日志消息
- `nodeId` - 节点ID
- `nodeName` - 节点名称
- `details` - 详细信息

### ComfyUITaskFile (任务文件表)
- `id` - 文件ID
- `taskId` - 任务ID
- `fileType` - 文件类型 (0:输入图片, 1:输出图片, 2:输出视频, 3:其他)
- `fileName` - 文件名
- `filePath` - 文件路径
- `fileSize` - 文件大小
- `fileMd5` - 文件MD5

## RESTful API

由于ComfyUIManage继承了RESTfulAPIBase，所有方法都会自动生成RESTful API接口。

访问 `http://127.0.0.1:7778/api/comfyuimanage` 可以获取完整的API文档。

## 使用示例

```csharp
// 1. 添加服务器
var serverId = ComfyUIManage.Instance.AddServer(
    "ComfyUI服务器1", 
    "192.168.1.100", 
    8188, 
    3, 
    "主要的ComfyUI服务器"
);

// 2. 添加工作流
var workflowId = ComfyUIManage.Instance.AddWorkflow(
    "文本转图片工作流",
    workflowJson,
    "text2img",
    "基础的文本转图片工作流",
    "管理员"
);

// 3. 创建任务
var taskId = ComfyUIManage.Instance.CreateTask(
    workflowId,
    "生成风景图片",
    "用户1",
    5
);

// 4. 更新任务进度
ComfyUIManage.Instance.UpdateTaskStatus(
    taskId, 
    1, // 运行中
    50, // 50%进度
    "node_3", 
    "KSampler"
);

// 5. 获取任务报告
var report = ComfyUIData.Instance.GetTaskExecutionReport(taskId);
```

## 测试

运行 `ComfyUITest.RunTests()` 可以执行完整的系统测试，包括：
- 服务器管理测试
- 工作流管理测试
- 任务创建和执行测试
- 进度跟踪测试
- 文件管理测试
- 统计信息测试

## 扩展功能建议

1. **实时通信** - 使用WebSocket实现实时进度推送
2. **负载均衡** - 智能任务分配算法
3. **故障恢复** - 任务失败自动重试机制
4. **性能监控** - 服务器性能指标收集
5. **用户权限** - 基于角色的访问控制
6. **任务调度** - 定时任务和批量处理
7. **资源管理** - GPU使用率监控
8. **插件系统** - 支持自定义节点和工作流

这个系统提供了完整的ComfyUI管理解决方案，可以轻松扩展和定制以满足特定需求。
