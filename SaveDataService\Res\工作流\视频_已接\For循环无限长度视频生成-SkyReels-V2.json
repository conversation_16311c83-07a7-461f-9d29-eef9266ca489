{"id": "206247b6-9fec-4ed2-8927-e4f388c674d4", "revision": 0, "last_node_id": 215, "last_link_id": 344, "nodes": [{"id": 110, "type": "SetNode", "pos": [20, -1100], "size": [210, 60], "flags": {"collapsed": true}, "order": 37, "mode": 0, "inputs": [{"label": "WANVIDEOMODEL", "name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "link": 189}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_WanModel", "properties": {"previousName": "WanModel"}, "widgets_values": ["WanModel"], "color": "#223", "bgcolor": "#335"}, {"id": 111, "type": "GetNode", "pos": [2972.869873046875, -460.0000305175781], "size": [210, 60], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "WANVIDEOMODEL", "name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "links": [190]}], "title": "Get_WanModel", "properties": {}, "widgets_values": ["WanModel"], "color": "#223", "bgcolor": "#335"}, {"id": 109, "type": "GetNode", "pos": [3282.869873046875, -470], "size": [210, 58], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "links": [188]}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 126, "type": "GetNode", "pos": [2433.34765625, -111.39134979248047], "size": [210, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [205]}], "title": "Get_Width", "properties": {}, "widgets_values": ["<PERSON><PERSON><PERSON>"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 125, "type": "GetNode", "pos": [2437.7001953125, -55.58784484863281], "size": [210, 58], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [206]}], "title": "Get_Height", "properties": {}, "widgets_values": ["Height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 114, "type": "SetNode", "pos": [1010, -1650], "size": [210, 60], "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [{"label": "TEACACHEARGS", "name": "TEACACHEARGS", "type": "TEACACHEARGS", "link": 195}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_TeaCache", "properties": {"previousName": "TeaCache"}, "widgets_values": ["TeaCache"]}, {"id": 116, "type": "SetNode", "pos": [1040, -1380], "size": [210, 60], "flags": {"collapsed": true}, "order": 31, "mode": 0, "inputs": [{"label": "EXPERIMENTALARGS", "name": "EXPERIMENTALARGS", "type": "EXPERIMENTALARGS", "link": 197}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_ExpArgs", "properties": {"previousName": "ExpArgs"}, "widgets_values": ["ExpArgs"]}, {"id": 87, "type": "WanVideoExperimentalArgs", "pos": [500, -1420], "size": [456, 226], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "video_attention_split_steps", "name": "video_attention_split_steps", "type": "STRING", "widget": {"name": "video_attention_split_steps"}, "link": null}, {"localized_name": "cfg_zero_star", "name": "cfg_zero_star", "type": "BOOLEAN", "widget": {"name": "cfg_zero_star"}, "link": null}, {"localized_name": "use_zero_init", "name": "use_zero_init", "type": "BOOLEAN", "widget": {"name": "use_zero_init"}, "link": null}, {"localized_name": "zero_star_steps", "name": "zero_star_steps", "type": "INT", "widget": {"name": "zero_star_steps"}, "link": null}, {"localized_name": "use_fresca", "name": "use_fresca", "type": "BOOLEAN", "widget": {"name": "use_fresca"}, "link": null}, {"localized_name": "fresca_scale_low", "name": "fresca_scale_low", "type": "FLOAT", "widget": {"name": "fresca_scale_low"}, "link": null}, {"localized_name": "fresca_scale_high", "name": "fresca_scale_high", "type": "FLOAT", "widget": {"name": "fresca_scale_high"}, "link": null}, {"localized_name": "fresca_freq_cutoff", "name": "fresca_freq_cutoff", "type": "INT", "widget": {"name": "fresca_freq_cutoff"}, "link": null}], "outputs": [{"label": "exp_args", "localized_name": "exp_args", "name": "exp_args", "type": "EXPERIMENTALARGS", "links": [197]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e5a326c9811514f2c08c89bccea9a7c731d9a503", "Node name for S&R": "WanVideoExperimentalArgs"}, "widgets_values": ["", true, false, 0, true, 1, 1.25, 20]}, {"id": 143, "type": "SetNode", "pos": [892.3596801757812, -1122.108154296875], "size": [210, 60], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [{"label": "SLGARGS", "name": "SLGARGS", "type": "SLGARGS", "link": 238}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_SLG_args", "properties": {"previousName": "SLG_args"}, "widgets_values": ["SLG_args"]}, {"id": 115, "type": "GetNode", "pos": [2789.89697265625, -291.8010559082031], "size": [210, 60], "flags": {"collapsed": true}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "TEACACHEARGS", "name": "TEACACHEARGS", "type": "TEACACHEARGS", "links": [196]}], "title": "Get_TeaCache", "properties": {}, "widgets_values": ["TeaCache"]}, {"id": 145, "type": "GetNode", "pos": [2789.89697265625, -252.04800415039062], "size": [210, 58], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "SLGARGS", "name": "SLGARGS", "type": "SLGARGS", "links": [240]}], "title": "Get_SLG_args", "properties": {}, "widgets_values": ["SLG_args"]}, {"id": 117, "type": "GetNode", "pos": [2789.89697265625, -209.09950256347656], "size": [210, 60], "flags": {"collapsed": true}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "EXPERIMENTALARGS", "name": "EXPERIMENTALARGS", "type": "EXPERIMENTALARGS", "links": [198]}], "title": "Get_ExpArgs", "properties": {}, "widgets_values": ["ExpArgs"]}, {"id": 169, "type": "SetNode", "pos": [-1250, -320], "size": [210, 60], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "link": 267}], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [269, 270, 323, 324]}], "title": "Set_TextEncoder", "properties": {"previousName": "TextEncoder"}, "widgets_values": ["TextEncoder"], "color": "#432", "bgcolor": "#653"}, {"id": 104, "type": "WanVideoDiffusionForcingSampler", "pos": [2972.869873046875, -400.0001220703125], "size": [428.4000244140625, 860.4000244140625], "flags": {}, "order": 58, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 190}, {"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 330}, {"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 207}, {"label": "samples", "localized_name": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "prefix_samples", "localized_name": "prefix_samples", "name": "prefix_samples", "shape": 7, "type": "LATENT", "link": 180}, {"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "shape": 7, "type": "TEACACHEARGS", "link": 196}, {"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS", "link": 240}, {"label": "experimental_args", "localized_name": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS", "link": 198}, {"label": "unianimate_poses", "localized_name": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"localized_name": "addnoise_condition", "name": "addnoise_condition", "type": "INT", "widget": {"name": "addnoise_condition"}, "link": null}, {"localized_name": "fps", "name": "fps", "type": "FLOAT", "widget": {"name": "fps"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}, {"localized_name": "rope_function", "name": "rope_function", "shape": 7, "type": "COMBO", "widget": {"name": "rope_function"}, "link": null}], "outputs": [{"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "links": [178]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e5a326c9811514f2c08c89bccea9a7c731d9a503", "Node name for S&R": "WanVideoDiffusionForcingSampler"}, "widgets_values": [24, 24.000000000000004, 30, 4.000000000000001, 5.000000000000001, 0, "fixed", true, "unipc", 1, "comfy"]}, {"id": 50, "type": "CLIPTextEncode", "pos": [-1040, -240], "size": [400, 200], "flags": {}, "order": 45, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 270}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [55]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", [false, true]], "color": "#322", "bgcolor": "#533"}, {"id": 46, "type": "WanVideoTextEmbedBridge", "pos": [-511.7523498535156, -329.84149169921875], "size": [315, 46], "flags": {}, "order": 48, "mode": 0, "inputs": [{"label": "positive", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 54}, {"label": "negative", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 55}], "outputs": [{"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "links": [191]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTextEmbedBridge"}, "widgets_values": [], "color": "#432", "bgcolor": "#653"}, {"id": 112, "type": "SetNode", "pos": [-95.33609771728516, -297.973388671875], "size": [210, 60], "flags": {"collapsed": true}, "order": 50, "mode": 0, "inputs": [{"label": "WANVIDEOTEXTEMBEDS", "name": "WANVIDEOTEXTEMBEDS", "type": "WANVIDEOTEXTEMBEDS", "link": 191}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_WanTextEmbeds", "properties": {"previousName": "WanTextEmbeds"}, "widgets_values": ["WanTextEmbeds"], "color": "#432", "bgcolor": "#653"}, {"id": 122, "type": "GetNode", "pos": [586.0716552734375, -439.12530517578125], "size": [210, 60], "flags": {"collapsed": true}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [203]}], "title": "Get_Width", "properties": {}, "widgets_values": ["<PERSON><PERSON><PERSON>"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 123, "type": "GetNode", "pos": [575.79052734375, -384.0552673339844], "size": [210, 60], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [204]}], "title": "Get_Height", "properties": {}, "widgets_values": ["Height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 82, "type": "WanVideoEmptyEmbeds", "pos": [760.8065185546875, -422.9595031738281], "size": [342.5999755859375, 106], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "control_embeds", "localized_name": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 203}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 204}, {"localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": null}], "outputs": [{"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [297]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e5a326c9811514f2c08c89bccea9a7c731d9a503", "Node name for S&R": "WanVideoEmptyEmbeds"}, "widgets_values": [512, 512, 53]}, {"id": 103, "type": "WanVideoDiffusionForcingSampler", "pos": [1166.916015625, -509.81597900390625], "size": [428.4000244140625, 860.4000244140625], "flags": {}, "order": 47, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 199}, {"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 200}, {"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 297}, {"label": "samples", "localized_name": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "prefix_samples", "localized_name": "prefix_samples", "name": "prefix_samples", "shape": 7, "type": "LATENT", "link": 183}, {"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "shape": 7, "type": "TEACACHEARGS", "link": 235}, {"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS", "link": 239}, {"label": "experimental_args", "localized_name": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS", "link": 236}, {"label": "unianimate_poses", "localized_name": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"localized_name": "addnoise_condition", "name": "addnoise_condition", "type": "INT", "widget": {"name": "addnoise_condition"}, "link": null}, {"localized_name": "fps", "name": "fps", "type": "FLOAT", "widget": {"name": "fps"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}, {"localized_name": "rope_function", "name": "rope_function", "shape": 7, "type": "COMBO", "widget": {"name": "rope_function"}, "link": null}], "outputs": [{"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "links": [171]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e5a326c9811514f2c08c89bccea9a7c731d9a503", "Node name for S&R": "WanVideoDiffusionForcingSampler"}, "widgets_values": [10, 24.000000000000004, 30, 4.000000000000001, 5.000000000000001, 0, "fixed", true, "unipc", 1, "comfy"]}, {"id": 28, "type": "WanVideoDecode", "pos": [1250.5562744140625, -773.344482421875], "size": [315, 174], "flags": {}, "order": 49, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 43}, {"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "link": 171}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}], "outputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [311, 312, 313]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoDecode"}, "widgets_values": [false, 272, 272, 144, 128], "color": "#322", "bgcolor": "#533"}, {"id": 194, "type": "GetNode", "pos": [1687.5537109375, -619.5382080078125], "size": [210, 60], "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [301]}], "title": "Get_OverlapFrameCount", "properties": {}, "widgets_values": ["OverlapFrameCount"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 93, "type": "GetImageRangeFromBatch", "pos": [1872.482421875, -758.1996459960938], "size": [428.4000244140625, 102], "flags": {}, "order": 51, "mode": 0, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 311}, {"label": "masks", "localized_name": "masks", "name": "masks", "shape": 7, "type": "MASK"}, {"localized_name": "start_index", "name": "start_index", "type": "INT", "widget": {"name": "start_index"}, "link": null}, {"label": "num_frames", "localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 301}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [314]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "366b866f8ebf501e84f316ab313d489e3f5cd94d", "Node name for S&R": "GetImageRangeFromBatch"}, "widgets_values": [-1, 17]}, {"id": 205, "type": "Text Load Line From File", "pos": [3452.869873046875, 599.9999389648438], "size": [315, 198], "flags": {}, "order": 54, "mode": 0, "inputs": [{"label": "multiline_text", "localized_name": "multiline_text", "name": "multiline_text", "shape": 7, "type": "STRING", "link": 343}, {"localized_name": "file_path", "name": "file_path", "type": "STRING", "widget": {"name": "file_path"}, "link": null}, {"localized_name": "dictionary_name", "name": "dictionary_name", "type": "STRING", "widget": {"name": "dictionary_name"}, "link": null}, {"localized_name": "label", "name": "label", "type": "STRING", "widget": {"name": "label"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"label": "index", "localized_name": "index", "name": "index", "type": "INT", "widget": {"name": "index"}, "link": 337}], "outputs": [{"label": "line_text", "localized_name": "line_text", "name": "line_text", "type": "STRING", "slot_index": 0, "links": [327]}, {"label": "dictionary", "localized_name": "dictionary", "name": "dictionary", "type": "DICT"}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "9ae952b1b435d2bd846bfe6516919b5a8b9201aa", "Node name for S&R": "Text Load Line From File"}, "widgets_values": ["", "[filename]", "TextBatch", "index", 1]}, {"id": 89, "type": "WanVideoDecode", "pos": [3432.869873046875, -470], "size": [315, 174], "flags": {}, "order": 59, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 188}, {"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "link": 178}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}], "outputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [318, 335]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoDecode"}, "widgets_values": [false, 272, 272, 144, 128], "color": "#322", "bgcolor": "#533"}, {"id": 206, "type": "CLIPTextEncode", "pos": [3902.869873046875, 860], "size": [400, 200], "flags": {}, "order": 46, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 323}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [326]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", [false, true]], "color": "#322", "bgcolor": "#533"}, {"id": 35, "type": "WanVideoTorchCompileSettings", "pos": [-810, -1300], "size": [421.6000061035156, 203.9819793701172], "flags": {}, "order": 11, "mode": 4, "inputs": [{"localized_name": "backend", "name": "backend", "type": "COMBO", "widget": {"name": "backend"}, "link": null}, {"localized_name": "fullgraph", "name": "fullgraph", "type": "BOOLEAN", "widget": {"name": "fullgraph"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}, {"localized_name": "dynamic", "name": "dynamic", "type": "BOOLEAN", "widget": {"name": "dynamic"}, "link": null}, {"localized_name": "dynamo_cache_size_limit", "name": "dynamo_cache_size_limit", "type": "INT", "widget": {"name": "dynamo_cache_size_limit"}, "link": null}, {"localized_name": "compile_transformer_blocks_only", "name": "compile_transformer_blocks_only", "type": "BOOLEAN", "widget": {"name": "compile_transformer_blocks_only"}, "link": null}, {"localized_name": "dynamo_recompile_limit", "name": "dynamo_recompile_limit", "shape": 7, "type": "INT", "widget": {"name": "dynamo_recompile_limit"}, "link": null}], "outputs": [{"label": "torch_compile_args", "localized_name": "torch_compile_args", "name": "torch_compile_args", "type": "WANCOMPILEARGS", "slot_index": 0, "links": []}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTorchCompileSettings"}, "widgets_values": ["inductor", false, "default", false, 64, true, 128], "color": "#223", "bgcolor": "#335"}, {"id": 127, "type": "INTConstant", "pos": [520, -970], "size": [261.0429382324219, 85.25131225585938], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "value", "localized_name": "value", "name": "value", "type": "INT", "links": [209]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "366b866f8ebf501e84f316ab313d489e3f5cd94d", "Node name for S&R": "INTConstant"}, "widgets_values": [17], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 210, "type": "GetImageSizeAndCount", "pos": [3517.752685546875, -171.8631591796875], "size": [277.20001220703125, 86], "flags": {"collapsed": false}, "order": 61, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 335}], "outputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "links": [333]}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT"}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT"}, {"label": "count", "localized_name": "count", "name": "count", "type": "INT", "links": [331]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "366b866f8ebf501e84f316ab313d489e3f5cd94d", "Node name for S&R": "GetImageSizeAndCount"}, "widgets_values": []}, {"id": 209, "type": "GetNode", "pos": [3467.897216796875, 80.61260986328125], "size": [210, 58], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [332]}], "title": "Get_OverlapFrameCount", "properties": {}, "widgets_values": ["OverlapFrameCount"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 211, "type": "SimpleMath+", "pos": [3856.957275390625, -166.7803955078125], "size": [315, 98], "flags": {"collapsed": false}, "order": 62, "mode": 0, "inputs": [{"label": "a", "localized_name": "a", "name": "a", "shape": 7, "type": "INT,FLOAT", "link": 331}, {"label": "b", "localized_name": "b", "name": "b", "shape": 7, "type": "INT,FLOAT", "link": 332}, {"localized_name": "value", "name": "value", "type": "STRING", "widget": {"name": "value"}, "link": null}, {"label": "c", "localized_name": "c", "name": "c", "shape": 7, "type": "*"}], "outputs": [{"label": "INT", "localized_name": "整数", "name": "INT", "type": "INT", "links": [334]}, {"label": "FLOAT", "localized_name": "浮点", "name": "FLOAT", "type": "FLOAT"}], "properties": {"cnr_id": "comfyui_essentials", "ver": "76e9d1e4399bd025ce8b12c290753d58f9f53e93", "Node name for S&R": "SimpleMath+", "aux_id": "kijai/ComfyUI_essentials"}, "widgets_values": ["a-b"]}, {"id": 212, "type": "GetImageRangeFromBatch", "pos": [3837.363525390625, 73.88627624511719], "size": [428.4000244140625, 102], "flags": {"collapsed": false}, "order": 63, "mode": 0, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 333}, {"label": "masks", "localized_name": "masks", "name": "masks", "shape": 7, "type": "MASK"}, {"localized_name": "start_index", "name": "start_index", "type": "INT", "widget": {"name": "start_index"}, "link": null}, {"label": "num_frames", "localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 334}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [336]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "366b866f8ebf501e84f316ab313d489e3f5cd94d", "Node name for S&R": "GetImageRangeFromBatch"}, "widgets_values": [-1, 80]}, {"id": 195, "type": "GetNode", "pos": [3464.7294921875, -714.4935913085938], "size": [210, 58], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [302]}], "title": "Get_OverlapFrameCount", "properties": {}, "widgets_values": ["OverlapFrameCount"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 133, "type": "GetImageRangeFromBatch", "pos": [3995.124755859375, -760.951171875], "size": [428.4000244140625, 102], "flags": {}, "order": 60, "mode": 0, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 318}, {"label": "masks", "localized_name": "masks", "name": "masks", "shape": 7, "type": "MASK"}, {"localized_name": "start_index", "name": "start_index", "type": "INT", "widget": {"name": "start_index"}, "link": null}, {"label": "num_frames", "localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 302}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [316]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "366b866f8ebf501e84f316ab313d489e3f5cd94d", "Node name for S&R": "GetImageRangeFromBatch"}, "widgets_values": [-1, 17]}, {"id": 108, "type": "GetNode", "pos": [2532.240478515625, -726.5841674804688], "size": [210, 60], "flags": {"collapsed": true}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "links": [187]}], "title": "Get_VAE", "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 105, "type": "WanVideoEncode", "pos": [2531.628662109375, -619.157470703125], "size": [330, 242], "flags": {}, "order": 55, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 187}, {"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 315}, {"label": "mask", "localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}, {"localized_name": "noise_aug_strength", "name": "noise_aug_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "noise_aug_strength"}, "link": null}, {"localized_name": "latent_strength", "name": "latent_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "latent_strength"}, "link": null}], "outputs": [{"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "links": [180]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e5a326c9811514f2c08c89bccea9a7c731d9a503", "Node name for S&R": "WanVideoEncode"}, "widgets_values": [false, 272, 272, 144, 128, 0, 1], "color": "#322", "bgcolor": "#533"}, {"id": 128, "type": "SetNode", "pos": [897.2467651367188, -955.3472900390625], "size": [210, 60], "flags": {"collapsed": true}, "order": 33, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 209}], "outputs": [{"label": "*", "name": "*", "type": "*", "links": []}], "title": "Set_OverlapFrameCount", "properties": {"previousName": "OverlapFrameCount"}, "widgets_values": ["OverlapFrameCount"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 78, "type": "VHS_VideoCombine", "pos": [1636.1868896484375, -473.4078674316406], "size": [291.642333984375, 334], "flags": {}, "order": 52, "mode": 0, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "link": 312}, {"label": "audio", "localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE"}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"label": "Filenames", "localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "SkyreelsDiffusionForcing_01", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "SkyreelsDiffusionForcing_01_00003_mfozl_1745567302.mp4", "workflow": "SkyreelsDiffusionForcing_01_00003.png", "fullpath": "/data/ComfyUI/personal/fdfb2ce455efd876711c763d174500f7/temp/SkyreelsDiffusionForcing_01_00003.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "temp", "frame_rate": 24}}}}, {"id": 207, "type": "CLIPTextEncode", "pos": [3902.869873046875, 589.9999389648438], "size": [400, 200], "flags": {}, "order": 56, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 324}, {"label": "text", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 327}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [325]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["一个女孩缓缓的向前行走，后面跟着一只白虎,", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 208, "type": "WanVideoTextEmbedBridge", "pos": [4416.193359375, 774.089111328125], "size": [315, 46], "flags": {}, "order": 57, "mode": 0, "inputs": [{"label": "positive", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 325}, {"label": "negative", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 326}], "outputs": [{"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "links": [330]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoTextEmbedBridge"}, "widgets_values": [], "color": "#432", "bgcolor": "#653"}, {"id": 199, "type": "easy forLoopEnd", "pos": [4881.19580078125, -1132.7393798828125], "size": [178, 106], "flags": {}, "order": 65, "mode": 0, "inputs": [{"label": "flow", "localized_name": "结束", "name": "flow", "shape": 5, "type": "FLOW_CONTROL", "link": 308}, {"label": "initial_value1", "localized_name": "initial_value1", "name": "initial_value1", "shape": 7, "type": "*", "link": 309}, {"label": "initial_value2", "name": "initial_value2", "type": "*", "link": 316}, {"label": "initial_value4", "name": "initial_value4", "type": "*"}, {"name": "initial_value5", "type": "*", "link": null}], "outputs": [{"label": "value1", "localized_name": "值1", "name": "value1", "type": "*", "slot_index": 0, "links": [307]}, {"label": "value2", "name": "value2", "type": "*"}, {"label": "value4", "name": "value4", "type": "*"}, {"name": "value5", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "0104f7f6a9ef02ac6651221c4bc2b742c184e79a", "Node name for S&R": "easy forLoopEnd"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 200, "type": "easy batchAnything", "pos": [4552.3515625, -1090.0391845703125], "size": [210, 46], "flags": {}, "order": 64, "mode": 0, "inputs": [{"label": "any_1", "localized_name": "任何1", "name": "any_1", "type": "*", "link": 310}, {"label": "any_2", "localized_name": "任何2", "name": "any_2", "type": "*", "link": 336}], "outputs": [{"label": "batch", "localized_name": "批次", "name": "batch", "type": "*", "slot_index": 0, "links": [309]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "0104f7f6a9ef02ac6651221c4bc2b742c184e79a", "Node name for S&R": "easy batchAnything"}, "widgets_values": []}, {"id": 106, "type": "WanVideoTeaCache", "pos": [520, -1670], "size": [380.4000244140625, 178], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "rel_l1_thresh", "name": "rel_l1_thresh", "type": "FLOAT", "widget": {"name": "rel_l1_thresh"}, "link": null}, {"localized_name": "start_step", "name": "start_step", "type": "INT", "widget": {"name": "start_step"}, "link": null}, {"localized_name": "end_step", "name": "end_step", "type": "INT", "widget": {"name": "end_step"}, "link": null}, {"localized_name": "cache_device", "name": "cache_device", "type": "COMBO", "widget": {"name": "cache_device"}, "link": null}, {"localized_name": "use_coefficients", "name": "use_coefficients", "type": "BOOLEAN", "widget": {"name": "use_coefficients"}, "link": null}, {"localized_name": "mode", "name": "mode", "shape": 7, "type": "COMBO", "widget": {"name": "mode"}, "link": null}], "outputs": [{"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "type": "TEACACHEARGS", "links": [195]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "65f5505fca115c23f3eb60689a2cf793d11b90df", "Node name for S&R": "WanVideoTeaCache"}, "widgets_values": [0.10000000000000002, 6, -1, "offload_device", true, "e0"]}, {"id": 198, "type": "VHS_VideoCombine", "pos": [5042.9267578125, -534.9450073242188], "size": [420.3818664550781, 334], "flags": {}, "order": 66, "mode": 0, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "link": 307}, {"label": "audio", "localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE"}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"label": "Filenames", "localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "124c913ccdd8a585734ea758c35fa1bab8499c99", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "WanVideo2_1", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideo2_1_00002_ajaub_1745567562.mp4", "workflow": "WanVideo2_1_00002.png", "fullpath": "/data/ComfyUI/personal/fdfb2ce455efd876711c763d174500f7/output/WanVideo2_1_00002.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 24}}}}, {"id": 197, "type": "easy forLoopStart", "pos": [2387.756591796875, -1161.475830078125], "size": [210, 158], "flags": {}, "order": 53, "mode": 0, "inputs": [{"label": "initial_value1", "localized_name": "initial_value1", "name": "initial_value1", "shape": 7, "type": "*", "link": 313}, {"localized_name": "总量", "name": "total", "type": "INT", "widget": {"name": "total"}, "link": null}, {"label": "initial_value2", "name": "initial_value2", "type": "*", "link": 314}, {"label": "initial_value4", "name": "initial_value4", "type": "*"}, {"name": "initial_value5", "type": "*", "link": null}], "outputs": [{"label": "flow", "localized_name": "开始", "name": "flow", "shape": 5, "type": "FLOW_CONTROL", "slot_index": 0, "links": [308]}, {"label": "index", "localized_name": "索引", "name": "index", "type": "INT", "slot_index": 1, "links": [337]}, {"label": "value1", "localized_name": "value1", "name": "value1", "type": "*", "slot_index": 2, "links": [310]}, {"label": "value2", "name": "value2", "type": "*", "slot_index": 3, "links": [315]}, {"label": "value4", "name": "value4", "type": "*"}, {"name": "value5", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "0104f7f6a9ef02ac6651221c4bc2b742c184e79a", "Node name for S&R": "easy forLoopStart"}, "widgets_values": [2], "color": "#223", "bgcolor": "#335"}, {"id": 142, "type": "WanVideoSLG", "pos": [525.31640625, -1142.8824462890625], "size": [315, 106], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "blocks", "name": "blocks", "type": "STRING", "widget": {"name": "blocks"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "type": "SLGARGS", "links": [238]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "65f5505fca115c23f3eb60689a2cf793d11b90df", "Node name for S&R": "WanVideoSLG"}, "widgets_values": ["9,10", 0.5000000000000001, 0.8000000000000002]}, {"id": 49, "type": "CLIPTextEncode", "pos": [-1040, -490], "size": [400, 200], "flags": {}, "order": 44, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 269}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [54]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A red motorcycle speeds through a rainy night", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 124, "type": "WanVideoEmptyEmbeds", "pos": [2592.869873046875, -149.99998474121094], "size": [342.5999755859375, 106], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "control_embeds", "localized_name": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 205}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 206}, {"localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": null}], "outputs": [{"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [207]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e5a326c9811514f2c08c89bccea9a7c731d9a503", "Node name for S&R": "WanVideoEmptyEmbeds"}, "widgets_values": [512, 512, 53]}, {"id": 107, "type": "SetNode", "pos": [113.87886047363281, -533.1160888671875], "size": [210, 60], "flags": {"collapsed": true}, "order": 36, "mode": 0, "inputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "link": 186}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_VAE", "properties": {"previousName": "VAE"}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "WanVideoVAELoader", "pos": [-361.78936767578125, -733.7146606445312], "size": [499.713623046875, 83.63792419433594], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "shape": 7, "type": "COMBO", "widget": {"name": "precision"}, "link": null}], "outputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [43, 128, 186]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoVAELoader"}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#322", "bgcolor": "#533"}, {"id": 214, "type": "Text Multiline", "pos": [2862.749267578125, 608.7967529296875], "size": [400, 200], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "字符串", "name": "STRING", "type": "STRING", "links": [343]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Text Multiline"}, "widgets_values": ["A red motorcycle speeds through a rainy night\nA red motorcycle speeds through a rainy night", [false, true]]}, {"id": 184, "type": "Note", "pos": [-89.30052185058594, -142.39903259277344], "size": [294.9496765136719, 139.27662658691406], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Input can be nothing for text2vid, a single image for image2vid or any number of frames for video extension"], "color": "#432", "bgcolor": "#653"}, {"id": 120, "type": "SetNode", "pos": [288.5196533203125, 132.0226593017578], "size": [210, 60], "flags": {"collapsed": true}, "order": 42, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 341}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_Width", "properties": {"previousName": "<PERSON><PERSON><PERSON>"}, "widgets_values": ["<PERSON><PERSON><PERSON>"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 121, "type": "SetNode", "pos": [291.3054504394531, 228.99374389648438], "size": [210, 60], "flags": {"collapsed": true}, "order": 43, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 342}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_Height", "properties": {"previousName": "Height"}, "widgets_values": ["Height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 84, "type": "WanVideoEncode", "pos": [733.8919677734375, -263.95745849609375], "size": [330, 242], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 128}, {"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 340}, {"label": "mask", "localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}, {"localized_name": "noise_aug_strength", "name": "noise_aug_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "noise_aug_strength"}, "link": null}, {"localized_name": "latent_strength", "name": "latent_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "latent_strength"}, "link": null}], "outputs": [{"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "links": [183]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "e5a326c9811514f2c08c89bccea9a7c731d9a503", "Node name for S&R": "WanVideoEncode"}, "widgets_values": [false, 272, 272, 144, 128, 0, 1], "color": "#322", "bgcolor": "#533"}, {"id": 118, "type": "GetNode", "pos": [915.0751953125, -764.2933959960938], "size": [210, 58], "flags": {"collapsed": false}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"label": "WANVIDEOMODEL", "name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "links": [199]}], "title": "Get_WanModel", "properties": {}, "widgets_values": ["WanModel"], "color": "#223", "bgcolor": "#335"}, {"id": 119, "type": "GetNode", "pos": [938.0263671875, -648.6298217773438], "size": [210, 58], "flags": {"collapsed": false}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"label": "WANVIDEOTEXTEMBEDS", "name": "WANVIDEOTEXTEMBEDS", "type": "WANVIDEOTEXTEMBEDS", "links": [200]}], "title": "Get_WanTextEmbeds", "properties": {}, "widgets_values": ["WanTextEmbeds"], "color": "#432", "bgcolor": "#653"}, {"id": 140, "type": "GetNode", "pos": [724.8881225585938, -579.0637817382812], "size": [210, 58], "flags": {"collapsed": true}, "order": 23, "mode": 0, "inputs": [], "outputs": [{"label": "TEACACHEARGS", "name": "TEACACHEARGS", "type": "TEACACHEARGS", "links": [235]}], "title": "Get_TeaCache", "properties": {}, "widgets_values": ["TeaCache"]}, {"id": 141, "type": "GetNode", "pos": [733.8970947265625, -524.1089477539062], "size": [210, 58], "flags": {"collapsed": true}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"label": "EXPERIMENTALARGS", "name": "EXPERIMENTALARGS", "type": "EXPERIMENTALARGS", "links": [236]}], "title": "Get_ExpArgs", "properties": {}, "widgets_values": ["ExpArgs"]}, {"id": 144, "type": "GetNode", "pos": [733.1347045898438, -481.9584655761719], "size": [210, 60], "flags": {"collapsed": true}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"label": "SLGARGS", "name": "SLGARGS", "type": "SLGARGS", "links": [239]}], "title": "Get_SLG_args", "properties": {}, "widgets_values": ["SLG_args"]}, {"id": 22, "type": "WanVideoModelLoader", "pos": [-377.06671142578125, -1046.866455078125], "size": [528.6734619140625, 254], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "compile_args", "localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS"}, {"label": "lora", "localized_name": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "vram_management_args", "localized_name": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "localized_name": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH"}, {"localized_name": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [189]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoModelLoader"}, "widgets_values": ["SKYREEL\\Wan2_1-SkyReels-V2-DF-14B-540P_fp8_e4m3fn.safetensors", "fp16", "disabled", "offload_device", "sdpa"], "color": "#223", "bgcolor": "#335"}, {"id": 213, "type": "ImageResizeKJ", "pos": [-110.93486022949219, 55.49329376220703], "size": [315, 266], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 339}, {"label": "get_image_size", "localized_name": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE"}, {"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "keep_proportion", "name": "keep_proportion", "type": "BOOLEAN", "widget": {"name": "keep_proportion"}, "link": null}, {"localized_name": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}, "link": null}, {"localized_name": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [340]}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "links": [341]}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "links": [342]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImageResizeKJ"}, "widgets_values": [960, 544, "lanc<PERSON>s", false, 16, "center"]}, {"id": 187, "type": "LoadImage", "pos": [-789.0867919921875, 8.756402969360352], "size": [631.8269653320312, 449.5911865234375], "flags": {}, "order": 27, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [339]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_yrcht_00004_.png", "image"]}, {"id": 39, "type": "WanVideoBlockSwap", "pos": [-760.8101806640625, -1043.6922607421875], "size": [315, 154], "flags": {}, "order": 39, "mode": 4, "inputs": [{"localized_name": "blocks_to_swap", "name": "blocks_to_swap", "type": "INT", "widget": {"name": "blocks_to_swap"}, "link": null}, {"localized_name": "offload_img_emb", "name": "offload_img_emb", "type": "BOOLEAN", "widget": {"name": "offload_img_emb"}, "link": null}, {"localized_name": "offload_txt_emb", "name": "offload_txt_emb", "type": "BOOLEAN", "widget": {"name": "offload_txt_emb"}, "link": null}, {"localized_name": "use_non_blocking", "name": "use_non_blocking", "shape": 7, "type": "BOOLEAN", "widget": {"name": "use_non_blocking"}, "link": null}, {"localized_name": "vace_blocks_to_swap", "name": "vace_blocks_to_swap", "shape": 7, "type": "INT", "widget": {"name": "vace_blocks_to_swap"}, "link": 344}], "outputs": [{"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "slot_index": 0, "links": []}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoBlockSwap"}, "widgets_values": [40, true, true, true, 0], "color": "#223", "bgcolor": "#335"}, {"id": 215, "type": "Reroute", "pos": [-707.1238403320312, -860.0014038085938], "size": [75, 26], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": null}], "outputs": [{"name": "", "type": "*", "links": [344]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 48, "type": "CLIPLoader", "pos": [-1620, -350], "size": [315, 106], "flags": {}, "order": 29, "mode": 0, "inputs": [{"localized_name": "CLIP名称", "name": "clip_name", "type": "COMBO", "widget": {"name": "clip_name"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [267]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.26", "Node name for S&R": "CLIPLoader"}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#432", "bgcolor": "#653"}], "links": [[43, 38, 0, 28, 0, "VAE"], [54, 49, 0, 46, 0, "CONDITIONING"], [55, 50, 0, 46, 1, "CONDITIONING"], [128, 38, 0, 84, 0, "WANVAE"], [171, 103, 0, 28, 1, "LATENT"], [178, 104, 0, 89, 1, "LATENT"], [180, 105, 0, 104, 4, "LATENT"], [183, 84, 0, 103, 4, "LATENT"], [186, 38, 0, 107, 0, "*"], [187, 108, 0, 105, 0, "WANVAE"], [188, 109, 0, 89, 0, "WANVAE"], [189, 22, 0, 110, 0, "*"], [190, 111, 0, 104, 0, "WANVIDEOMODEL"], [191, 46, 0, 112, 0, "*"], [195, 106, 0, 114, 0, "*"], [196, 115, 0, 104, 5, "TEACACHEARGS"], [197, 87, 0, 116, 0, "*"], [198, 117, 0, 104, 7, "EXPERIMENTALARGS"], [199, 118, 0, 103, 0, "WANVIDEOMODEL"], [200, 119, 0, 103, 1, "WANVIDEOTEXTEMBEDS"], [203, 122, 0, 82, 1, "INT"], [204, 123, 0, 82, 2, "INT"], [205, 126, 0, 124, 1, "INT"], [206, 125, 0, 124, 2, "INT"], [207, 124, 0, 104, 2, "WANVIDIMAGE_EMBEDS"], [209, 127, 0, 128, 0, "*"], [235, 140, 0, 103, 5, "TEACACHEARGS"], [236, 141, 0, 103, 7, "EXPERIMENTALARGS"], [238, 142, 0, 143, 0, "*"], [239, 144, 0, 103, 6, "SLGARGS"], [240, 145, 0, 104, 6, "SLGARGS"], [267, 48, 0, 169, 0, "*"], [269, 169, 0, 49, 0, "CLIP"], [270, 169, 0, 50, 0, "CLIP"], [297, 82, 0, 103, 2, "WANVIDIMAGE_EMBEDS"], [301, 194, 0, 93, 3, "INT"], [302, 195, 0, 133, 3, "INT"], [307, 199, 0, 198, 0, "IMAGE"], [308, 197, 0, 199, 0, "FLOW_CONTROL"], [309, 200, 0, 199, 1, "*"], [310, 197, 2, 200, 0, "*"], [311, 28, 0, 93, 0, "IMAGE"], [312, 28, 0, 78, 0, "IMAGE"], [313, 28, 0, 197, 0, "*"], [314, 93, 0, 197, 2, "*"], [315, 197, 3, 105, 1, "IMAGE"], [316, 133, 0, 199, 2, "*"], [318, 89, 0, 133, 0, "IMAGE"], [323, 169, 0, 206, 0, "CLIP"], [324, 169, 0, 207, 0, "CLIP"], [325, 207, 0, 208, 0, "CONDITIONING"], [326, 206, 0, 208, 1, "CONDITIONING"], [327, 205, 0, 207, 1, "STRING"], [330, 208, 0, 104, 1, "WANVIDEOTEXTEMBEDS"], [331, 210, 3, 211, 0, "*"], [332, 209, 0, 211, 1, "*"], [333, 210, 0, 212, 0, "IMAGE"], [334, 211, 0, 212, 3, "INT"], [335, 89, 0, 210, 0, "IMAGE"], [336, 212, 0, 200, 1, "*"], [337, 197, 1, 205, 5, "INT"], [339, 187, 0, 213, 0, "IMAGE"], [340, 213, 0, 84, 1, "IMAGE"], [341, 213, 1, 120, 0, "INT"], [342, 213, 2, 121, 0, "INT"], [343, 214, 0, 205, 0, "STRING"], [344, 215, 0, 39, 4, "INT"]], "groups": [{"id": 1, "title": "Sampler 1", "bounding": [503.95928955078125, -883.208251953125, 1851.056640625, 1370.1871337890625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Sampler 2", "bounding": [2384.84814453125, -877.103271484375, 2441.03076171875, 2916.99853515625], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.1577222547666344, "offset": [3465.067138671875, 2977.78662109375]}, "frontendVersion": "1.18.10", "VHS_KeepIntermediate": true, "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false, "node_versions": {"ComfyUI-WanVideoWrapper": "5a2383621a05825d0d0437781afcb8552d9590fd", "ComfyUI-VideoHelperSuite": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "ComfyUI-KJNodes": "a5bd3c86c8ed6b83c55c2d0e7a59515b15a0137f", "comfy-core": "0.3.26"}}, "version": 0.4}