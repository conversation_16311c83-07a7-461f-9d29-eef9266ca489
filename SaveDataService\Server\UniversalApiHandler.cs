using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace SaveDataService
{
    /// <summary>
    /// 通用 API 处理器 - 处理所有继承 RESTfulAPIBase 的类的 HTTP 请求
    /// </summary>
    public class UniversalApiHandler
    {
        /// <summary>
        /// 处理通用 API 请求
        /// </summary>
        /// <param name="context">HTTP 上下文</param>
        /// <param name="className">类名</param>
        /// <param name="methodName">方法名</param>
        public static async Task HandleApiRequest(HttpContext context, string className, string methodName)
        {
            try
            {
                // 设置 CORS 头
                context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                context.Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
                context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

                // 处理 OPTIONS 预检请求
                if (context.Request.Method == "OPTIONS")
                {
                    context.Response.StatusCode = 200;
                    return;
                }

                // 查找目标类型
                var assembly = Assembly.GetExecutingAssembly();
                var targetType = assembly.GetTypes()
                    .FirstOrDefault(t => t.Name.Equals(className, StringComparison.OrdinalIgnoreCase) && 
                                        typeof(RESTfulAPIBase).IsAssignableFrom(t));

                if (targetType == null)
                {
                    await WriteErrorResponse(context, $"未找到类 {className} 或该类未继承 RESTfulAPIBase");
                    return;
                }

                // 读取请求体
                string requestBody;
                using (var reader = new StreamReader(context.Request.Body))
                {
                    requestBody = await reader.ReadToEndAsync();
                }

                // 解析请求参数
                Dictionary<string, object> requestData;
                if (string.IsNullOrEmpty(requestBody))
                {
                    requestData = new Dictionary<string, object>();
                }
                else
                {
                    var rawData = JsonConvert.DeserializeObject<Dictionary<string, object>>(requestBody) ?? new Dictionary<string, object>();

                    // 检查是否是包装格式 (HTML 页面发送的格式)
                    if (rawData.ContainsKey("method") && rawData.ContainsKey("parameters"))
                    {
                        // 提取 parameters 部分
                        var parametersObj = rawData["parameters"];
                        if (parametersObj is Newtonsoft.Json.Linq.JObject jObj)
                        {
                            requestData = jObj.ToObject<Dictionary<string, object>>() ?? new Dictionary<string, object>();
                        }
                        else
                        {
                            requestData = new Dictionary<string, object>();
                        }
                    }
                    else
                    {
                        // 直接使用原始数据（向后兼容）
                        requestData = rawData;
                    }
                }

                // 调用对应的方法
                var result = await CallTargetMethod(targetType, methodName, requestData);

                context.Response.ContentType = "application/json; charset=utf-8";
                var jsonResponse = JsonConvert.SerializeObject(result, Formatting.Indented);
                await context.Response.WriteAsync(jsonResponse);
            }
            catch (Exception ex)
            {
                await WriteErrorResponse(context, ex.Message, ex.StackTrace);
            }
        }

        /// <summary>
        /// 调用目标类型的方法
        /// </summary>
        /// <param name="targetType">目标类型</param>
        /// <param name="methodName">方法名</param>
        /// <param name="requestData">请求数据</param>
        /// <returns>方法执行结果</returns>
        private static async Task<object> CallTargetMethod(Type targetType, string methodName, Dictionary<string, object> requestData)
        {
            try
            {
                // 查找匹配的方法（忽略大小写）
                var method = targetType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                    .FirstOrDefault(m => m.Name.Equals(methodName, StringComparison.OrdinalIgnoreCase) && 
                                        m.DeclaringType == targetType);

                if (method == null)
                {
                    return new { error = $"在类 {targetType.Name} 中未找到方法 '{methodName}'" };
                }

                // 获取方法参数
                var parameters = method.GetParameters();
                var args = new object[parameters.Length];

                // 填充参数值
                for (int i = 0; i < parameters.Length; i++)
                {
                    var param = parameters[i];
                    var paramName = param.Name ?? "";

                    if (requestData.ContainsKey(paramName))
                    {
                        var value = requestData[paramName];
                        args[i] = ConvertParameter(value, param.ParameterType);
                    }
                    else if (param.HasDefaultValue)
                    {
                        args[i] = param.DefaultValue;
                    }
                    else
                    {
                        // 必需参数但未提供值
                        return new { error = $"必需参数 '{paramName}' 缺失" };
                    }
                }

                // 调用方法
                var result = method.Invoke(null, args);

                // 如果是异步方法，等待结果
                if (result is Task task)
                {
                    await task;
                    var resultProperty = task.GetType().GetProperty("Result");
                    if (resultProperty != null)
                    {
                        result = resultProperty.GetValue(task);
                    }
                }

                return result ?? new { message = "方法执行成功" };
            }
            catch (Exception ex)
            {
                return new { error = ex.Message, stackTrace = ex.StackTrace };
            }
        }

        /// <summary>
        /// 转换参数类型
        /// </summary>
        /// <param name="value">原始值</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>转换后的值</returns>
        private static object? ConvertParameter(object? value, Type targetType)
        {
            if (value == null)
                return null;

            // 处理可空类型
            if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                targetType = targetType.GetGenericArguments()[0];
            }

            // 如果类型已经匹配，直接返回
            if (targetType.IsAssignableFrom(value.GetType()))
                return value;

            // 处理字符串类型
            if (targetType == typeof(string))
                return value.ToString();

            // 处理数值类型
            if (targetType == typeof(int))
                return Convert.ToInt32(value);
            if (targetType == typeof(long))
                return Convert.ToInt64(value);
            if (targetType == typeof(bool))
                return Convert.ToBoolean(value);
            if (targetType == typeof(double))
                return Convert.ToDouble(value);
            if (targetType == typeof(float))
                return Convert.ToSingle(value);
            if (targetType == typeof(decimal))
                return Convert.ToDecimal(value);

            // 处理数组类型
            if (targetType.IsArray)
            {
                var elementType = targetType.GetElementType()!;
                if (value is Newtonsoft.Json.Linq.JArray jArray)
                {
                    var array = Array.CreateInstance(elementType, jArray.Count);
                    for (int i = 0; i < jArray.Count; i++)
                    {
                        var convertedElement = ConvertParameter(jArray[i], elementType);
                        array.SetValue(convertedElement, i);
                    }
                    return array;
                }
            }

            // 尝试使用 Convert.ChangeType
            try
            {
                return Convert.ChangeType(value, targetType);
            }
            catch
            {
                // 如果转换失败，返回原值
                return value;
            }
        }

        /// <summary>
        /// 写入错误响应
        /// </summary>
        /// <param name="context">HTTP 上下文</param>
        /// <param name="message">错误消息</param>
        /// <param name="stackTrace">堆栈跟踪（可选）</param>
        private static async Task WriteErrorResponse(HttpContext context, string message, string? stackTrace = null)
        {
            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json; charset=utf-8";
            
            var errorResponse = stackTrace != null
                ? new { error = message, stackTrace = stackTrace }
                : new { error = message, stackTrace = (string?)null };
                
            var jsonResponse = JsonConvert.SerializeObject(errorResponse, Formatting.Indented);
            await context.Response.WriteAsync(jsonResponse);
        }

        /// <summary>
        /// 获取所有可用的 API 类列表
        /// </summary>
        /// <returns>API 类列表</returns>
        public static List<string> GetAvailableApiClasses()
        {
            var assembly = Assembly.GetExecutingAssembly();
            return assembly.GetTypes()
                .Where(t => typeof(RESTfulAPIBase).IsAssignableFrom(t) && 
                           !t.IsAbstract && 
                           t != typeof(RESTfulAPIBase))
                .Select(t => t.Name)
                .ToList();
        }

        /// <summary>
        /// 处理 API 描述请求
        /// </summary>
        /// <param name="context">HTTP 上下文</param>
        /// <param name="className">类名（可选）</param>
        public static async Task HandleApiDescription(HttpContext context, string? className = null)
        {
            try
            {
                context.Response.ContentType = "application/json; charset=utf-8";
                
                string jsonResponse;
                if (string.IsNullOrEmpty(className))
                {
                    // 返回所有 API 的描述
                    jsonResponse = RESTfulAPIGen.GetAllHttpPostFunctions();
                }
                else
                {
                    // 返回指定类的 API 描述
                    jsonResponse = RESTfulAPIGen.GetHttpPostFunction(className);
                }

                await context.Response.WriteAsync(jsonResponse);
            }
            catch (Exception ex)
            {
                await WriteErrorResponse(context, ex.Message, ex.StackTrace);
            }
        }
    }
}
