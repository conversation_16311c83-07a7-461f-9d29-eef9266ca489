# SaveDataService

一个基于 .NET 9.0 的多功能数据服务平台，集成了 AI 对话、Excel 数据处理、数据库管理和 WebSocket 通信等功能。

## 🚀 主要功能

### AI 对话服务
- **DeepSeek AI 集成**: 支持与 DeepSeek AI 模型进行实时对话
- **流式响应**: 支持流式输出，实时获取 AI 生成内容
- **多对话管理**: 支持同时管理多个对话会话
- **对话状态跟踪**: 实时跟踪对话状态（未开始、运行中、已完成、已取消）
- **游戏内容生成**: 专门针对游戏策划的 AI 内容生成功能

### 数据处理服务
- **Excel 数据转换**: 自动读取 Excel 文件并转换为二进制数据
- **数据库同步**: 将 Excel 数据同步到 MySQL 数据库
- **ORM 支持**: 使用 Entity Framework Core 进行数据库操作
- **数据表管理**: 支持数据库表的创建、刷新和管理

### Web 服务
- **HTTP API**: 提供 RESTful API 接口
- **WebSocket 支持**: 实时双向通信
- **跨平台部署**: 支持 Docker 容器化部署
- **响应压缩**: 内置响应压缩功能

### 云服务集成
- **Redis 缓存**: 分布式缓存和会话管理
- **阿里云 OSS**: 对象存储服务集成
- **Kubernetes**: 支持 K8s 集群管理和监控
- **腾讯云 TKE**: 腾讯云容器服务集成

## 🛠️ 技术栈

- **.NET 9.0**: 主要开发框架
- **ASP.NET Core**: Web 框架
- **Entity Framework Core**: ORM 框架
- **MySQL**: 主数据库
- **Redis**: 缓存数据库
- **Semantic Kernel**: AI 集成框架
- **Docker**: 容器化部署
- **Kubernetes**: 容器编排

## 📋 系统要求

- .NET 9.0 SDK
- MySQL 数据库
- Redis 服务器
- Docker (可选，用于容器化部署)

## 🚀 快速开始

### 1. 环境配置

确保已安装以下软件：
- .NET 9.0 SDK
- MySQL Server
- Redis Server

### 2. 配置文件

项目会自动生成配置文件 `SaveDataService.appconfig.json`，主要配置项包括：

```json
{
  "mysqlServer": "192.168.1.99",
  "mysqlPort": 3306,
  "mysqlUser": "root",
  "mysqlPasswrd": "your_password",
  "mysqlDatabaseName": "worldgpt_zq",
  "redisServer": "192.168.1.99",
  "redisPort": 6379,
  "redisPwd": "111111"
}
```

### 3. 运行项目

```bash
# 克隆项目
git clone <repository-url>
cd SaveDataService

# 还原依赖
dotnet restore

# 运行项目
dotnet run
```

### 4. Docker 部署

```bash
# 构建镜像
docker build -t savedataservice .

# 运行容器
docker run -p 7778:7778 savedataservice
```

## 📡 API 接口

### AI 对话接口

| 端点 | 方法 | 描述 |
|------|------|------|
| `/chat` | POST | DeepSeek AI 对话 |
| `/conversation` | GET | 获取对话内容 |
| `/active-conversations` | GET | 获取活跃对话列表 |
| `/cancel-conversation` | POST | 取消对话生成 |
| `/multi-chat` | POST | 多对话处理 |

### 游戏内容生成接口

| 端点 | 方法 | 描述 |
|------|------|------|
| `/basetemplate` | POST | 设置基础模板 |
| `/userinput` | POST | 用户输入生成游戏描述 |
| `/worldviewandtarget` | POST | 生成世界观和游戏目标 |
| `/chapter` | POST | 生成游戏章节 |
| `/characterkeys` | POST | 生成角色设定关键字 |
| `/scencekeys` | POST | 生成场景设定关键字 |
| `/scencemusic` | POST | 生成场景背景音乐 |
| `/scenceeffect` | POST | 生成场景音效 |
| `/skybox` | POST | 生成天空盒子 |

### 数据管理接口

| 端点 | 方法 | 描述 |
|------|------|------|
| `/reexceldb` | POST | 刷新 Excel 数据到数据库 |

## 🗂️ 项目结构

```
SaveDataService/
├── Manage/                 # 管理模块
│   ├── AiConversation.cs   # AI 对话管理
│   ├── DeepseekManager.cs  # DeepSeek AI 管理器
│   ├── NewDeepSeekManager.cs # 新版 DeepSeek 管理器
│   ├── ExcelDataManager.cs # Excel 数据管理器
│   ├── PostManager.cs      # HTTP 请求管理器
│   └── NewPostManager.cs   # 新版请求管理器
├── Server/                 # 服务器模块
│   ├── BaseServer.cs       # 基础服务器
│   ├── KestrelServerSetup.cs # Kestrel 服务器配置
│   └── Websocket/          # WebSocket 服务
├── Service/                # 服务模块
│   ├── Excel/              # Excel 处理服务
│   ├── ORM/                # ORM 数据库服务
│   └── Tools/              # 工具服务
├── Util/                   # 工具类
├── Demo/                   # 示例代码
├── Res/                    # 资源文件
│   └── baseExcel/          # Excel 模板文件
└── UnityAutoCode/          # Unity 自动代码生成
```

## 🔧 配置说明

### 数据库配置
- 支持 MySQL 数据库
- 自动创建和管理数据表
- 支持 ORM 日志记录

### AI 服务配置
- DeepSeek API 集成
- 支持自定义模型参数
- 流式响应处理

### 缓存配置
- Redis 分布式缓存
- 支持分布式锁
- 会话管理

## 📝 开发说明

### 添加新的 API 端点

1. 在 `NewPostManager.cs` 中添加新的路由：
```csharp
httpserver.SetHttpAction("/your-endpoint", onYourHandler);
```

2. 实现对应的处理方法：
```csharp
static async Task onYourHandler(HttpContext context)
{
    // 处理逻辑
}
```

### 数据库模型修改

1. 修改 `ORMModel.cs` 中的数据模型
2. 清空相关数据库表
3. 重启服务器自动重新填充数据

## 🐳 Docker 支持

项目包含完整的 Docker 配置：
- 多阶段构建优化
- 支持调试和生产环境
- 自动依赖安装

## 📄 许可证

[添加许可证信息]

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

[添加联系方式]
