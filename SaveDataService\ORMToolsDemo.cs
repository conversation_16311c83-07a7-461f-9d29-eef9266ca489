using System;
using System.IO;
using System.Collections.Generic;
using ORMTools;
using ExcelToData;

namespace SaveDataService
{
    /// <summary>
    /// ORMTools使用演示类
    /// </summary>
    public class ORMToolsDemo
    {
        /// <summary>
        /// 演示如何使用ORMTools生成CSV文件 - 专门分析ComfyUI相关实体类
        /// </summary>
        public static void RunDemo()
        {
            Console.WriteLine("=== ORMTools ComfyUI实体类分析演示 ===");

            try
            {
                // 创建输出目录
                string outputDir = Path.Combine(AppContext.BaseDirectory, "ComfyUI_CSV_Output");
                if (!Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                Console.WriteLine($"📁 CSV文件输出目录: {outputDir}");
                Console.WriteLine($"📁 完整路径: {Path.GetFullPath(outputDir)}");

                // 获取所有ComfyUI相关的实体类
                var comfyUIEntityTypes = new[]
                {
                    typeof(ComfyUIServer),
                    typeof(ComfyUIWorkflow),
                    typeof(ComfyUITask)
                };

                Console.WriteLine($"\n🔍 找到 {comfyUIEntityTypes.Length} 个ComfyUI实体类");

                var generatedFiles = new List<string>();
                int successCount = 0;
                int failCount = 0;

                foreach (var entityType in comfyUIEntityTypes)
                {
                    try
                    {
                        Console.WriteLine($"\n📊 正在分析: {entityType.Name}");

                        string csvPath = ORMEntityAnalyzer.GenerateEntityCsv(entityType, outputDir);
                        generatedFiles.Add(csvPath);
                        successCount++;

                        // 显示文件信息
                        if (File.Exists(csvPath))
                        {
                            var fileInfo = new FileInfo(csvPath);
                            var lines = File.ReadAllLines(csvPath);

                            Console.WriteLine($"   ✅ 成功生成: {Path.GetFileName(csvPath)}");
                            Console.WriteLine($"   📏 文件大小: {fileInfo.Length} 字节");
                            Console.WriteLine($"   🔢 属性数量: {lines[0].Split(',').Length}");
                            Console.WriteLine($"   📍 文件路径: {csvPath}");

                            // 显示前几个属性作为预览
                            if (lines.Length >= 3)
                            {
                                var propertyNames = lines[0].Split(',');
                                var propertyTypes = lines[1].Split(',');
                                var propertyComments = lines[2].Split(',');

                                Console.WriteLine("   🔍 属性预览:");
                                for (int i = 0; i < Math.Min(5, propertyNames.Length); i++)
                                {
                                    string name = propertyNames[i].Trim('"');
                                    string type = propertyTypes[i].Trim('"');
                                    string comment = i < propertyComments.Length ? propertyComments[i].Trim('"') : "";
                                    Console.WriteLine($"      • {name} ({type}): {comment}");
                                }
                                if (propertyNames.Length > 5)
                                {
                                    Console.WriteLine($"      ... 还有 {propertyNames.Length - 5} 个属性");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ 分析 {entityType.Name} 失败: {ex.Message}");
                        failCount++;
                    }
                }

                Console.WriteLine("\n" + new string('=', 60));
                Console.WriteLine("📋 分析结果汇总:");
                Console.WriteLine($"   ✅ 成功: {successCount} 个实体类");
                Console.WriteLine($"   ❌ 失败: {failCount} 个实体类");
                Console.WriteLine($"   📁 输出目录: {outputDir}");

                Console.WriteLine("\n📄 生成的CSV文件列表:");
                foreach (var filePath in generatedFiles)
                {
                    var fileName = Path.GetFileName(filePath);
                    var fileSize = new FileInfo(filePath).Length;
                    Console.WriteLine($"   📄 {fileName} ({fileSize} 字节)");
                }

                Console.WriteLine("\n📖 CSV文件格式说明：");
                Console.WriteLine("   • 第一行：属性名称");
                Console.WriteLine("   • 第二行：数据类型（数组/List显示为如string[]格式）");
                Console.WriteLine("   • 第三行：注释内容");

                Console.WriteLine("\n💡 使用提示：");
                Console.WriteLine("   • 可以使用Excel或其他工具打开CSV文件查看");
                Console.WriteLine("   • 数组和List类型会显示为如int[]、string[]格式");
                Console.WriteLine("   • 可空类型会显示为如int?格式");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 演示过程中发生错误: {ex.Message}");
                Console.WriteLine($"🔍 错误详情: {ex}");
            }

            Console.WriteLine("\n=== ORMTools ComfyUI实体类分析演示结束 ===");
        }

        /// <summary>
        /// 分析指定类型并生成CSV
        /// </summary>
        /// <param name="entityType">要分析的实体类型</param>
        /// <param name="outputPath">输出路径</param>
        public static void AnalyzeEntity(Type entityType, string outputPath = ".")
        {
            try
            {
                Console.WriteLine($"\n正在分析实体类: {entityType.Name}");
                string csvPath = ORMEntityAnalyzer.GenerateEntityCsv(entityType, outputPath);
                Console.WriteLine($"CSV文件已生成: {csvPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析实体类 {entityType.Name} 时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量分析多个实体类
        /// </summary>
        /// <param name="entityTypes">实体类型数组</param>
        /// <param name="outputPath">输出路径</param>
        public static void AnalyzeMultipleEntities(Type[] entityTypes, string outputPath = ".")
        {
            Console.WriteLine($"\n开始批量分析 {entityTypes.Length} 个实体类...");
            
            int successCount = 0;
            int failCount = 0;
            
            foreach (var entityType in entityTypes)
            {
                try
                {
                    AnalyzeEntity(entityType, outputPath);
                    successCount++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"分析 {entityType.Name} 失败: {ex.Message}");
                    failCount++;
                }
            }
            
            Console.WriteLine($"\n批量分析完成: 成功 {successCount} 个，失败 {failCount} 个");
        }
    }
}
