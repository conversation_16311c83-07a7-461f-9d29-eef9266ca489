using System;
using System.IO;
using ORMTools;
using ExcelToData;

namespace SaveDataService
{
    /// <summary>
    /// ORMTools使用示例类
    /// </summary>
    public class ORMToolsUsageExample
    {
        /// <summary>
        /// 演示如何使用ORMTools分析现有的ORM实体类
        /// </summary>
        public static void DemonstrateORMToolsUsage()
        {
            Console.WriteLine("=== ORMTools 使用演示 ===");
            
            try
            {
                // 创建输出目录
                string outputDir = Path.Combine(AppContext.BaseDirectory, "EntityAnalysis");
                if (!Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                Console.WriteLine($"CSV文件将保存到: {outputDir}");

                // 分析各种实体类
                var entityTypes = new[]
                {
                    typeof(Account),
                    typeof(ErrorInfo),
                    typeof(TimeEvent),
                    typeof(SeverData),
                    typeof(ComfyUIServer),
                    typeof(ComfyUITask),
                    typeof(ComfyUIWorkflow)
                };

                Console.WriteLine($"\n开始分析 {entityTypes.Length} 个实体类...");

                int successCount = 0;
                int failCount = 0;

                foreach (var entityType in entityTypes)
                {
                    try
                    {
                        Console.WriteLine($"\n正在分析: {entityType.Name}");
                        
                        string csvPath = ORMEntityAnalyzer.GenerateEntityCsv(entityType, outputDir);
                        
                        // 显示生成的CSV文件的基本信息
                        if (File.Exists(csvPath))
                        {
                            var lines = File.ReadAllLines(csvPath);
                            Console.WriteLine($"  ✓ 成功生成 {entityType.Name}.csv");
                            Console.WriteLine($"  ✓ 属性数量: {lines[0].Split(',').Length}");
                            Console.WriteLine($"  ✓ 文件路径: {csvPath}");
                            
                            // 显示前几个属性作为预览
                            if (lines.Length >= 3)
                            {
                                var propertyNames = lines[0].Split(',');
                                var propertyTypes = lines[1].Split(',');
                                var propertyComments = lines[2].Split(',');
                                
                                Console.WriteLine("  ✓ 属性预览:");
                                for (int i = 0; i < Math.Min(3, propertyNames.Length); i++)
                                {
                                    Console.WriteLine($"    - {propertyNames[i]} ({propertyTypes[i]}): {propertyComments[i]}");
                                }
                                if (propertyNames.Length > 3)
                                {
                                    Console.WriteLine($"    ... 还有 {propertyNames.Length - 3} 个属性");
                                }
                            }
                        }
                        
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  ✗ 分析 {entityType.Name} 失败: {ex.Message}");
                        failCount++;
                    }
                }

                Console.WriteLine($"\n=== 分析完成 ===");
                Console.WriteLine($"成功: {successCount} 个");
                Console.WriteLine($"失败: {failCount} 个");
                Console.WriteLine($"输出目录: {outputDir}");

                // 列出所有生成的文件
                Console.WriteLine("\n生成的CSV文件列表:");
                var csvFiles = Directory.GetFiles(outputDir, "*.csv");
                foreach (var file in csvFiles)
                {
                    var fileInfo = new FileInfo(file);
                    Console.WriteLine($"  - {fileInfo.Name} ({fileInfo.Length} 字节)");
                }

                Console.WriteLine("\n=== 使用说明 ===");
                Console.WriteLine("生成的CSV文件格式:");
                Console.WriteLine("- 第一行: 属性名称");
                Console.WriteLine("- 第二行: 数据类型 (数组/List显示为如string[]格式)");
                Console.WriteLine("- 第三行: 注释内容");
                Console.WriteLine("\n您可以使用Excel或其他工具打开这些CSV文件进行查看和编辑。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"演示过程中发生错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex}");
            }
        }

        /// <summary>
        /// 分析单个实体类的详细信息
        /// </summary>
        /// <param name="entityType">要分析的实体类型</param>
        /// <param name="outputPath">输出路径</param>
        public static void AnalyzeSingleEntity(Type entityType, string outputPath = ".")
        {
            try
            {
                Console.WriteLine($"\n=== 分析实体类: {entityType.Name} ===");
                
                string csvPath = ORMEntityAnalyzer.GenerateEntityCsv(entityType, outputPath);
                
                if (File.Exists(csvPath))
                {
                    Console.WriteLine($"CSV文件已生成: {csvPath}");
                    
                    // 读取并显示详细内容
                    var lines = File.ReadAllLines(csvPath);
                    if (lines.Length >= 3)
                    {
                        var propertyNames = lines[0].Split(',');
                        var propertyTypes = lines[1].Split(',');
                        var propertyComments = lines[2].Split(',');
                        
                        Console.WriteLine($"\n实体类 {entityType.Name} 包含 {propertyNames.Length} 个属性:");
                        Console.WriteLine(new string('-', 80));
                        Console.WriteLine($"{"属性名",-20} {"类型",-15} {"注释",-40}");
                        Console.WriteLine(new string('-', 80));
                        
                        for (int i = 0; i < propertyNames.Length; i++)
                        {
                            string name = propertyNames[i].Trim('"');
                            string type = propertyTypes[i].Trim('"');
                            string comment = i < propertyComments.Length ? propertyComments[i].Trim('"') : "";
                            
                            Console.WriteLine($"{name,-20} {type,-15} {comment,-40}");
                        }
                        Console.WriteLine(new string('-', 80));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析实体类 {entityType.Name} 时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量分析指定命名空间中的所有实体类
        /// </summary>
        /// <param name="namespaceName">命名空间名称</param>
        /// <param name="outputPath">输出路径</param>
        public static void AnalyzeEntitiesInNamespace(string namespaceName, string outputPath = ".")
        {
            try
            {
                Console.WriteLine($"\n=== 分析命名空间: {namespaceName} ===");
                
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var types = assembly.GetTypes()
                    .Where(t => t.Namespace == namespaceName && t.IsClass && !t.IsAbstract)
                    .ToArray();
                
                Console.WriteLine($"找到 {types.Length} 个类型");
                
                foreach (var type in types)
                {
                    try
                    {
                        AnalyzeSingleEntity(type, outputPath);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"分析类型 {type.Name} 时出错: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析命名空间 {namespaceName} 时发生错误: {ex.Message}");
            }
        }
    }
}
