{"id": "6fe8c8e2-bf07-4c5d-8e3e-af47c46e75df", "revision": 0, "last_node_id": 354, "last_link_id": 538, "nodes": [{"id": 324, "type": "Mask Gaussian Region", "pos": [-8886.544921875, -868.3756713867188], "size": [270, 58], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "masks", "name": "masks", "type": "MASK", "link": 488}, {"localized_name": "radius", "name": "radius", "type": "FLOAT", "widget": {"name": "radius"}, "link": null}], "outputs": [{"localized_name": "MASKS", "name": "MASKS", "type": "MASK", "links": [495]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1.0.2", "Node name for S&R": "Mask Gaussian Region"}, "widgets_values": [5]}, {"id": 321, "type": "GrowMask", "pos": [-9286.1904296875, -849.1388549804688], "size": [270, 82], "flags": {"collapsed": false}, "order": 17, "mode": 0, "inputs": [{"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 494}, {"localized_name": "扩展", "name": "expand", "type": "INT", "widget": {"name": "expand"}, "link": null}, {"localized_name": "倒角", "name": "tapered_corners", "type": "BOOLEAN", "widget": {"name": "tapered_corners"}, "link": null}], "outputs": [{"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": [488]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "GrowMask"}, "widgets_values": [15, true]}, {"id": 338, "type": "MaskPreview+", "pos": [-9550.853515625, -337.1397399902344], "size": [150.14315795898438, 246], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "mask", "name": "mask", "type": "MASK", "link": 496}], "outputs": [], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 312, "type": "CLIPTextEncode", "pos": [-8953.5712890625, -492.8201599121094], "size": [400, 200], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 447}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [452, 502]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 319, "type": "InpaintModelConditioning", "pos": [-8157.88623046875, -556.784912109375], "size": [270, 138], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 451}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 452}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 471}, {"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 525}, {"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 495}, {"localized_name": "噪波遮罩", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}], "outputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "links": [453]}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "links": [454]}, {"localized_name": "Latent", "name": "latent", "type": "LATENT", "links": [455]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 343, "type": "Reroute", "pos": [-9317.7099609375, 529.86669921875], "size": [75, 26], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 513}], "outputs": [{"name": "", "type": "MASK", "links": [514]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 315, "type": "KSamplerSelect", "pos": [-7325.55859375, -856.5877685546875], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"label": "采样器", "localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [443, 518]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 308, "type": "UNETLoader", "pos": [-8540.857421875, -404.643310546875], "size": [315, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [492]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "UNETLoader"}, "widgets_values": ["F.1-Fill-fp16_Inpaint&Outpaint_1.0.safetensors", "fp8_e4m3fn"]}, {"id": 337, "type": "LoraLoaderModelOnly", "pos": [-8148.08056640625, -315.53997802734375], "size": [270, 82], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 492}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [493]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["removal_timestep_alpha-2-1740.safetensors", 1]}, {"id": 318, "type": "VAELoader", "pos": [-8486.5498046875, -789.8217163085938], "size": [270, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [450, 471, 503, 516]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 346, "type": "PreviewImage", "pos": [-9783.205078125, -368.9332275390625], "size": [235.37484741210938, 369.36529541015625], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 521}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 347, "type": "INPAINT_InpaintWithModel", "pos": [-9813.060546875, -567.4926147460938], "size": [270, 142], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "inpaint_model", "name": "inpaint_model", "type": "INPAINT_MODEL", "link": 522}, {"localized_name": "image", "name": "image", "type": "IMAGE", "link": 523}, {"localized_name": "mask", "name": "mask", "type": "MASK", "link": 524}, {"localized_name": "optional_upscale_model", "name": "optional_upscale_model", "shape": 7, "type": "UPSCALE_MODEL", "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [521, 525]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_InpaintWithModel"}, "widgets_values": [788339165756166, "randomize"]}, {"id": 290, "type": "InpaintCropImproved", "pos": [-10203.6201171875, -756.8104248046875], "size": [348.095703125, 626], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 486}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 497}, {"localized_name": "optional_context_mask", "name": "optional_context_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "downscale_algorithm", "name": "downscale_algorithm", "type": "COMBO", "widget": {"name": "downscale_algorithm"}, "link": null}, {"localized_name": "upscale_algorithm", "name": "upscale_algorithm", "type": "COMBO", "widget": {"name": "upscale_algorithm"}, "link": null}, {"localized_name": "preresize", "name": "preresize", "type": "BOOLEAN", "widget": {"name": "preresize"}, "link": null}, {"localized_name": "preresize_mode", "name": "preresize_mode", "type": "COMBO", "widget": {"name": "preresize_mode"}, "link": null}, {"localized_name": "preresize_min_width", "name": "preresize_min_width", "type": "INT", "widget": {"name": "preresize_min_width"}, "link": null}, {"localized_name": "preresize_min_height", "name": "preresize_min_height", "type": "INT", "widget": {"name": "preresize_min_height"}, "link": null}, {"localized_name": "preresize_max_width", "name": "preresize_max_width", "type": "INT", "widget": {"name": "preresize_max_width"}, "link": null}, {"localized_name": "preresize_max_height", "name": "preresize_max_height", "type": "INT", "widget": {"name": "preresize_max_height"}, "link": null}, {"localized_name": "mask_fill_holes", "name": "mask_fill_holes", "type": "BOOLEAN", "widget": {"name": "mask_fill_holes"}, "link": null}, {"localized_name": "mask_expand_pixels", "name": "mask_expand_pixels", "type": "INT", "widget": {"name": "mask_expand_pixels"}, "link": null}, {"localized_name": "mask_invert", "name": "mask_invert", "type": "BOOLEAN", "widget": {"name": "mask_invert"}, "link": null}, {"localized_name": "mask_blend_pixels", "name": "mask_blend_pixels", "type": "INT", "widget": {"name": "mask_blend_pixels"}, "link": null}, {"localized_name": "mask_hipass_filter", "name": "mask_hipass_filter", "type": "FLOAT", "widget": {"name": "mask_hipass_filter"}, "link": null}, {"localized_name": "extend_for_outpainting", "name": "extend_for_outpainting", "type": "BOOLEAN", "widget": {"name": "extend_for_outpainting"}, "link": null}, {"localized_name": "extend_up_factor", "name": "extend_up_factor", "type": "FLOAT", "widget": {"name": "extend_up_factor"}, "link": null}, {"localized_name": "extend_down_factor", "name": "extend_down_factor", "type": "FLOAT", "widget": {"name": "extend_down_factor"}, "link": null}, {"localized_name": "extend_left_factor", "name": "extend_left_factor", "type": "FLOAT", "widget": {"name": "extend_left_factor"}, "link": null}, {"localized_name": "extend_right_factor", "name": "extend_right_factor", "type": "FLOAT", "widget": {"name": "extend_right_factor"}, "link": null}, {"localized_name": "context_from_mask_extend_factor", "name": "context_from_mask_extend_factor", "type": "FLOAT", "widget": {"name": "context_from_mask_extend_factor"}, "link": null}, {"localized_name": "output_resize_to_target_size", "name": "output_resize_to_target_size", "type": "BOOLEAN", "widget": {"name": "output_resize_to_target_size"}, "link": null}, {"localized_name": "output_target_width", "name": "output_target_width", "type": "INT", "widget": {"name": "output_target_width"}, "link": 461}, {"localized_name": "output_target_height", "name": "output_target_height", "type": "INT", "widget": {"name": "output_target_height"}, "link": 462}, {"localized_name": "output_padding", "name": "output_padding", "type": "COMBO", "widget": {"name": "output_padding"}, "link": null}], "outputs": [{"localized_name": "stitcher", "name": "stitcher", "type": "STITCHER", "links": [498]}, {"localized_name": "cropped_image", "name": "cropped_image", "type": "IMAGE", "links": [523]}, {"localized_name": "cropped_mask", "name": "cropped_mask", "type": "MASK", "links": [494, 496, 513, 524]}], "properties": {"cnr_id": "comfyui-inpaint-cropand<PERSON>itch", "ver": "2.1.7", "Node name for S&R": "InpaintCropImproved"}, "widgets_values": ["bilinear", "bicubic", false, "ensure minimum resolution", 1024, 1024, 16384, 16384, true, 0, false, 32, 0.1, false, 1, 1, 1, 1, 1.2, false, 512, 512, "32"]}, {"id": 322, "type": "PrimitiveInt", "pos": [-10537.5185546875, -276.7528381347656], "size": [270, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "INT", "type": "INT", "links": [460, 461, 462]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PrimitiveInt"}, "widgets_values": [1344, "fixed"]}, {"id": 288, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [-10560.76171875, -710.0988159179688], "size": [318.1509094238281, 357.59649658203125], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 407}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 408}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": 460}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [486]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": [497]}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "longest", 948, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 309, "type": "BasicScheduler", "pos": [-7753.494140625, -863.365478515625], "size": [315, 106], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 526}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"label": "降噪", "localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Sigmas", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [444]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 310, "type": "SamplerCustom", "pos": [-7865.62451171875, -564.9423828125], "size": [424.28216552734375, 547.2218017578125], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 477}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 453}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 454}, {"label": "采样器", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 443}, {"label": "Sigmas", "localized_name": "Sigmas", "name": "sigmas", "type": "SIGMAS", "link": 444}, {"label": "Latent", "localized_name": "Latent", "name": "latent_image", "type": "LATENT", "link": 455}, {"localized_name": "添加噪波", "name": "add_noise", "type": "BOOLEAN", "widget": {"name": "add_noise"}, "link": null}, {"localized_name": "噪波种子", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"label": "输出", "localized_name": "Latent", "name": "output", "type": "LATENT", "links": [449]}, {"label": "降噪输出", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 466682314042308, "randomize", 1]}, {"id": 311, "type": "CLIPTextEncode", "pos": [-8956.2705078125, -756.0133056640625], "size": [400, 200], "flags": {"collapsed": false}, "order": 9, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 446}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [448]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["simple background，Shining, golden starlight，1girl,purple dress,black stockings"]}, {"id": 314, "type": "DualCLIPLoader", "pos": [-9319.3154296875, -590.0062866210938], "size": [315, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [446, 447, 530]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 313, "type": "FluxGuidance", "pos": [-8497.9853515625, -628.6405639648438], "size": [315, 58], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 448}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [451]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 317, "type": "VAEDecode", "pos": [-7659.37939453125, -681.4656982421875], "size": [210, 46], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 449}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 450}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [438, 511, 529]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 307, "type": "PreviewImage", "pos": [-7399.44921875, -550.40234375], "size": [578.************, 555.8528442382812], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 438}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 352, "type": "PreviewImage", "pos": [-8079.31005859375, 535.1471557617188], "size": [867.1678466796875, 884.251220703125], "flags": {}, "order": 37, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 538}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 348, "type": "INPAINT_LoadInpaintModel", "pos": [-9809.01171875, -679.53271484375], "size": [270, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"localized_name": "INPAINT_MODEL", "name": "INPAINT_MODEL", "type": "INPAINT_MODEL", "links": [522]}], "properties": {"cnr_id": "comfyui-inpaint-nodes", "ver": "1.0.4", "Node name for S&R": "INPAINT_LoadInpaintModel"}, "widgets_values": ["big-lama.pt"]}, {"id": 287, "type": "LoadImage", "pos": [-10859.962890625, -651.0812377929688], "size": [270, 314], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [407]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": [408]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-546642.200000003.png [input]", "image"]}, {"id": 325, "type": "LayerUtility: JoyCaption2", "pos": [-9300.0771484375, 1193.8328857421875], "size": [303.7271423339844, 342], "flags": {}, "order": 27, "mode": 4, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 529}, {"localized_name": "额外选项", "name": "extra_options", "shape": 7, "type": "JoyCaption2ExtraOption", "link": null}, {"localized_name": "LLM模型", "name": "llm_model", "type": "COMBO", "widget": {"name": "llm_model"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "数据类型", "name": "dtype", "type": "COMBO", "widget": {"name": "dtype"}, "link": null}, {"localized_name": "VLM LoRA", "name": "vlm_lora", "type": "COMBO", "widget": {"name": "vlm_lora"}, "link": null}, {"localized_name": "字幕类型", "name": "caption_type", "type": "COMBO", "widget": {"name": "caption_type"}, "link": null}, {"localized_name": "字幕长度", "name": "caption_length", "type": "COMBO", "widget": {"name": "caption_length"}, "link": null}, {"localized_name": "用户提示", "name": "user_prompt", "type": "STRING", "widget": {"name": "user_prompt"}, "link": null}, {"localized_name": "最大新令牌数", "name": "max_new_tokens", "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "顶部P", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "温度", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}, {"localized_name": "使用全局模型", "name": "use_global_model", "type": "BOOLEAN", "widget": {"name": "use_global_model"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "shape": 6, "type": "STRING", "links": [467]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerUtility: JoyCaption2"}, "widgets_values": ["Orenguteng/Llama-3.1-8B-Lexi-Uncensored-V2", "cuda", "nf4", "text_model", "Descriptive", "any", "", 300, 0.9, 0.6, false, false], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 326, "type": "ShowText|pysssss", "pos": [-8957.23828125, 1212.11279296875], "size": [392.3556213378906, 118.68616485595703], "flags": {}, "order": 28, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 467}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [468]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["A captivating portrait of a young, spirited young woman, adorned in a majestic attire, is presented before us. Her striking features, accentuated by an effervescent complexion and an infectious grin, are set against a radiant backdrop of golden hues and soft, luminescent orbs. \n\nShe stands tall, her slender frame elegantly draped in a rich, velvety purple ensemble, complete with an intricately designed, lace-like pattern of gold accents that crisscross her torso. A crimson ribbon, tied with an air of whimsy, adds a pop of vibrant color to her ensemble, while her long, flowing locks, tied back in a loose ponytail, cascade down her back like a silken waterfall. \n\nA sprightly, white-haired girl, with piercing, crimson eyes, she exudes an aura of youthful vitality, as if poised to embark on a thrilling adventure at any moment. Her slender legs, clad in opaque, black stockings, seem to be in motion, as if she is about to take a step forward, her right arm raised in a gesture of excitement. \n\nThis enchanting portrait, bathed in a warm, golden light, seems to radiate an aura of joy, hope, and limitless possibility, inviting the viewer to step into the world of this captivating young woman and share in her boundless enthusiasm."]}, {"id": 327, "type": "CR Text", "pos": [-8969.845703125, 1373.0054931640625], "size": [400, 200], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [469]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["simple background，Shining, golden starlight，1girl,purple dress,black stockings,"]}, {"id": 332, "type": "DifferentialDiffusion", "pos": [-9388.7216796875, 18.475143432617188], "size": [216.77987670898438, 26], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 493}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [477, 504, 526, 527]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 340, "type": "InpaintModelConditioning", "pos": [-9283.595703125, 674.5171508789062], "size": [270, 138], "flags": {}, "order": 32, "mode": 0, "inputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 536}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 502}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 503}, {"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 511}, {"localized_name": "遮罩", "name": "mask", "type": "MASK", "link": 514}, {"localized_name": "噪波遮罩", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}], "outputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "links": [535]}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "links": [506]}, {"localized_name": "Latent", "name": "latent", "type": "LATENT", "links": [507]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 349, "type": "BasicScheduler", "pos": [-9325.0322265625, 494.55047607421875], "size": [315, 106], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 527}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"label": "降噪", "localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "Sigmas", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [528]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 0.4000000000000001]}, {"id": 344, "type": "VAEDecode", "pos": [-8476.19921875, 521.2581176757812], "size": [210, 46], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 515}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 516}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [517, 537]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 351, "type": "FluxGuidance", "pos": [-8517.5595703125, 384.9570007324219], "size": [315, 58], "flags": {"collapsed": false}, "order": 31, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 533}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [536]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 345, "type": "PreviewImage", "pos": [-8482.498046875, 646.66455078125], "size": [140, 246], "flags": {}, "order": 35, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 517}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 339, "type": "InpaintStitchImproved", "pos": [-8452.837890625, 970.9320068359375], "size": [215.52206420898438, 46], "flags": {}, "order": 36, "mode": 0, "inputs": [{"localized_name": "stitcher", "name": "stitcher", "type": "STITCHER", "link": 498}, {"localized_name": "inpainted_image", "name": "inpainted_image", "type": "IMAGE", "link": 537}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [538]}], "properties": {"cnr_id": "comfyui-inpaint-cropand<PERSON>itch", "ver": "2.1.7", "Node name for S&R": "InpaintStitchImproved"}, "widgets_values": []}, {"id": 328, "type": "CR Text Concatenate", "pos": [-8482.298828125, 1084.551025390625], "size": [270, 78], "flags": {}, "order": 29, "mode": 0, "inputs": [{"localized_name": "text1", "name": "text1", "shape": 7, "type": "STRING", "link": 468}, {"localized_name": "text2", "name": "text2", "shape": 7, "type": "STRING", "link": 469}, {"localized_name": "separator", "name": "separator", "shape": 7, "type": "STRING", "widget": {"name": "separator"}, "link": null}], "outputs": [{"localized_name": "STRING", "name": "STRING", "type": "*", "links": [531]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text Concatenate"}, "widgets_values": [""]}, {"id": 341, "type": "SamplerCustom", "pos": [-8964.5341796875, 603.1173095703125], "size": [424.28216552734375, 547.2218017578125], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 504}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 535}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 506}, {"label": "采样器", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 518}, {"label": "Sigmas", "localized_name": "Sigmas", "name": "sigmas", "type": "SIGMAS", "link": 528}, {"label": "Latent", "localized_name": "Latent", "name": "latent_image", "type": "LATENT", "link": 507}, {"localized_name": "添加噪波", "name": "add_noise", "type": "BOOLEAN", "widget": {"name": "add_noise"}, "link": null}, {"localized_name": "噪波种子", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"label": "输出", "localized_name": "Latent", "name": "output", "type": "LATENT", "links": [515]}, {"label": "降噪输出", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 324246618560153, "randomize", 1]}, {"id": 350, "type": "CLIPTextEncode", "pos": [-8961.6611328125, 346.0279541015625], "size": [400, 200], "flags": {"collapsed": false}, "order": 30, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 530}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 531}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [533]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["simple background，Shining, golden starlight，1girl,purple dress,black stockings"]}], "links": [[407, 287, 0, 288, 0, "IMAGE"], [408, 287, 1, 288, 1, "MASK"], [438, 317, 0, 307, 0, "IMAGE"], [443, 315, 0, 310, 3, "SAMPLER"], [444, 309, 0, 310, 4, "SIGMAS"], [446, 314, 0, 311, 0, "CLIP"], [447, 314, 0, 312, 0, "CLIP"], [448, 311, 0, 313, 0, "CONDITIONING"], [449, 310, 0, 317, 0, "LATENT"], [450, 318, 0, 317, 1, "VAE"], [451, 313, 0, 319, 0, "CONDITIONING"], [452, 312, 0, 319, 1, "CONDITIONING"], [453, 319, 0, 310, 1, "CONDITIONING"], [454, 319, 1, 310, 2, "CONDITIONING"], [455, 319, 2, 310, 5, "LATENT"], [460, 322, 0, 288, 9, "INT"], [461, 322, 0, 290, 23, "INT"], [462, 322, 0, 290, 24, "INT"], [467, 325, 0, 326, 0, "STRING"], [468, 326, 0, 328, 0, "STRING"], [469, 327, 0, 328, 1, "STRING"], [471, 318, 0, 319, 2, "VAE"], [477, 332, 0, 310, 0, "MODEL"], [486, 288, 0, 290, 0, "IMAGE"], [488, 321, 0, 324, 0, "MASK"], [492, 308, 0, 337, 0, "MODEL"], [493, 337, 0, 332, 0, "MODEL"], [494, 290, 2, 321, 0, "MASK"], [495, 324, 0, 319, 4, "MASK"], [496, 290, 2, 338, 0, "MASK"], [497, 288, 1, 290, 1, "MASK"], [498, 290, 0, 339, 0, "STITCHER"], [502, 312, 0, 340, 1, "CONDITIONING"], [503, 318, 0, 340, 2, "VAE"], [504, 332, 0, 341, 0, "MODEL"], [506, 340, 1, 341, 2, "CONDITIONING"], [507, 340, 2, 341, 5, "LATENT"], [511, 317, 0, 340, 3, "IMAGE"], [513, 290, 2, 343, 0, "*"], [514, 343, 0, 340, 4, "MASK"], [515, 341, 0, 344, 0, "LATENT"], [516, 318, 0, 344, 1, "VAE"], [517, 344, 0, 345, 0, "IMAGE"], [518, 315, 0, 341, 3, "SAMPLER"], [521, 347, 0, 346, 0, "IMAGE"], [522, 348, 0, 347, 0, "INPAINT_MODEL"], [523, 290, 1, 347, 1, "IMAGE"], [524, 290, 2, 347, 2, "MASK"], [525, 347, 0, 319, 3, "IMAGE"], [526, 332, 0, 309, 0, "MODEL"], [527, 332, 0, 349, 0, "MODEL"], [528, 349, 0, 341, 4, "SIGMAS"], [529, 317, 0, 325, 0, "IMAGE"], [530, 314, 0, 350, 0, "CLIP"], [531, 328, 0, 350, 1, "STRING"], [533, 350, 0, 351, 0, "CONDITIONING"], [535, 340, 0, 341, 1, "CONDITIONING"], [536, 351, 0, 340, 0, "CONDITIONING"], [537, 344, 0, 339, 1, "IMAGE"], [538, 339, 0, 352, 0, "IMAGE"]], "groups": [{"id": 3, "title": "Group", "bounding": [-9386.0693359375, -993.6643676757812, 2594.88671875, 1212.4803466796875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Group", "bounding": [-9327.7099609375, 262.38348388671875, 2331.126708984375, 1342.4859619140625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Group", "bounding": [-9831.3544921875, -762.1014404296875, 400.1341857910156, 766.3132934570312], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6588450000000016, "offset": [10313.557535185548, 206.19028335161698]}, "frontendVersion": "1.18.9", "0246.VERSION": [0, 0, 4], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}