{"id": "13615999-8f7a-4014-adb7-99f15001d232", "revision": 0, "last_node_id": 25, "last_link_id": 30, "nodes": [{"id": 1, "type": "WanVideoSLG", "pos": [590.7264404296875, 5356.72607421875], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "blocks", "name": "blocks", "type": "STRING", "widget": {"name": "blocks"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "type": "SLGARGS", "links": [17]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "ccca41cdb76d55b7d0f8bdd02fed1e86dd379fa2", "Node name for S&R": "WanVideoSLG"}, "widgets_values": ["9,10", 0.7000000000000002, 0.9000000000000001]}, {"id": 5, "type": "WanVideoEnhanceAVideo", "pos": [998.0396728515625, 5066.1845703125], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}, "link": null}, {"localized_name": "start_percent", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "end_percent", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"label": "feta_args", "localized_name": "feta_args", "name": "feta_args", "type": "FETAARGS", "links": [15]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "ccca41cdb76d55b7d0f8bdd02fed1e86dd379fa2", "Node name for S&R": "WanVideoEnhanceAVideo"}, "widgets_values": [2, 0, 1]}, {"id": 6, "type": "Reroute", "pos": [249.45297241210938, 6325.98388671875], "size": [75, 26], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 3}], "outputs": [{"name": "", "type": "STRING", "links": [9]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 8, "type": "WanVideoTextEncode", "pos": [564.6182861328125, 5857.72900390625], "size": [420.30511474609375, 261.5306701660156], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "t5", "localized_name": "t5", "name": "t5", "type": "WANTEXTENCODER", "link": 7}, {"label": "model_to_offload", "localized_name": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL", "link": 8}, {"label": "positive_prompt", "localized_name": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": 9}, {"localized_name": "negative_prompt", "name": "negative_prompt", "type": "STRING", "widget": {"name": "negative_prompt"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "shape": 7, "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}], "outputs": [{"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [13]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoTextEncode"}, "widgets_values": ["一个男人在发怒", "bad quality, blurry, messy, chaotic", true, [false, true], [false, true]], "color": "#432", "bgcolor": "#653"}, {"id": 9, "type": "WanVideoVAELoader", "pos": [-289.1678771972656, 5542.51611328125], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "shape": 7, "type": "COMBO", "widget": {"name": "precision"}, "link": null}], "outputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [1, 25]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoVAELoader"}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#322", "bgcolor": "#533"}, {"id": 10, "type": "LoadWanVideoT5TextEncoder", "pos": [131.35751342773438, 5971.146484375], "size": [377.1661376953125, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "shape": 7, "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "shape": 7, "type": "COMBO", "widget": {"name": "quantization"}, "link": null}], "outputs": [{"label": "wan_t5_model", "localized_name": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [7]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "LoadWanVideoT5TextEncoder"}, "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "disabled"], "color": "#432", "bgcolor": "#653"}, {"id": 15, "type": "WanVideoTeaCache", "pos": [590.7264404296875, 5136.72607421875], "size": [315, 178], "flags": {}, "order": 4, "mode": 4, "inputs": [{"localized_name": "rel_l1_thresh", "name": "rel_l1_thresh", "type": "FLOAT", "widget": {"name": "rel_l1_thresh"}, "link": null}, {"localized_name": "start_step", "name": "start_step", "type": "INT", "widget": {"name": "start_step"}, "link": null}, {"localized_name": "end_step", "name": "end_step", "type": "INT", "widget": {"name": "end_step"}, "link": null}, {"localized_name": "cache_device", "name": "cache_device", "type": "COMBO", "widget": {"name": "cache_device"}, "link": null}, {"localized_name": "use_coefficients", "name": "use_coefficients", "type": "BOOLEAN", "widget": {"name": "use_coefficients"}, "link": null}, {"localized_name": "mode", "name": "mode", "shape": 7, "type": "COMBO", "widget": {"name": "mode"}, "link": null}], "outputs": [{"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "type": "TEACACHEARGS", "links": [16]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d20baf00247fd06553fdc9253e18732244e54172", "Node name for S&R": "WanVideoTeaCache"}, "widgets_values": [0.26000000000000006, 1, -1, "offload_device", "true", "e"]}, {"id": 19, "type": "PreviewImage", "pos": [619.1156005859375, 6210.7939453125], "size": [342.69537353515625, 608.6505737304688], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 22}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 25, "type": "WanVideoVACEEncode", "pos": [61.14706802368164, 5568.3056640625], "size": [343.59088134765625, 334], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 25}, {"label": "input_frames", "localized_name": "input_frames", "name": "input_frames", "shape": 7, "type": "IMAGE", "link": null}, {"label": "ref_images", "localized_name": "ref_images", "name": "ref_images", "shape": 7, "type": "IMAGE", "link": 26}, {"label": "input_masks", "localized_name": "input_masks", "name": "input_masks", "shape": 7, "type": "MASK", "link": null}, {"label": "prev_vace_embeds", "localized_name": "prev_vace_embeds", "name": "prev_vace_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS", "link": null}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 27}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 28}, {"label": "num_frames", "localized_name": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 29}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "vace_start_percent", "name": "vace_start_percent", "type": "FLOAT", "widget": {"name": "vace_start_percent"}, "link": null}, {"localized_name": "vace_end_percent", "name": "vace_end_percent", "type": "FLOAT", "widget": {"name": "vace_end_percent"}, "link": null}, {"localized_name": "tiled_vae", "name": "tiled_vae", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_vae"}, "link": null}], "outputs": [{"label": "vace_embeds", "localized_name": "vace_embeds", "name": "vace_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [14]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoVACEEncode"}, "widgets_values": [480, 832, 29, 1.0590000000000002, 0, 1, true], "color": "#322", "bgcolor": "#533"}, {"id": 4, "type": "WanVideoDecode", "pos": [1041.577880859375, 5520.41943359375], "size": [315, 198], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "vae", "localized_name": "vae", "name": "vae", "type": "WANVAE", "link": 1}, {"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "link": 2}, {"localized_name": "enable_vae_tiling", "name": "enable_vae_tiling", "type": "BOOLEAN", "widget": {"name": "enable_vae_tiling"}, "link": null}, {"localized_name": "tile_x", "name": "tile_x", "type": "INT", "widget": {"name": "tile_x"}, "link": null}, {"localized_name": "tile_y", "name": "tile_y", "type": "INT", "widget": {"name": "tile_y"}, "link": null}, {"localized_name": "tile_stride_x", "name": "tile_stride_x", "type": "INT", "widget": {"name": "tile_stride_x"}, "link": null}, {"localized_name": "tile_stride_y", "name": "tile_stride_y", "type": "INT", "widget": {"name": "tile_stride_y"}, "link": null}], "outputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "links": [11]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoDecode"}, "widgets_values": [true, 272, 272, 144, 128], "color": "#322", "bgcolor": "#533"}, {"id": 14, "type": "VHS_VideoCombine", "pos": [1437.0205078125, 5472.04736328125], "size": [605.903076171875, 334], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "images", "localized_name": "images", "name": "images", "type": "IMAGE", "link": 11}, {"label": "audio", "localized_name": "audio", "name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"label": "meta_batch", "localized_name": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"label": "vae", "localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}, {"localized_name": "frame_rate", "name": "frame_rate", "type": "FLOAT", "widget": {"name": "frame_rate"}, "link": null}, {"localized_name": "loop_count", "name": "loop_count", "type": "INT", "widget": {"name": "loop_count"}, "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}, {"localized_name": "pingpong", "name": "pingpong", "type": "BOOLEAN", "widget": {"name": "pingpong"}, "link": null}, {"localized_name": "save_output", "name": "save_output", "type": "BOOLEAN", "widget": {"name": "save_output"}, "link": null}], "outputs": [{"label": "Filenames", "localized_name": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_VACE_startendframe", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_VACE_startendframe_00034_psjxl_1747476471.mp4", "workflow": "WanVideoWrapper_VACE_startendframe_00034.png", "fullpath": "/data/ComfyUI/personal/fdfb2ce455efd876711c763d174500f7/output/WanVideoWrapper_VACE_startendframe_00034.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 16}}}}, {"id": 11, "type": "WanVideoLoraSelect", "pos": [-786.8536376953125, 5292.3037109375], "size": [376.903564453125, 130.47491455078125], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "prev_lora", "localized_name": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA", "link": null}, {"label": "blocks", "localized_name": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS", "link": null}, {"localized_name": "lora", "name": "lora", "type": "COMBO", "widget": {"name": "lora"}, "link": null}, {"localized_name": "strength", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "low_mem_load", "name": "low_mem_load", "shape": 7, "type": "BOOLEAN", "widget": {"name": "low_mem_load"}, "link": null}], "outputs": [{"label": "lora", "localized_name": "lora", "name": "lora", "type": "WANVIDLORA", "links": [5]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "f2bc29b931983e279d25452d284b3888c8c81346", "Node name for S&R": "WanVideoLoraSelect"}, "widgets_values": ["CausVid\\Wan21_CausVid_14B_T2V_lora_rank32.safetensors", 0.8000000000000002, false]}, {"id": 16, "type": "WanVideoVACEModelSelect", "pos": [-786.8536376953125, 5522.3037109375], "size": [438.1178283691406, 112], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "vace_model", "name": "vace_model", "type": "COMBO", "widget": {"name": "vace_model"}, "link": null}], "outputs": [{"label": "vace_model", "localized_name": "vace_model", "name": "vace_model", "type": "VACEPATH", "links": [6]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "f2bc29b931983e279d25452d284b3888c8c81346", "Node name for S&R": "WanVideoVACEModelSelect"}, "widgets_values": ["vace\\Wan2_1-VACE_module_14B_fp8_e4m3fn.safetensors"]}, {"id": 2, "type": "WanVideoExperimentalArgs", "pos": [587.0326538085938, 5543.12255859375], "size": [327.5999755859375, 226], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "video_attention_split_steps", "name": "video_attention_split_steps", "type": "STRING", "widget": {"name": "video_attention_split_steps"}, "link": null}, {"localized_name": "cfg_zero_star", "name": "cfg_zero_star", "type": "BOOLEAN", "widget": {"name": "cfg_zero_star"}, "link": null}, {"localized_name": "use_zero_init", "name": "use_zero_init", "type": "BOOLEAN", "widget": {"name": "use_zero_init"}, "link": null}, {"localized_name": "zero_star_steps", "name": "zero_star_steps", "type": "INT", "widget": {"name": "zero_star_steps"}, "link": null}, {"localized_name": "use_fresca", "name": "use_fresca", "type": "BOOLEAN", "widget": {"name": "use_fresca"}, "link": null}, {"localized_name": "fresca_scale_low", "name": "fresca_scale_low", "type": "FLOAT", "widget": {"name": "fresca_scale_low"}, "link": null}, {"localized_name": "fresca_scale_high", "name": "fresca_scale_high", "type": "FLOAT", "widget": {"name": "fresca_scale_high"}, "link": null}, {"localized_name": "fresca_freq_cutoff", "name": "fresca_freq_cutoff", "type": "INT", "widget": {"name": "fresca_freq_cutoff"}, "link": null}], "outputs": [{"label": "exp_args", "localized_name": "exp_args", "name": "exp_args", "type": "EXPERIMENTALARGS", "links": [18]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d5085d4e879b5c9d56e39f57e656255ce72e81fc", "Node name for S&R": "WanVideoExperimentalArgs"}, "widgets_values": ["", true, false, 0, false, 1, 1.25, 20]}, {"id": 21, "type": "easy showAnything", "pos": [-334.96673583984375, 5898.9873046875], "size": [339.1517333984375, 424.70947265625], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "anything", "localized_name": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 24}], "outputs": [{"label": "output", "localized_name": "输出", "name": "output", "type": "*"}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy showAnything"}, "widgets_values": ["\"Visualizing the Intersection of Technology and Urbanization: A Futuristic Cityscape with a Global Information Hub\n\nThis captivating image presents a futuristic cityscape at night, where towering skyscrapers and neon-lit buildings stretch towards the sky, their peaks disappearing into the darkness. The city's skyline is dominated by a sleek, glowing globe, representing a global information hub that serves as the epicenter of technological innovation. The globe, with its transparent blue grid and illuminated continents, embodies the fusion of data visualization and urban planning, where geographical boundaries are transcended by the limitless potential of digital networks.\n\nThe city's infrastructure is depicted as a complex matrix of glowing lines and symbols, where data streams and information flows are constantly being generated and analyzed. This futuristic urban landscape is a testament to humanity's ingenuity in harnessing technology to create a more interconnected and efficient world. As the city's skyline stretches out into the distance, the image invites the viewer to contemplate the possibilities and challenges that arise from the intersection of technology and urbanization.\""]}, {"id": 3, "type": "WanVideoBlockSwap", "pos": [-297.4967956542969, 5677.33984375], "size": [315, 154], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "blocks_to_swap", "name": "blocks_to_swap", "type": "INT", "widget": {"name": "blocks_to_swap"}, "link": null}, {"localized_name": "offload_img_emb", "name": "offload_img_emb", "type": "BOOLEAN", "widget": {"name": "offload_img_emb"}, "link": null}, {"localized_name": "offload_txt_emb", "name": "offload_txt_emb", "type": "BOOLEAN", "widget": {"name": "offload_txt_emb"}, "link": null}, {"localized_name": "use_non_blocking", "name": "use_non_blocking", "shape": 7, "type": "BOOLEAN", "widget": {"name": "use_non_blocking"}, "link": null}, {"localized_name": "vace_blocks_to_swap", "name": "vace_blocks_to_swap", "shape": 7, "type": "INT", "widget": {"name": "vace_blocks_to_swap"}, "link": null}], "outputs": [{"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "links": []}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "f2bc29b931983e279d25452d284b3888c8c81346", "Node name for S&R": "WanVideoBlockSwap"}, "widgets_values": [40, true, true, true, 15]}, {"id": 13, "type": "LayerUtility: JoyCaption2", "pos": [-712.5896606445312, 5787.15869140625], "size": [303.7271423339844, 342], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 10}, {"localized_name": "额外选项", "name": "extra_options", "shape": 7, "type": "JoyCaption2ExtraOption", "link": null}, {"localized_name": "LLM模型", "name": "llm_model", "type": "COMBO", "widget": {"name": "llm_model"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "数据类型", "name": "dtype", "type": "COMBO", "widget": {"name": "dtype"}, "link": null}, {"localized_name": "VLM LoRA", "name": "vlm_lora", "type": "COMBO", "widget": {"name": "vlm_lora"}, "link": null}, {"localized_name": "字幕类型", "name": "caption_type", "type": "COMBO", "widget": {"name": "caption_type"}, "link": null}, {"localized_name": "字幕长度", "name": "caption_length", "type": "COMBO", "widget": {"name": "caption_length"}, "link": null}, {"localized_name": "用户提示", "name": "user_prompt", "type": "STRING", "widget": {"name": "user_prompt"}, "link": null}, {"localized_name": "最大新令牌数", "name": "max_new_tokens", "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "顶部P", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "温度", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}, {"localized_name": "使用全局模型", "name": "use_global_model", "type": "BOOLEAN", "widget": {"name": "use_global_model"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "shape": 6, "type": "STRING", "links": [3, 24]}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerUtility: JoyCaption2"}, "widgets_values": ["Orenguteng/Llama-3.1-8B-Lexi-Uncensored-V2", "cuda", "nf4", "text_model", "Descriptive", "any", "", 300, 0.9, 0.6, false, false], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 12, "type": "LoadImage", "pos": [-1107.3753662109375, 5744.18212890625], "size": [315, 314], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [10, 30]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_yrcht_00004_.png", "image"]}, {"id": 18, "type": "ImageResizeKJ", "pos": [137.9469451904297, 6178.94580078125], "size": [315, 266], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 30}, {"label": "get_image_size", "localized_name": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE", "link": null}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 20}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 21}, {"localized_name": "upscale_method", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "keep_proportion", "name": "keep_proportion", "type": "BOOLEAN", "widget": {"name": "keep_proportion"}, "link": null}, {"localized_name": "divisible_by", "name": "divisible_by", "type": "INT", "widget": {"name": "divisible_by"}, "link": null}, {"localized_name": "crop", "name": "crop", "shape": 7, "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [22, 26]}, {"label": "width", "localized_name": "width", "name": "width", "type": "INT", "links": [27]}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "links": [28]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.0", "Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "lanc<PERSON>s", true, 16, "disabled"]}, {"id": 22, "type": "INTConstant", "pos": [-234.82101440429688, 6380.04833984375], "size": [210, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "value", "localized_name": "value", "name": "value", "type": "INT", "links": [20]}], "title": "宽", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "Node name for S&R": "INTConstant"}, "widgets_values": [1280], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 23, "type": "INTConstant", "pos": [-254.0105438232422, 6488.865234375], "size": [210, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "value", "localized_name": "value", "name": "value", "type": "INT", "links": [21]}], "title": "高", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "Node name for S&R": "INTConstant"}, "widgets_values": [1280], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 24, "type": "INTConstant", "pos": [-306.01654052734375, 5414.3291015625], "size": [210, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "INT", "widget": {"name": "value"}, "link": null}], "outputs": [{"label": "value", "localized_name": "value", "name": "value", "type": "INT", "links": [29]}], "title": "长", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "Node name for S&R": "INTConstant"}, "widgets_values": [61], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 7, "type": "WanVideoModelLoader", "pos": [69.9875717163086, 5237.57275390625], "size": [477.4410095214844, 254], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "compile_args", "localized_name": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS", "link": null}, {"label": "block_swap_args", "localized_name": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": null}, {"label": "lora", "localized_name": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 5}, {"label": "vram_management_args", "localized_name": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS", "link": null}, {"label": "vace_model", "localized_name": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH", "link": 6}, {"label": "fantasytalking_model", "localized_name": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "base_precision", "name": "base_precision", "type": "COMBO", "widget": {"name": "base_precision"}, "link": null}, {"localized_name": "quantization", "name": "quantization", "type": "COMBO", "widget": {"name": "quantization"}, "link": null}, {"localized_name": "load_device", "name": "load_device", "type": "COMBO", "widget": {"name": "load_device"}, "link": null}, {"localized_name": "attention_mode", "name": "attention_mode", "shape": 7, "type": "COMBO", "widget": {"name": "attention_mode"}, "link": null}], "outputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [8, 12]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoModelLoader"}, "widgets_values": ["SKYREEL\\Wan2_1-SkyReels-V2-DF-14B-720P_fp8_e4m3fn.safetensors", "bf16", "fp8_e4m3fn", "offload_device", "sageattn"], "color": "#223", "bgcolor": "#335"}, {"id": 17, "type": "WanVideoSampler", "pos": [1023.427978515625, 5807.98779296875], "size": [312.8956298828125, 804.8956298828125], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 12}, {"label": "text_embeds", "localized_name": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 13}, {"label": "image_embeds", "localized_name": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 14}, {"label": "samples", "localized_name": "samples", "name": "samples", "shape": 7, "type": "LATENT", "link": null}, {"label": "feta_args", "localized_name": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS", "link": 15}, {"label": "context_options", "localized_name": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT", "link": null}, {"label": "teacache_args", "localized_name": "teacache_args", "name": "teacache_args", "shape": 7, "type": "TEACACHEARGS", "link": 16}, {"label": "flowedit_args", "localized_name": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS", "link": null}, {"label": "slg_args", "localized_name": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS", "link": 17}, {"label": "loop_args", "localized_name": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS", "link": null}, {"label": "experimental_args", "localized_name": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS", "link": 18}, {"label": "sigmas", "localized_name": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS", "link": null}, {"label": "unianimate_poses", "localized_name": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE", "link": null}, {"label": "fantasytalking_embeds", "localized_name": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS", "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "shift", "name": "shift", "type": "FLOAT", "widget": {"name": "shift"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "force_offload", "name": "force_offload", "type": "BOOLEAN", "widget": {"name": "force_offload"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "riflex_freq_index", "name": "riflex_freq_index", "type": "INT", "widget": {"name": "riflex_freq_index"}, "link": null}, {"localized_name": "denoise_strength", "name": "denoise_strength", "shape": 7, "type": "FLOAT", "widget": {"name": "denoise_strength"}, "link": null}, {"localized_name": "batched_cfg", "name": "batched_cfg", "shape": 7, "type": "BOOLEAN", "widget": {"name": "batched_cfg"}, "link": null}, {"localized_name": "rope_function", "name": "rope_function", "shape": 7, "type": "COMBO", "widget": {"name": "rope_function"}, "link": null}], "outputs": [{"label": "samples", "localized_name": "samples", "name": "samples", "type": "LATENT", "links": [2]}], "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "Node name for S&R": "WanVideoSampler"}, "widgets_values": [3, 1.0000000000000002, 8.000000000000002, 80113604551089, "randomize", true, "unipc", 0, 1, false, "comfy"]}], "links": [[1, 9, 0, 4, 0, "WANVAE"], [2, 17, 0, 4, 1, "LATENT"], [3, 13, 0, 6, 0, "*"], [5, 11, 0, 7, 2, "WANVIDLORA"], [6, 16, 0, 7, 4, "VACEPATH"], [7, 10, 0, 8, 0, "WANTEXTENCODER"], [8, 7, 0, 8, 1, "WANVIDEOMODEL"], [9, 6, 0, 8, 2, "STRING"], [10, 12, 0, 13, 0, "IMAGE"], [11, 4, 0, 14, 0, "IMAGE"], [12, 7, 0, 17, 0, "WANVIDEOMODEL"], [13, 8, 0, 17, 1, "WANVIDEOTEXTEMBEDS"], [14, 25, 0, 17, 2, "WANVIDIMAGE_EMBEDS"], [15, 5, 0, 17, 4, "FETAARGS"], [16, 15, 0, 17, 6, "TEACACHEARGS"], [17, 1, 0, 17, 8, "SLGARGS"], [18, 2, 0, 17, 10, "EXPERIMENTALARGS"], [20, 22, 0, 18, 2, "INT"], [21, 23, 0, 18, 3, "INT"], [22, 18, 0, 19, 0, "IMAGE"], [24, 13, 0, 21, 0, "*"], [25, 9, 0, 25, 0, "WANVAE"], [26, 18, 0, 25, 2, "IMAGE"], [27, 18, 1, 25, 5, "INT"], [28, 18, 2, 25, 6, "INT"], [29, 24, 0, 25, 7, "INT"], [30, 12, 0, 18, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"frontendVersion": "1.18.10", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}