using System;
using System.Threading.Tasks;
using SaveDataService.Manage;
using Newtonsoft.Json;
using System.Net.Http;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI详细日志演示程序
    /// </summary>
    public static class ComfyUILogDemo
    {
        private static readonly HttpClient _httpClient = new HttpClient();

        /// <summary>
        /// 测试AddWorkflows方法
        /// </summary>
        public static void TestAddWorkflows()
        {
            Console.WriteLine("🧪 测试 AddWorkflows 方法");
            Console.WriteLine(new string('=', 50));

            try
            {
                SaveDataService.TestAddWorkflows.RunQuickTest();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }

            Console.WriteLine(new string('=', 50));
            Console.WriteLine("AddWorkflows 测试完成");
            Console.WriteLine();
        }

        /// <summary>
        /// 运行详细日志演示
        /// </summary>
        public static async Task RunLogDemo()
        {
            var logger = ComfyUILogger.Instance;
            var monitor = ComfyUIMonitor.Instance;
            var comfyUIManage = ComfyUIManage.Instance;

            Console.WriteLine("🎯 ComfyUI详细日志演示开始");
            Console.WriteLine("这个演示将展示ComfyUI工作流执行的每个环节的详细日志记录");
            Console.WriteLine();

            // 创建一个真实的工作流
            var workflowJson = CreateDemoWorkflow();
            var serverUrl = "http://127.0.0.1:8888";

            // 首先检查数据库中是否已有服务器和工作流
            var allServers = comfyUIManage.GetAllServers();
            var allWorkflows = comfyUIManage.GetAllWorkflows();

            string serverId, workflowId;

            if (allServers.Count > 0)
            {
                // 使用现有的第一个服务器，但更新其配置为真实的ComfyUI服务器
                var existingServer = allServers[0];
                serverId = existingServer.id;


                //这个代码是不需要的主要是为了测试准确才写死的
               // comfyUIManage.UpdateServer(serverId, "真实ComfyUI服务器", "127.0.0.1", 8888, 5, "用于真实ComfyUI测试的服务器");
                Console.WriteLine($"使用现有服务器: {serverId}");
            }
            else
            {
                // 创建新服务器
                serverId = comfyUIManage.AddServer("真实ComfyUI服务器", "127.0.0.1", 8888, 5, "用于真实ComfyUI测试的服务器");
                Console.WriteLine($"创建新服务器: {serverId}");
            }

            if (allWorkflows.Count > 0)
            {
                // 使用现有的第一个工作流，但更新其内容
                var existingWorkflow = allWorkflows[0];
                workflowId = existingWorkflow.id;




                ////这个代码是不需要的主要是为了测试准确才写死的
               // comfyUIManage.UpdateWorkflow(workflowId, "真实ComfyUI工作流", workflowJson, "system", "image_generation", "演示用的图像生成工作流");
                Console.WriteLine($"使用现有工作流: {workflowId}");
            }
            else
            {
                // 创建新工作流
                workflowId = comfyUIManage.AddWorkflow("真实ComfyUI工作流", workflowJson, "image_generation", "演示用的图像生成工作流", "system");
                Console.WriteLine($"创建新工作流: {workflowId}");
            }

            // 创建任务记录
            var taskId = comfyUIManage.CreateTask(workflowId, "真实ComfyUI测试任务", "system", 5);

            try
            {
                // 1. 记录工作流开始
                logger.LogWorkflowStart(taskId, workflowJson, serverId);
                comfyUIManage.UpdateTaskStatus(taskId, 1, 0); // 状态：运行中，进度：0%

                // 2. 提交工作流到真实的ComfyUI服务器
                var url = $"{serverUrl}/prompt";
                var requestData = new
                {
                    prompt = JsonConvert.DeserializeObject(workflowJson),
                    client_id = Guid.NewGuid().ToString()
                };

                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                logger.LogInfo("向ComfyUI服务器提交工作流...");

                var response = await _httpClient.PostAsync(url, content);
                var responseText = await response.Content.ReadAsStringAsync();

                // 3. 记录提交结果
                logger.LogWorkflowSubmission(taskId, response.IsSuccessStatusCode, responseText);

                if (response.IsSuccessStatusCode)
                {
                    // 4. 解析响应获取prompt_id
                    var responseObj = JsonConvert.DeserializeObject<dynamic>(responseText);
                    if (responseObj?.prompt_id != null)
                    {
                        var promptId = responseObj.prompt_id.ToString();
                        logger.LogInfo($"获得Prompt ID: {promptId}，开始详细监控...");
                        comfyUIManage.UpdateTaskStatus(taskId, 1, 10); // 状态：运行中，进度：10%

                        // 5. 启动详细监控
                        await monitor.StartMonitoring(taskId, promptId, serverUrl, workflowJson);

                        logger.LogInfo("监控已启动，工作流正在执行中...");
                        logger.LogInfo("请观察上面的详细执行日志！");
                        comfyUIManage.UpdateTaskStatus(taskId, 1, 50); // 状态：运行中，进度：50%

                        // 6. 等待一段时间让工作流完成
                        await Task.Delay(45000); // 等待45秒

                        // 7. 显示最终统计
                        var activeMonitors = monitor.GetActiveMonitorCount();
                        logger.LogInfo($"当前活动监控数量: {activeMonitors}");

                        // 8. 标记任务完成
                        comfyUIManage.UpdateTaskStatus(taskId, 2, 100); // 状态：已完成，进度：100%
                        logger.LogInfo("✅ 任务已标记为完成并保存到数据库");
                    }
                    else
                    {
                        logger.LogError("无法从响应中获取prompt_id");
                        comfyUIManage.UpdateTaskStatus(taskId, 3, 0, "", "", "无法从响应中获取prompt_id"); // 状态：失败
                    }
                }
                else
                {
                    logger.LogError($"工作流提交失败: {response.StatusCode}");
                    logger.LogError($"错误详情: {responseText}");
                    comfyUIManage.UpdateTaskStatus(taskId, 3, 0, "", "", $"工作流提交失败: {response.StatusCode}"); // 状态：失败
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"演示过程中发生错误: {ex.Message}");
                comfyUIManage.UpdateTaskStatus(taskId, 3, 0, "", "", $"演示过程中发生错误: {ex.Message}"); // 状态：失败
            }

            Console.WriteLine();
            Console.WriteLine("🏁 ComfyUI详细日志演示结束");
            Console.WriteLine();
            Console.WriteLine("📋 日志说明:");
            Console.WriteLine("🚀 = 工作流开始");
            Console.WriteLine("📤 = 工作流提交");
            Console.WriteLine("🔄 = 节点开始执行");
            Console.WriteLine("⏳ = 节点执行进度");
            Console.WriteLine("✅ = 节点执行完成");
            Console.WriteLine("❌ = 节点执行失败");
            Console.WriteLine("📋 = 队列状态");
            Console.WriteLine("📚 = 历史查询");
            Console.WriteLine("🎉 = 工作流完成");
            Console.WriteLine("ℹ️ = 一般信息");
            Console.WriteLine("⚠️ = 警告信息");
            Console.WriteLine("❌ = 错误信息");
        }

        /// <summary>
        /// 创建演示用的工作流
        /// </summary>
        private static string CreateDemoWorkflow()
        {
            var workflow = new Dictionary<string, object>
            {
                // 检查点加载器
                ["4"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CheckpointLoaderSimple",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["ckpt_name"] = "v1-5-pruned-emaonly-fp16.safetensors"
                    }
                },
                // 空白潜在图像
                ["5"] = new Dictionary<string, object>
                {
                    ["class_type"] = "EmptyLatentImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["width"] = 512,
                        ["height"] = 512,
                        ["batch_size"] = 1
                    }
                },
                // 正面提示词编码
                ["6"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["clip"] = new object[] { "4", 1 },
                        ["text"] = "a beautiful sunset over mountains, detailed landscape, high quality"
                    }
                },
                // 负面提示词编码
                ["7"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["clip"] = new object[] { "4", 1 },
                        ["text"] = "blurry, low quality, bad anatomy"
                    }
                },
                // KSampler采样器
                ["3"] = new Dictionary<string, object>
                {
                    ["class_type"] = "KSampler",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["model"] = new object[] { "4", 0 },
                        ["positive"] = new object[] { "6", 0 },
                        ["negative"] = new object[] { "7", 0 },
                        ["latent_image"] = new object[] { "5", 0 },
                        ["seed"] = 42,
                        ["steps"] = 20,
                        ["cfg"] = 7.0,
                        ["sampler_name"] = "euler",
                        ["scheduler"] = "normal",
                        ["denoise"] = 1.0
                    }
                },
                // VAE解码器
                ["8"] = new Dictionary<string, object>
                {
                    ["class_type"] = "VAEDecode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["samples"] = new object[] { "3", 0 },
                        ["vae"] = new object[] { "4", 2 }
                    }
                },
                // 保存图像
                ["9"] = new Dictionary<string, object>
                {
                    ["class_type"] = "SaveImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["images"] = new object[] { "8", 0 },
                        ["filename_prefix"] = "ComfyUI_LogDemo"
                    }
                }
            };

            return JsonConvert.SerializeObject(workflow, Formatting.Indented);
        }

        /// <summary>
        /// 运行模拟日志演示（不需要真实的ComfyUI服务器）
        /// </summary>
        public static async Task RunMockLogDemo()
        {
            var logger = ComfyUILogger.Instance;

            Console.WriteLine("🎯 ComfyUI模拟日志演示开始");
            Console.WriteLine("这个演示将模拟ComfyUI工作流执行的完整过程");
            Console.WriteLine();

            var taskId = Guid.NewGuid().ToString();
            var workflowJson = CreateDemoWorkflow();

            // 1. 工作流开始
            logger.LogWorkflowStart(taskId, workflowJson, "mock-server");

            // 2. 模拟提交成功
            var mockResponse = $"{{\"prompt_id\": \"{Guid.NewGuid()}\", \"number\": 1}}";
            logger.LogWorkflowSubmission(taskId, true, mockResponse);

            // 3. 模拟节点执行过程
            var nodes = new[]
            {
                ("4", "CheckpointLoaderSimple", "加载Stable Diffusion模型"),
                ("5", "EmptyLatentImage", "创建512x512空白潜在图像"),
                ("6", "CLIPTextEncode", "编码正面提示词"),
                ("7", "CLIPTextEncode", "编码负面提示词"),
                ("3", "KSampler", "执行扩散采样生成"),
                ("8", "VAEDecode", "VAE解码为最终图像"),
                ("9", "SaveImage", "保存生成的图像")
            };

            foreach (var (nodeId, nodeType, description) in nodes)
            {
                // 节点开始
                var inputs = new Newtonsoft.Json.Linq.JObject();
                inputs["description"] = description;
                logger.LogNodeStart(taskId, nodeId, nodeType, nodeType, inputs);

                // 模拟执行时间和进度
                for (int progress = 0; progress <= 100; progress += 25)
                {
                    await Task.Delay(500);
                    if (progress < 100)
                    {
                        logger.LogNodeProgress(taskId, nodeId, nodeType, progress, "执行中");
                    }
                }

                // 节点完成
                var outputs = new Newtonsoft.Json.Linq.JObject();
                outputs["result"] = $"{nodeType} 执行完成";
                if (nodeType == "SaveImage")
                {
                    outputs["filename"] = "ComfyUI_LogDemo_00001_.png";
                }
                logger.LogNodeComplete(taskId, nodeId, nodeType, outputs, TimeSpan.FromSeconds(2));
            }

            // 4. 工作流完成
            var outputFiles = new[] { "ComfyUI_LogDemo_00001_.png" };
            logger.LogWorkflowComplete(taskId, true, TimeSpan.FromSeconds(14), outputFiles.ToList());

            Console.WriteLine();
            Console.WriteLine("🏁 ComfyUI模拟日志演示结束");
        }
    }
}
