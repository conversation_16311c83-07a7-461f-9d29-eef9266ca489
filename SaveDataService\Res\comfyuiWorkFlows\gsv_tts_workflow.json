{"id": "c88e3638-0284-45ba-9b3d-afe401fd8209", "revision": 0, "last_node_id": 10, "last_link_id": 13, "nodes": [{"id": 6, "type": "ExperienceNode", "pos": [172.40733337402344, 555.0353393554688], "size": [315, 178], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "exp_name", "name": "exp_name", "type": "STRING", "widget": {"name": "exp_name"}, "link": null}, {"localized_name": "version", "name": "version", "type": "COMBO", "widget": {"name": "version"}, "link": null}, {"localized_name": "is_half", "name": "is_half", "type": "BOOLEAN", "widget": {"name": "is_half"}, "link": null}, {"localized_name": "if_redataset", "name": "if_redataset", "type": "BOOLEAN", "widget": {"name": "if_redataset"}, "link": null}, {"localized_name": "if_ft_sovits", "name": "if_ft_sovits", "type": "BOOLEAN", "widget": {"name": "if_ft_sovits"}, "link": null}, {"localized_name": "if_ft_gpt", "name": "if_ft_gpt", "type": "BOOLEAN", "widget": {"name": "if_ft_gpt"}, "link": null}], "outputs": [{"localized_name": "CONFIG", "name": "CONFIG", "type": "CONFIG", "links": [11]}], "properties": {"cnr_id": "GSTTS-ComfyUI", "ver": "da760a2b3ebd20120e3644c46a8d686ffd4ec9ba", "Node name for S&R": "ExperienceNode", "widget_ue_connectable": {}}, "widgets_values": ["a<PERSON><PERSON>", "v2", true, true, true, true]}, {"id": 4, "type": "LoadAudio", "pos": [153.5635986328125, -118.30266571044922], "size": [315, 136], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "音频", "name": "audio", "type": "COMBO", "widget": {"name": "audio"}, "link": null}, {"localized_name": "音频UI", "name": "audioUI", "type": "AUDIO_UI", "widget": {"name": "audioUI"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "AUDIOUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "音频", "name": "AUDIO", "type": "AUDIO", "links": [10]}], "title": "input-audio-参考人声音频", "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "LoadAudio", "widget_ue_connectable": {}}, "widgets_values": ["委屈-要不是溜出来的时候身上没带够钱，本小姐也不至于…….wav", null, null]}, {"id": 2, "type": "TextDictNode", "pos": [120.57051849365234, 64.7601089477539], "size": [400, 200], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}, {"localized_name": "language", "name": "language", "type": "COMBO", "widget": {"name": "language"}, "link": null}], "outputs": [{"localized_name": "TEXTDICT", "name": "TEXTDICT", "type": "TEXTDICT", "links": [8]}], "title": "input-text-需要的台词", "properties": {"cnr_id": "GSTTS-ComfyUI", "ver": "da760a2b3ebd20120e3644c46a8d686ffd4ec9ba", "Node name for S&R": "TextDictNode", "widget_ue_connectable": {}}, "widgets_values": ["我是杨思纯，是兄弟就来砍我", "中文", [false, true]]}, {"id": 3, "type": "TextDictNode", "pos": [113.46512603759766, 308.2427062988281], "size": [400, 200], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}, {"localized_name": "language", "name": "language", "type": "COMBO", "widget": {"name": "language"}, "link": null}], "outputs": [{"localized_name": "TEXTDICT", "name": "TEXTDICT", "type": "TEXTDICT", "links": [9]}], "title": "input-text-还没测试的输入暂时不输入", "properties": {"cnr_id": "GSTTS-ComfyUI", "ver": "da760a2b3ebd20120e3644c46a8d686ffd4ec9ba", "Node name for S&R": "TextDictNode", "widget_ue_connectable": {}}, "widgets_values": ["", "中文", [false, true]]}, {"id": 8, "type": "GSVTTSNode", "pos": [572.17529296875, -23.182174682617188], "size": [370.9635925292969, 546.4337158203125], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "text_dict", "name": "text_dict", "type": "TEXTDICT", "link": 8}, {"localized_name": "prompt_text_dict", "name": "prompt_text_dict", "type": "TEXTDICT", "link": 9}, {"localized_name": "prompt_audio", "name": "prompt_audio", "type": "AUDIO", "link": 10}, {"localized_name": "config", "name": "config", "type": "CONFIG", "link": 11}, {"localized_name": "GPT_weight", "name": "GPT_weight", "type": "COMBO", "widget": {"name": "GPT_weight"}, "link": null}, {"localized_name": "SoVITS_weight", "name": "SoVITS_weight", "type": "COMBO", "widget": {"name": "SoVITS_weight"}, "link": null}, {"localized_name": "how_to_cut", "name": "how_to_cut", "type": "COMBO", "widget": {"name": "how_to_cut"}, "link": null}, {"localized_name": "speed", "name": "speed", "type": "FLOAT", "widget": {"name": "speed"}, "link": null}, {"localized_name": "top_k", "name": "top_k", "type": "INT", "widget": {"name": "top_k"}, "link": null}, {"localized_name": "top_p", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "temperature", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}], "outputs": [{"localized_name": "音频", "name": "AUDIO", "type": "AUDIO", "slot_index": 0, "links": [12]}], "properties": {"cnr_id": "GSTTS-ComfyUI", "ver": "da760a2b3ebd20120e3644c46a8d686ffd4ec9ba", "Node name for S&R": "GSVTTSNode", "widget_ue_connectable": {}}, "widgets_values": ["gsv-v2final-pretrained/s1bert25hz-5kh-longer-epoch=12-step=369668.ckpt", "gsv-v2final-pretrained/s2G2333k.pth", "凑四句一切", 0.9800000000000002, 15, 1, 1]}, {"id": 9, "type": "SaveAudio", "pos": [1038.8682861328125, -15.351277351379395], "size": [270, 112], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "音频", "name": "audio", "type": "AUDIO", "link": 12}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 13}, {"localized_name": "音频UI", "name": "audioUI", "type": "AUDIO_UI", "widget": {"name": "audioUI"}, "link": null}], "outputs": [], "title": "output-audio-生成的音频", "properties": {"widget_ue_connectable": {}, "cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "SaveAudio"}, "widgets_values": ["audio/ComfyUI"]}, {"id": 10, "type": "CR Text", "pos": [554.4547729492188, -209.49371337890625], "size": [396.2302551269531, 113], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [13]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "title": "input-text-文件前缀", "properties": {"widget_ue_connectable": {}, "cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["audio/comfy", [false, true]]}], "links": [[8, 2, 0, 8, 0, "TEXTDICT"], [9, 3, 0, 8, 1, "TEXTDICT"], [10, 4, 0, 8, 2, "AUDIO"], [11, 6, 0, 8, 3, "CONFIG"], [12, 8, 0, 9, 0, "AUDIO"], [13, 10, 0, 9, 1, "STRING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.0610764609500007, "offset": [657.5220135836112, 572.3328144283527]}, "ue_links": [], "links_added_by_ue": []}, "version": 0.4}