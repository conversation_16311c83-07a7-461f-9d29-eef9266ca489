{"id": "67697bae-7817-4584-a63a-fec837dfde80", "revision": 0, "last_node_id": 283, "last_link_id": 544, "nodes": [{"id": 236, "type": "Note", "pos": [-1264, 822], "size": [404.1505126953125, 88], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["插件链接---<PERSON>lors plugin \nhttps://github.com/MinusZoneAI/ComfyUI-Kolors-MZ"], "color": "#432", "bgcolor": "#653"}, {"id": 138, "type": "MZ_ChatGLM3Loader", "pos": [-839, 948], "size": [414.6513977050781, 77.22203826904297], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "chatglm3_checkpoint", "name": "chatglm3_checkpoint", "type": "COMBO", "widget": {"name": "chatglm3_checkpoint"}, "link": null}], "outputs": [{"label": "chatglm3_model", "localized_name": "chatglm3_model", "name": "chatglm3_model", "type": "CHATGLM3MODEL", "slot_index": 0, "links": [294, 423]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_ChatGLM3Loader"}, "widgets_values": ["checkpoints\\chatglm3-fp16.safetensors"]}, {"id": 238, "type": "Note", "pos": [-1260, 1074], "size": [414.33770751953125, 88], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["插件链接---plugin\nhttps://github.com/city96/SD-Latent-Upscaler"], "color": "#432", "bgcolor": "#653"}, {"id": 237, "type": "Note", "pos": [-1266, 934], "size": [414.33770751953125, 88], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["插件链接---plugin\nhttps://github.com/jags111/ComfyUI_Jags_VectorMagic"], "color": "#432", "bgcolor": "#653"}, {"id": 252, "type": "SamplerCustom", "pos": [54.16666030883789, 559.7890014648438], "size": [355.20001220703125, 442], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 490}, {"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 484}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 485}, {"label": "sampler", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 489}, {"label": "sigmas", "localized_name": "Sigmas", "name": "sigmas", "type": "SIGMAS", "link": 488}, {"label": "latent_image", "localized_name": "Latent", "name": "latent_image", "type": "LATENT", "link": 486}, {"localized_name": "添加噪波", "name": "add_noise", "type": "BOOLEAN", "widget": {"name": "add_noise"}, "link": null}, {"localized_name": "噪波种子", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"label": "output", "localized_name": "Latent", "name": "output", "type": "LATENT", "slot_index": 0, "links": [487]}, {"label": "denoised_output", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 762738612106638, "randomize", 4]}, {"id": 254, "type": "KSamplerSelect", "pos": [61.11725616455078, 1091.44384765625], "size": [270, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [489]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["ipndm"]}, {"id": 253, "type": "AlignYourStepsScheduler", "pos": [-1344.7708740234375, 578.9161987304688], "size": [315, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "模型类型", "name": "model_type", "type": "COMBO", "widget": {"name": "model_type"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "SIGMAS", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "slot_index": 0, "links": [488]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "AlignYourStepsScheduler"}, "widgets_values": ["SDXL", 25, 1]}, {"id": 142, "type": "MZ_KolorsUNETLoaderV2", "pos": [-845, 804], "size": [406.3903503417969, 70.29098510742188], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}], "outputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "slot_index": 0, "links": [470, 491]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_KolorsUNETLoaderV2"}, "widgets_values": ["diffusion_pytorch_model.fp16.safetensors"]}, {"id": 248, "type": "CR SDXL Aspect Ratio", "pos": [-960.312255859375, 431.0336608886719], "size": [315, 278], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "swap_dimensions", "name": "swap_dimensions", "type": "COMBO", "widget": {"name": "swap_dimensions"}, "link": null}, {"localized_name": "upscale_factor", "name": "upscale_factor", "type": "FLOAT", "widget": {"name": "upscale_factor"}, "link": null}, {"localized_name": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"label": "width", "localized_name": "width", "name": "width", "type": "INT", "slot_index": 0, "links": []}, {"label": "height", "localized_name": "height", "name": "height", "type": "INT", "slot_index": 1, "links": []}, {"label": "upscale_factor", "localized_name": "upscale_factor", "name": "upscale_factor", "type": "FLOAT", "links": null}, {"label": "batch_size", "localized_name": "batch_size", "name": "batch_size", "type": "INT", "links": []}, {"label": "empty_latent", "localized_name": "empty_latent", "name": "empty_latent", "type": "LATENT", "slot_index": 4, "links": [486]}, {"label": "show_help", "localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR SDXL Aspect Ratio"}, "widgets_values": [1024, 1024, "4:3 landscape 1152x896", "Off", 1, 1]}, {"id": 257, "type": "Automatic CFG - Preset Loader", "pos": [-556.6266479492188, 482.3846130371094], "size": [544.199951171875, 170], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "link": 491}, {"label": "join_global_parameters", "localized_name": "join_global_parameters", "name": "join_global_parameters", "shape": 7, "type": "ATTNMOD", "link": null}, {"localized_name": "preset", "name": "preset", "type": "COMBO", "widget": {"name": "preset"}, "link": null}, {"localized_name": "uncond_sigma_end", "name": "uncond_sigma_end", "type": "FLOAT", "widget": {"name": "uncond_sigma_end"}, "link": null}, {"localized_name": "use_uncond_sigma_end_from_preset", "name": "use_uncond_sigma_end_from_preset", "type": "BOOLEAN", "widget": {"name": "use_uncond_sigma_end_from_preset"}, "link": null}, {"localized_name": "automatic_cfg", "name": "automatic_cfg", "type": "COMBO", "widget": {"name": "automatic_cfg"}, "link": null}], "outputs": [{"label": "Model", "localized_name": "Model", "name": "Model", "type": "MODEL", "slot_index": 0, "links": [490]}, {"label": "Preset name", "localized_name": "Preset name", "name": "Preset name", "type": "STRING", "links": null}, {"label": "Parameters as string", "localized_name": "Parameters as string", "name": "Parameters as string", "type": "STRING", "links": null}], "properties": {"cnr_id": "comfyui-automaticcfg", "ver": "1.0.0", "Node name for S&R": "Automatic CFG - Preset Loader"}, "widgets_values": ["For magic", 0, true, "From preset"]}, {"id": 259, "type": "KSampler (Efficient)", "pos": [1274.08447265625, 452.8616943359375], "size": [312.5668640136719, 615.245849609375], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "模型", "localized_name": "model", "name": "model", "type": "MODEL", "link": 492}, {"label": "正面条件", "localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 517}, {"label": "负面条件", "localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 515}, {"label": "Latent", "localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 495}, {"label": "VAE(可选)", "localized_name": "optional_vae", "name": "optional_vae", "shape": 7, "type": "VAE", "link": null}, {"label": "脚本", "localized_name": "script", "name": "script", "shape": 7, "type": "SCRIPT", "link": 496}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}, {"localized_name": "preview_method", "name": "preview_method", "type": "COMBO", "widget": {"name": "preview_method"}, "link": null}, {"localized_name": "vae_decode", "name": "vae_decode", "type": "COMBO", "widget": {"name": "vae_decode"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": null}, {"label": "正面条件", "localized_name": "CONDITIONING+", "name": "CONDITIONING+", "type": "CONDITIONING", "links": null}, {"label": "负面条件", "localized_name": "CONDITIONING-", "name": "CONDITIONING-", "type": "CONDITIONING", "links": null}, {"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 3, "links": [505]}, {"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 4, "links": []}, {"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 5, "links": []}], "properties": {"cnr_id": "efficiency-nodes-comfyui", "ver": "1.0.6", "Node name for S&R": "KSampler (Efficient)"}, "widgets_values": [413139236402374, null, 40, 5, "dpmpp_2m", "karras", 0.65, "auto", "true"], "color": "#222233", "bgcolor": "#333355", "shape": 1}, {"id": 266, "type": "VAEDecodeTiled", "pos": [1611.089111328125, 559.1851196289062], "size": [315, 150], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 505}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 528}, {"localized_name": "分块尺寸", "name": "tile_size", "type": "INT", "widget": {"name": "tile_size"}, "link": null}, {"localized_name": "重叠", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "时间尺寸", "name": "temporal_size", "type": "INT", "widget": {"name": "temporal_size"}, "link": null}, {"localized_name": "时间重叠", "name": "temporal_overlap", "type": "INT", "widget": {"name": "temporal_overlap"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [507]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecodeTiled"}, "widgets_values": [512, 64, 64, 8]}, {"id": 270, "type": "GetImageSize+", "pos": [2003.173583984375, 390.2041931152344], "size": [159.50155639648438, 66], "flags": {}, "order": 34, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 508}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": [503]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [504]}, {"localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}}, {"id": 246, "type": "VAEDecode", "pos": [126.74182891845703, 449.9585266113281], "size": [210, 46], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "samples", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 487}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 471}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [473, 526, 529]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 267, "type": "UpscaleModelLoader", "pos": [1596.609130859375, 440.88177490234375], "size": [315, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "模型名称", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"label": "放大模型", "localized_name": "放大模型", "name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "slot_index": 0, "links": [506]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UpscaleModelLoader"}, "widgets_values": ["4x_NMKD-Siax_200k.pth"]}, {"id": 271, "type": "VAEEncodeTiled", "pos": [899.9364013671875, 556.5197143554688], "size": [315, 150], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "图像", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 526}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 527}, {"localized_name": "分块尺寸", "name": "tile_size", "type": "INT", "widget": {"name": "tile_size"}, "link": null}, {"localized_name": "重叠", "name": "overlap", "type": "INT", "widget": {"name": "overlap"}, "link": null}, {"localized_name": "时间尺寸", "name": "temporal_size", "type": "INT", "widget": {"name": "temporal_size"}, "link": null}, {"localized_name": "时间重叠", "name": "temporal_overlap", "type": "INT", "widget": {"name": "temporal_overlap"}, "link": null}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [495]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEEncodeTiled"}, "widgets_values": [512, 64, 64, 8]}, {"id": 265, "type": "ImageScale", "pos": [1965.77734375, 494.7701721191406], "size": [315, 130], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": null}, {"localized_name": "缩放算法", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"label": "宽度", "localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 503}, {"label": "高度", "localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 504}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 512, 512, "disabled"]}, {"id": 264, "type": "PreviewImage", "pos": [1697.54052734375, 759.0928344726562], "size": [616.8348999023438, 520.336669921875], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 502}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 263, "type": "ControlNetApply", "pos": [1455.1710205078125, 1109.75732421875], "size": [210, 139.81285095214844], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 516}, {"label": "ControlNet", "localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 501}, {"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 529}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [517]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApply"}, "widgets_values": [0.6]}, {"id": 268, "type": "ControlNetLoader", "pos": [1191.463134765625, 1150.8861083984375], "size": [210, 85.72789764404297], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"label": "ControlNet", "localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "links": [501]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["XL\\diffusion_pytorch_model_promax.safetensors"]}, {"id": 249, "type": "SaveImage", "pos": [455.3194274902344, 406.4166259765625], "size": [318.4622497558594, 774.0809326171875], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "images", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 473}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 223, "type": "MZ_ChatGLM3_V2", "pos": [-386.99041748046875, 741.3886108398438], "size": [323.1945495605469, 151.08518981933594], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "chatglm3_model", "localized_name": "chatglm3_model", "name": "chatglm3_model", "type": "CHATGLM3MODEL", "link": 423}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 540}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [425, 484]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_ChatGLM3_V2"}, "widgets_values": ["一个穿蕾丝连衣裙的中国美女，彩色的长发，大眼睛，精致的妆容，跪在佛像下虔诚的祈祷"]}, {"id": 144, "type": "MZ_ChatGLM3_V2", "pos": [-374.3522644042969, 947.9832153320312], "size": [341.9886779785156, 105.83680725097656], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "chatglm3_model", "localized_name": "chatglm3_model", "name": "chatglm3_model", "type": "CHATGLM3MODEL", "link": 294}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 539}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [426, 485]}], "properties": {"cnr_id": "comfyui-kolors-mz", "ver": "43ec2701a1390259a17ef3bea6244a3134aa5153", "Node name for S&R": "MZ_ChatGLM3_V2"}, "widgets_values": [""]}, {"id": 275, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [931.5118408203125, 1228.0615234375], "size": [400, 200], "flags": {"collapsed": true}, "order": 24, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 512}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 542}, {"localized_name": "token_normalization", "name": "token_normalization", "type": "COMBO", "widget": {"name": "token_normalization"}, "link": null}, {"localized_name": "weight_interpretation", "name": "weight_interpretation", "type": "COMBO", "widget": {"name": "weight_interpretation"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": []}], "properties": {"cnr_id": "ComfyUI_ADV_CLIP_emb", "ver": "63984deefb005da1ba90a1175e21d91040da38ab", "Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "length+mean", "A1111"]}, {"id": 276, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [936.0826416015625, 1146.87109375], "size": [400, 200], "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 514}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 543}, {"localized_name": "token_normalization", "name": "token_normalization", "type": "COMBO", "widget": {"name": "token_normalization"}, "link": null}, {"localized_name": "weight_interpretation", "name": "weight_interpretation", "type": "COMBO", "widget": {"name": "weight_interpretation"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [515, 516]}], "properties": {"cnr_id": "ComfyUI_ADV_CLIP_emb", "ver": "63984deefb005da1ba90a1175e21d91040da38ab", "Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "length+mean", "A1111"]}, {"id": 262, "type": "CheckpointLoaderSimple", "pos": [918.74072265625, 908.4339599609375], "size": [241.93006896972656, 179.44290161132812], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "Checkpoint名称", "name": "ckpt_name", "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [498]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [499, 512, 514]}, {"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["XL\\juggernautXL_ragnarokBy.safetensors"]}, {"id": 261, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [882.2872924804688, 754.1140747070312], "size": [315, 126], "flags": {}, "order": 17, "mode": 4, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 498}, {"label": "CLIP", "localized_name": "CLIPCLIP", "name": "clip", "type": "CLIP", "link": 499}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}, {"localized_name": "CLIP强度", "name": "strength_clip", "type": "FLOAT", "widget": {"name": "strength_clip"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [492]}, {"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["add-detail-xl.safetensors", 0.75, 1]}, {"id": 273, "type": "Noise Control Script", "pos": [885.1207275390625, 377.0473937988281], "size": [325, 154], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "脚本", "localized_name": "script", "name": "script", "shape": 7, "type": "SCRIPT", "link": null}, {"localized_name": "rng_source", "name": "rng_source", "type": "COMBO", "widget": {"name": "rng_source"}, "link": null}, {"localized_name": "cfg_denoiser", "name": "cfg_denoiser", "type": "BOOLEAN", "widget": {"name": "cfg_denoiser"}, "link": null}, {"localized_name": "add_seed_noise", "name": "add_seed_noise", "type": "BOOLEAN", "widget": {"name": "add_seed_noise"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "weight", "name": "weight", "type": "FLOAT", "widget": {"name": "weight"}, "link": null}], "outputs": [{"label": "脚本", "localized_name": "SCRIPT", "name": "SCRIPT", "type": "SCRIPT", "slot_index": 0, "links": [496]}], "properties": {"cnr_id": "efficiency-nodes-comfyui", "ver": "1.0.6", "Node name for S&R": "Noise Control Script"}, "widgets_values": ["gpu", false, false, 294711390606250, null, 0.015], "shape": 1}, {"id": 269, "type": "ImageUpscaleWithModel", "pos": [2001.9033203125, 658.9649658203125], "size": [241.79998779296875, 46], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "放大模型", "localized_name": "放大模型", "name": "upscale_model", "type": "UPSCALE_MODEL", "link": 506}, {"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 507}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [502, 508]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageUpscaleWithModel"}}, {"id": 279, "type": "OllamaGenerate", "pos": [-1332.1807861328125, 1448.9202880859375], "size": [400, 208], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "debug", "name": "debug", "type": "COMBO", "widget": {"name": "debug"}, "link": null}, {"localized_name": "url", "name": "url", "type": "STRING", "widget": {"name": "url"}, "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "keep_alive", "name": "keep_alive", "type": "INT", "widget": {"name": "keep_alive"}, "link": null}, {"localized_name": "format", "name": "format", "type": "COMBO", "widget": {"name": "format"}, "link": null}], "outputs": [{"localized_name": "response", "name": "response", "type": "STRING", "links": [532]}], "properties": {"cnr_id": "comfyui-o<PERSON><PERSON>", "ver": "2.0.3", "Node name for S&R": "OllamaGenerate"}, "widgets_values": ["我要画一个美丽的中国女孩在纯洁的图片，只输出英文提示词", "enable", "http://127.0.0.1:11434", "qwen2.5-coder:32b", 5, "text"]}, {"id": 278, "type": "easy stylesSelector", "pos": [-393.4047546386719, 1422.5618896484375], "size": [425, 500], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "正面提示词（可选）", "name": "positive", "shape": 7, "type": "STRING", "link": 536}, {"localized_name": "负面提示词（可选）", "name": "negative", "shape": 7, "type": "STRING", "link": 544}, {"localized_name": "风格类型", "name": "styles", "type": "COMBO", "widget": {"name": "styles"}, "link": null}], "outputs": [{"localized_name": "正面提示词", "name": "positive", "type": "STRING", "links": [534]}, {"localized_name": "负面提示词", "name": "negative", "type": "STRING", "links": [539, 543]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy stylesSelector", "values": ["<PERSON><PERSON><PERSON><PERSON>", "Fooocus Masterpiece"]}, "widgets_values": ["fooocus_styles", "<PERSON><PERSON><PERSON><PERSON>,Fooocus Masterpiece"]}, {"id": 283, "type": "CR Text", "pos": [-837.7593383789062, 1661.261474609375], "size": [346.59515380859375, 108], "flags": {}, "order": 15, "mode": 4, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": []}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image，2d,2.5d"]}, {"id": 282, "type": "ShowText|pysssss", "pos": [170.7974395751953, 1416.7501220703125], "size": [368.0702819824219, 298.7486267089844], "flags": {}, "order": 20, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 534}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [540, 542]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["(masterpiece), (best quality), (ultra-detailed), A beautiful Chinese girl in a pure and innocent setting, traditional clothing, soft lighting, gentle expression, serene background, floral elements, subtle makeup, long flowing hair, elegant posture, smiling gently., illustration, disheveled hair, detailed eyes, perfect composition, moist skin, intricate details, earrings, by wlop"]}, {"id": 274, "type": "CR Text", "pos": [-373.6786804199219, 1116.0802001953125], "size": [346.59515380859375, 108], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [544]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["Blurred, messy, turbid, lowest resolution, worst quality, least details, extremely poor image quality, distorted structure, messy structure, broken image，2d,2.5d"]}, {"id": 141, "type": "VAELoader", "pos": [-854.2865600585938, 1111.923828125], "size": [425.97418212890625, 59.07209777832031], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [471, 527, 528]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["sdxl_vae.safetensors"]}, {"id": 281, "type": "ShowText|pysssss", "pos": [-876.9307250976562, 1409.401123046875], "size": [445.49322509765625, 188.9615936279297], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 532}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [536]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["A beautiful Chinese girl in a pure and innocent setting, traditional clothing, soft lighting, gentle expression, serene background, floral elements, subtle makeup, long flowing hair, elegant posture, smiling gently."]}], "links": [[294, 138, 0, 144, 0, "CHATGLM3MODEL"], [423, 138, 0, 223, 0, "CHATGLM3MODEL"], [471, 141, 0, 246, 1, "VAE"], [473, 246, 0, 249, 0, "IMAGE"], [484, 223, 0, 252, 1, "CONDITIONING"], [485, 144, 0, 252, 2, "CONDITIONING"], [486, 248, 4, 252, 5, "LATENT"], [487, 252, 0, 246, 0, "LATENT"], [488, 253, 0, 252, 4, "SIGMAS"], [489, 254, 0, 252, 3, "SAMPLER"], [490, 257, 0, 252, 0, "MODEL"], [491, 142, 0, 257, 0, "MODEL"], [492, 261, 0, 259, 0, "MODEL"], [495, 271, 0, 259, 3, "LATENT"], [496, 273, 0, 259, 5, "SCRIPT"], [498, 262, 0, 261, 0, "MODEL"], [499, 262, 1, 261, 1, "CLIP"], [501, 268, 0, 263, 1, "CONTROL_NET"], [502, 269, 0, 264, 0, "IMAGE"], [503, 270, 0, 265, 2, "INT"], [504, 270, 1, 265, 3, "INT"], [505, 259, 3, 266, 0, "LATENT"], [506, 267, 0, 269, 0, "UPSCALE_MODEL"], [507, 266, 0, 269, 1, "IMAGE"], [508, 269, 0, 270, 0, "IMAGE"], [512, 262, 1, 275, 0, "CLIP"], [514, 262, 1, 276, 0, "CLIP"], [515, 276, 0, 259, 2, "CONDITIONING"], [516, 276, 0, 263, 0, "CONDITIONING"], [517, 263, 0, 259, 1, "CONDITIONING"], [526, 246, 0, 271, 0, "IMAGE"], [527, 141, 0, 271, 1, "VAE"], [528, 141, 0, 266, 1, "VAE"], [529, 246, 0, 263, 2, "IMAGE"], [532, 279, 0, 281, 0, "STRING"], [534, 278, 0, 282, 0, "STRING"], [536, 281, 0, 278, 0, "STRING"], [539, 278, 1, 144, 1, "STRING"], [540, 282, 0, 223, 1, "STRING"], [542, 282, 0, 275, 1, "STRING"], [543, 278, 1, 276, 1, "STRING"], [544, 274, 0, 278, 1, "STRING"]], "groups": [{"id": 1, "title": "放大部分", "bounding": [842.0840454101562, 350.8615417480469, 1464, 959], "color": "#8AA", "font_size": 24, "flags": {}}, {"id": 2, "title": "Group", "bounding": [-1342.1807861328125, 1338.8369140625, 1891.048583984375, 593.7249755859375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.0610764609500007, "offset": [1432.5255690570164, 11.78103189388817]}, "frontendVersion": "1.18.9", "0246.VERSION": [0, 0, 4], "workspace_info": {"id": "28VoLYCWKmL5MZJVLfXmD", "saveLock": false, "cloudID": null, "coverMediaPath": null}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}