using System;
using System.Linq;
using ExcelToData;

namespace SaveDataService
{
    /// <summary>
    /// 数据库验证工具
    /// </summary>
    public class DatabaseVerification
    {
        /// <summary>
        /// 验证数据库连接和数据
        /// </summary>
        public static void VerifyDatabase()
        {
            Console.WriteLine("=== 数据库验证工具 ===");
            
            try
            {
                var db = ORMTables.Instance;
                Console.WriteLine("✅ 数据库连接成功");
                
                // 检查服务器表
                var servers = db.ComfyUIServers.ToList();
                Console.WriteLine($"📊 ComfyUIServers 表: {servers.Count} 条记录");
                foreach (var server in servers)
                {
                    Console.WriteLine($"   - ID: {server.id}, 名称: {server.serverName}, 地址: {server.serverUrl}:{server.port}");
                }
                
                // 检查工作流表
                var workflows = db.ComfyUIWorkflows.ToList();
                Console.WriteLine($"📊 ComfyUIWorkflows 表: {workflows.Count} 条记录");
                foreach (var workflow in workflows)
                {
                    Console.WriteLine($"   - ID: {workflow.id}, 名称: {workflow.workflowName}, 类型: {workflow.workflowType}");
                }
                
                // 检查任务表
                var tasks = db.ComfyUITasks.ToList();
                Console.WriteLine($"📊 ComfyUITasks 表: {tasks.Count} 条记录");
                foreach (var task in tasks)
                {
                    Console.WriteLine($"   - ID: {task.id}, 名称: {task.taskName}, 状态: {task.status}, 进度: {task.progress}%");
                }
                
                // 注意：日志和文件信息存储在 ComfyUITask 表的 Logs 和 Files JSON 字段中
                Console.WriteLine($"📊 任务日志和文件: 存储在 ComfyUITask 表的 JSON 字段中");
                
                Console.WriteLine("=== 验证完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 数据库验证失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 清空所有测试数据
        /// </summary>
        public static void ClearAllTestData()
        {
            Console.WriteLine("=== 清空测试数据 ===");
            
            try
            {
                var db = ORMTables.Instance;
                
                // 清空所有表（只清空已定义的表）
                db.ComfyUITasks.RemoveRange(db.ComfyUITasks);
                db.ComfyUIWorkflows.RemoveRange(db.ComfyUIWorkflows);
                db.ComfyUIServers.RemoveRange(db.ComfyUIServers);
                
                db.SaveChanges();
                
                Console.WriteLine("✅ 所有测试数据已清空");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 清空数据失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 添加测试数据
        /// </summary>
        public static void AddTestData()
        {
            Console.WriteLine("=== 添加测试数据 ===");
            
            try
            {
                var comfyUIManage = Manage.ComfyUIManage.Instance;
                
                // 添加测试服务器
                var serverId = comfyUIManage.AddServer(
                    "测试服务器",
                    "127.0.0.1",
                    8188,
                    5,
                    "数据库验证测试服务器"
                );
                
                if (!string.IsNullOrEmpty(serverId))
                {
                    Console.WriteLine($"✅ 成功添加测试服务器: {serverId}");
                    
                    // 立即验证是否保存成功
                    var server = comfyUIManage.GetServerById(serverId);
                    if (server != null)
                    {
                        Console.WriteLine($"✅ 服务器保存验证成功: {server.serverName}");
                    }
                    else
                    {
                        Console.WriteLine($"❌ 服务器保存验证失败: 无法找到服务器 {serverId}");
                    }
                }
                else
                {
                    Console.WriteLine("❌ 添加测试服务器失败");
                }
                
                // 添加测试工作流
                var workflowJson = @"{
                    ""1"": {
                        ""inputs"": {
                            ""text"": ""测试文本""
                        },
                        ""class_type"": ""CLIPTextEncode""
                    }
                }";
                
                var workflowId = comfyUIManage.AddWorkflow(
                    "测试工作流",
                    workflowJson,
                    "test",
                    "数据库验证测试工作流",
                    "系统测试"
                );
                
                if (!string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine($"✅ 成功添加测试工作流: {workflowId}");
                    
                    // 立即验证是否保存成功
                    var workflow = comfyUIManage.GetWorkflowById(workflowId);
                    if (workflow != null)
                    {
                        Console.WriteLine($"✅ 工作流保存验证成功: {workflow.workflowName}");
                    }
                    else
                    {
                        Console.WriteLine($"❌ 工作流保存验证失败: 无法找到工作流 {workflowId}");
                    }
                    
                    // 添加测试任务
                    var taskId = comfyUIManage.CreateTask(
                        workflowId,
                        "测试任务",
                        "测试用户",
                        5
                    );
                    
                    if (!string.IsNullOrEmpty(taskId))
                    {
                        Console.WriteLine($"✅ 成功添加测试任务: {taskId}");
                        
                        // 立即验证是否保存成功
                        var task = comfyUIManage.GetTaskById(taskId);
                        if (task != null)
                        {
                            Console.WriteLine($"✅ 任务保存验证成功: {task.taskName}");
                        }
                        else
                        {
                            Console.WriteLine($"❌ 任务保存验证失败: 无法找到任务 {taskId}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("❌ 添加测试任务失败");
                    }
                }
                else
                {
                    Console.WriteLine("❌ 添加测试工作流失败");
                }
                
                Console.WriteLine("=== 测试数据添加完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 添加测试数据失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }
    }
}
