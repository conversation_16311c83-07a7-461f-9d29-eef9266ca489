# AccountManage 账号管理类

## 概述

`AccountManage` 是一个基于 EF Core 实体类 `Account` 的账号管理类，提供了完整的用户注册、登录、忘记密码等账号相关功能。该类使用 `ORMTables.Instance` 单例模式进行数据库操作。

## 主要功能

### 1. 注册功能
- **用户名注册**: `RegisterByUsername(username, password, email?, mobile?)`
- **邮箱注册**: `RegisterByEmail(email, password, username?)`
- **手机号注册**: `RegisterByMobile(mobile, password, username?)`

### 2. 登录功能
- **用户名登录**: `LoginByUsername(username, password, clientIp?)`
- **邮箱登录**: `LoginByEmail(email, password, clientIp?)`
- **手机号登录**: `LoginByMobile(mobile, password, clientIp?)`
- **令牌登录**: `LoginByToken(accessToken)`

### 3. 验证码功能
- **发送邮箱验证码**: `SendEmailVerificationCode(email)`
- **发送手机验证码**: `SendMobileVerificationCode(mobile)`

### 4. 忘记密码功能
- **邮箱验证码重置密码**: `ResetPasswordByEmail(email, verificationCode, newPassword)`
- **手机验证码重置密码**: `ResetPasswordByMobile(mobile, verificationCode, newPassword)`
- **获取安全问题**: `GetSecurityQuestions(username)`
- **安全问题重置密码**: `ResetPasswordBySecurityQuestion(username, securityAnswers, newPassword)`

### 4. 账号管理功能
- **修改密码**: `ChangePassword(accountId, oldPassword, newPassword)`
- **设置安全问题**: `SetSecurityQuestions(accountId, securityQuestions, securityAnswers)`
- **启用/禁用二次验证**: `SetTwoFactorAuth(accountId, enable)`
- **更新账号信息**: `UpdateAccountInfo(accountId, nickname?, avatar?, realName?)`

## 使用示例

### 注册新用户

```csharp
// 用户名注册
var result = AccountManage.RegisterByUsername("testuser", "password123", "<EMAIL>", "***********");
if (result.Success)
{
    Console.WriteLine($"注册成功，账号ID: {result.AccountId}");
    Console.WriteLine($"访问令牌: {result.AccessToken}");
}
else
{
    Console.WriteLine($"注册失败: {result.Message}");
}

// 邮箱注册
var emailResult = AccountManage.RegisterByEmail("<EMAIL>", "password123");

// 手机号注册
var mobileResult = AccountManage.RegisterByMobile("***********", "password123");
```

### 用户登录

```csharp
// 用户名登录
var loginResult = AccountManage.LoginByUsername("testuser", "password123", "192.168.1.100");
if (loginResult.Success)
{
    Console.WriteLine($"登录成功，用户: {loginResult.Account.usrname}");
    Console.WriteLine($"访问令牌: {loginResult.AccessToken}");
}

// 邮箱登录
var emailLogin = AccountManage.LoginByEmail("<EMAIL>", "password123");

// 令牌登录
var tokenLogin = AccountManage.LoginByToken(accessToken);
```

### 发送验证码

```csharp
// 发送邮箱验证码
var emailCodeResult = AccountManage.SendEmailVerificationCode("<EMAIL>");
if (emailCodeResult.Success)
{
    Console.WriteLine("验证码已发送到邮箱");
}

// 发送手机验证码
var mobileCodeResult = AccountManage.SendMobileVerificationCode("***********");
if (mobileCodeResult.Success)
{
    Console.WriteLine("验证码已发送到手机");
}
```

### 重置密码

```csharp
// 通过邮箱验证码重置（需要先发送验证码）
var resetResult = AccountManage.ResetPasswordByEmail("<EMAIL>", "123456", "newpassword123");

// 通过手机验证码重置（需要先发送验证码）
var mobileReset = AccountManage.ResetPasswordByMobile("***********", "123456", "newpassword123");

// 获取安全问题
var questionsResult = AccountManage.GetSecurityQuestions("testuser");
if (questionsResult.Success && questionsResult.SecurityQuestions != null)
{
    foreach (var question in questionsResult.SecurityQuestions)
    {
        Console.WriteLine(question);
    }
}

// 通过安全问题重置
var securityAnswers = new string[] { "北京", "张三", "红色" };
var securityReset = AccountManage.ResetPasswordBySecurityQuestion("testuser", securityAnswers, "newpassword123");
```

### 账号管理

```csharp
// 修改密码
var changeResult = AccountManage.ChangePassword(accountId, "oldpassword", "newpassword");

// 设置安全问题
var questions = new string[] { "您出生的城市是？", "您的小学老师姓名？" };
var answers = new string[] { "北京", "张三" };
var securityResult = AccountManage.SetSecurityQuestions(accountId, questions, answers);

// 启用二次验证
var twoFactorResult = AccountManage.SetTwoFactorAuth(accountId, true);

// 更新账号信息
var updateResult = AccountManage.UpdateAccountInfo(accountId, "新昵称", "avatar.jpg", "真实姓名");
```

## 安全特性

### 密码安全
- 使用 SHA256 哈希算法加密密码
- 添加盐值防止彩虹表攻击
- 密码强度验证（长度、字母数字组合）

### 数据验证
- 邮箱格式验证
- 手机号格式验证（中国大陆）
- 用户名唯一性检查
- 参数空值检查

### 访问令牌
- 基于账号ID和时间戳生成
- SHA256 哈希加密
- 支持令牌验证和自动登录

## 数据库字段映射

基于 `Account` 实体类的主要字段：
- `id`: 主键，唯一标识
- `usrname`: 用户名
- `password`: 加密后的密码
- `email`: 邮箱地址
- `mobile`: 手机号码
- `security_question`: 安全问题数组
- `security_answer`: 安全答案数组（加密）
- `two_factor_auth`: 二次验证开关
- `accessToken`: 访问令牌
- `lastLogin`: 最后登录时间
- `currentLogin`: 当前登录时间
- `ip`: 当前IP地址
- `lastIP`: 最后IP地址

## 错误处理

所有方法都包含完整的异常处理，返回统一的结果对象：
- `Success`: 操作是否成功
- `Message`: 结果消息
- 其他相关数据（如账号信息、令牌等）

## 注意事项

1. 确保数据库连接正常，`ORMTables.Instance` 可用
2. 密码长度要求：6-20位，包含字母和数字
3. 手机号格式：中国大陆11位手机号
4. 邮箱格式：标准邮箱格式验证
5. 所有敏感信息（密码、安全答案）都会加密存储

## 运行演示

```csharp
// 运行完整功能演示
AccountManageExample.RunAllDemos();
```

这将演示所有功能的使用方法和效果。
