{"id": "be8940e3-c988-4b1b-b73c-d9cc0d72e63c", "revision": 0, "last_node_id": 106, "last_link_id": 209, "nodes": [{"id": 90, "type": "PreviewImage", "pos": [351.742919921875, -617.4329223632812], "size": [336.8946228027344, 737.948974609375], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 198}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 99, "type": "LayerUtility: ImageBlendAdvance V2", "pos": [322.5862731933594, -1023.0084838867188], "size": [315, 338], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "background_image", "name": "background_image", "type": "IMAGE", "link": 186}, {"localized_name": "layer_image", "name": "layer_image", "type": "IMAGE", "link": 206}, {"localized_name": "layer_mask", "name": "layer_mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "invert_mask", "name": "invert_mask", "type": "BOOLEAN", "widget": {"name": "invert_mask"}, "link": null}, {"localized_name": "blend_mode", "name": "blend_mode", "type": "COMBO", "widget": {"name": "blend_mode"}, "link": null}, {"localized_name": "opacity", "name": "opacity", "type": "INT", "widget": {"name": "opacity"}, "link": null}, {"localized_name": "x_percent", "name": "x_percent", "type": "FLOAT", "widget": {"name": "x_percent"}, "link": null}, {"localized_name": "y_percent", "name": "y_percent", "type": "FLOAT", "widget": {"name": "y_percent"}, "link": null}, {"localized_name": "mirror", "name": "mirror", "type": "COMBO", "widget": {"name": "mirror"}, "link": null}, {"localized_name": "scale", "name": "scale", "type": "FLOAT", "widget": {"name": "scale"}, "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "FLOAT", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "rotate", "name": "rotate", "type": "FLOAT", "widget": {"name": "rotate"}, "link": null}, {"localized_name": "transform_method", "name": "transform_method", "type": "COMBO", "widget": {"name": "transform_method"}, "link": null}, {"localized_name": "anti_aliasing", "name": "anti_aliasing", "type": "INT", "widget": {"name": "anti_aliasing"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [189]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageBlendAdvance V2"}, "widgets_values": [true, "normal", 100, 50, 50, "None", 1, 1, 0, "lanc<PERSON>s", 0], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 97, "type": "PreviewImage", "pos": [715.58349609375, -1068.72119140625], "size": [491.9326477050781, 312.9626770019531], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 189}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 95, "type": "LayerUtility: ColorPicker", "pos": [-464, -1017], "size": [210, 94], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "color", "name": "color", "type": "COLOR", "widget": {"name": "color"}, "link": null}, {"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}], "outputs": [{"localized_name": "value", "name": "value", "type": "STRING", "slot_index": 0, "links": [180]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ColorPicker"}, "widgets_values": ["#f7f7f7", "HEX"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 94, "type": "LayerUtility: ColorImage V2", "pos": [-99.72418212890625, -912.1459350585938], "size": [315, 130], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "size_as", "name": "size_as", "shape": 7, "type": "*", "link": null}, {"localized_name": "size", "name": "size", "type": "COMBO", "widget": {"name": "size"}, "link": null}, {"localized_name": "custom_width", "name": "custom_width", "type": "INT", "widget": {"name": "custom_width"}, "link": 208}, {"localized_name": "custom_height", "name": "custom_height", "type": "INT", "widget": {"name": "custom_height"}, "link": 209}, {"localized_name": "color", "name": "color", "type": "STRING", "widget": {"name": "color"}, "link": 180}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [186]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ColorImage V2"}, "widgets_values": ["custom", 512, 512, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 105, "type": "GetImageSize+", "pos": [-673.52783203125, -776.24462890625], "size": [159.50155639648438, 66], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 200}], "outputs": [{"localized_name": "width", "name": "width", "type": "INT", "links": [208]}, {"localized_name": "height", "name": "height", "type": "INT", "links": [209]}, {"localized_name": "count", "name": "count", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "GetImageSize+"}}, {"id": 102, "type": "SimpleMath+", "pos": [-451.9440002441406, -728.8571166992188], "size": [315, 78], "flags": {}, "order": 2, "mode": 4, "inputs": [{"localized_name": "a", "name": "a", "shape": 7, "type": "INT,FLOAT", "link": null}, {"localized_name": "b", "name": "b", "shape": 7, "type": "INT,FLOAT", "link": null}, {"localized_name": "value", "name": "value", "type": "STRING", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "整数", "name": "INT", "type": "INT", "slot_index": 0, "links": []}, {"localized_name": "浮点", "name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"cnr_id": "comfyui_essentials", "ver": "1.1.0", "Node name for S&R": "SimpleMath+"}, "widgets_values": [""]}, {"id": 85, "type": "LayerMask: SegmentAnythingUltra V2", "pos": [-35.688087463378906, -573.4832153320312], "size": [330.8785095214844, 366], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 158}, {"localized_name": "SAM模型", "name": "sam_model", "type": "COMBO", "widget": {"name": "sam_model"}, "link": null}, {"localized_name": "Grounding Dino模型", "name": "grounding_dino_model", "type": "COMBO", "widget": {"name": "grounding_dino_model"}, "link": null}, {"localized_name": "阈值", "name": "threshold", "type": "FLOAT", "widget": {"name": "threshold"}, "link": null}, {"localized_name": "细节方法", "name": "detail_method", "type": "COMBO", "widget": {"name": "detail_method"}, "link": null}, {"localized_name": "细节腐蚀", "name": "detail_erode", "type": "INT", "widget": {"name": "detail_erode"}, "link": null}, {"localized_name": "细节膨胀", "name": "detail_dilate", "type": "INT", "widget": {"name": "detail_dilate"}, "link": null}, {"localized_name": "黑点", "name": "black_point", "type": "FLOAT", "widget": {"name": "black_point"}, "link": null}, {"localized_name": "白点", "name": "white_point", "type": "FLOAT", "widget": {"name": "white_point"}, "link": null}, {"localized_name": "处理细节", "name": "process_detail", "type": "BOOLEAN", "widget": {"name": "process_detail"}, "link": null}, {"localized_name": "提示", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": null}, {"localized_name": "设备", "name": "device", "type": "COMBO", "widget": {"name": "device"}, "link": null}, {"localized_name": "最大百万像素", "name": "max_megapixels", "type": "FLOAT", "widget": {"name": "max_megapixels"}, "link": null}, {"localized_name": "缓存模型", "name": "cache_model", "type": "BOOLEAN", "widget": {"name": "cache_model"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [205, 206]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerMask: SegmentAnythingUltra V2"}, "widgets_values": ["sam_vit_h (2.56GB)", "GroundingDINO_SwinB (938MB)", 0.3, "VITMatte", 6, 6, 0.15, 0.99, true, "hair", "cuda", 2, false], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 3, "type": "LoadImage", "pos": [-780.0601196289062, -593.6231079101562], "size": [589.18896484375, 468.7266845703125], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [158, 198, 200]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_phydo_00013_.png", "image"]}, {"id": 92, "type": "PreviewImage", "pos": [728.2693481445312, -605.71826171875], "size": [575.1788330078125, 295.68121337890625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 205}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}], "links": [[158, 3, 0, 85, 0, "IMAGE"], [180, 95, 0, 94, 4, "STRING"], [186, 94, 0, 99, 0, "IMAGE"], [189, 99, 0, 97, 0, "IMAGE"], [198, 3, 0, 90, 0, "IMAGE"], [200, 3, 0, 105, 0, "IMAGE"], [205, 85, 0, 92, 0, "IMAGE"], [206, 85, 0, 99, 1, "IMAGE"], [208, 105, 0, 94, 2, "INT"], [209, 105, 1, 94, 3, "INT"]], "groups": [{"id": 1, "title": "Group", "bounding": [-151, -669, 2776, 1497], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.2839025177495051, "offset": [1760.6540673595202, 1473.8390721941983]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}