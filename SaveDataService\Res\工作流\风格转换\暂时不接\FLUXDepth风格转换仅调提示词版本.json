{"id": "1879136c-d723-46af-aefb-8a1a9481d630", "revision": 0, "last_node_id": 62, "last_link_id": 86, "nodes": [{"id": 22, "type": "SamplerCustom", "pos": [501.6785583496094, -520.8292846679688], "size": [424.28216552734375, 547.2218017578125], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 28}, {"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 24}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 25}, {"label": "采样器", "localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 37}, {"label": "Sigmas", "localized_name": "Sigmas", "name": "sigmas", "type": "SIGMAS", "link": 38}, {"label": "Latent", "localized_name": "Latent", "name": "latent_image", "type": "LATENT", "link": 53}, {"localized_name": "添加噪波", "name": "add_noise", "type": "BOOLEAN", "widget": {"name": "add_noise"}, "link": null}, {"localized_name": "噪波种子", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}], "outputs": [{"label": "输出", "localized_name": "Latent", "name": "output", "type": "LATENT", "links": [46]}, {"label": "降噪输出", "localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "SamplerCustom"}, "widgets_values": [true, 271453671564490, "randomize", 3.5]}, {"id": 37, "type": "PreviewImage", "pos": [-588.04296875, -806.3740844726562], "size": [494.51361083984375, 838.7733764648438], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 60}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 45, "type": "Image Comparer (rgthree)", "pos": [-48.36771774291992, -817.3445434570312], "size": [514.4216918945312, 847.214111328125], "flags": {}, "order": 32, "mode": 0, "inputs": [{"dir": 3, "label": "图像_A", "name": "image_a", "type": "IMAGE", "link": 79}, {"dir": 3, "label": "图像_B", "name": "image_b", "type": "IMAGE", "link": 62}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "1.0.0", "comparer_mode": "Slide"}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_eimqy_00003_.png&type=temp&subfolder=&rand=0.007236435276229192"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_eimqy_00004_.png&type=temp&subfolder=&rand=0.7084823147254788"}]]}, {"id": 44, "type": "PreviewImage", "pos": [976.447998046875, -801.537841796875], "size": [557.769775390625, 832.4588623046875], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 78}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 15, "type": "PreviewImage", "pos": [575.0401611328125, 341.8458557128906], "size": [308.3261413574219, 404.3208923339844], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "图像", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 17}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 30, "type": "BasicScheduler", "pos": [44.24113845825195, 284.321044921875], "size": [315, 106], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "模型", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 49}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"label": "降噪", "localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": 63}], "outputs": [{"label": "Sigmas", "localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "links": [38]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 0.9000000000000001]}, {"id": 21, "type": "CLIPTextEncode", "pos": [104.24113464355469, 604.3207397460938], "size": [400, 200], "flags": {"collapsed": true}, "order": 23, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 22}, {"label": "文本", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 34}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [39]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 23, "type": "CLIPTextEncode", "pos": [114.24115753173828, 654.3207397460938], "size": [400, 200], "flags": {"collapsed": true}, "order": 12, "mode": 0, "inputs": [{"label": "CLIP", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 26}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [27]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 31, "type": "FluxGuidance", "pos": [64.24111938476562, 224.32110595703125], "size": [315, 58], "flags": {"collapsed": true}, "order": 25, "mode": 0, "inputs": [{"label": "条件", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 39}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "条件", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [40]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 26, "type": "ShowText|pysssss", "pos": [-1104.************, 395.70806884765625], "size": [223.1726531982422, 372.44647216796875], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "STRING", "link": 30}], "outputs": [{"label": "字符串", "localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": null}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.3", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": []}, {"id": 35, "type": "VAEDecode", "pos": [104.24113464355469, 704.3207397460938], "size": [210, 46], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "Latent", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 46}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 47}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [48, 60, 62, 77]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 11, "type": "ControlNetApplyAdvanced", "pos": [936.0182495117188, 333.0755920410156], "size": [315, 186], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 40}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 27}, {"label": "ControlNet", "localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 10}, {"label": "图像", "localized_name": "图像", "name": "image", "type": "IMAGE", "link": 36}, {"localized_name": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": 50}, {"label": "强度", "localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": 67}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"label": "结束时间", "localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": 66}], "outputs": [{"label": "正面条件", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "links": [24]}, {"label": "负面条件", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "links": [25]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0.7500000000000001, 0, 0.8000000000000002]}, {"id": 38, "type": "VAEEncode", "pos": [-263.48211669921875, 371.3731689453125], "size": [210, 46], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "图像", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 74}, {"label": "VAE", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 52}], "outputs": [{"label": "Latent", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [53]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 29, "type": "KSamplerSelect", "pos": [-318.7944641113281, 272.1321105957031], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"label": "采样器", "localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "links": [37]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 49, "type": "PrimitiveNode", "pos": [495.08966064453125, -648.477294921875], "size": [210, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "FLOAT", "type": "FLOAT", "widget": {"name": "strength"}, "links": [67]}], "properties": {"Run widget replace on values": false}, "widgets_values": [0.7500000000000001, "fixed"]}, {"id": 50, "type": "PrimitiveNode", "pos": [718.2061157226562, -651.2919311523438], "size": [210, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "FLOAT", "type": "FLOAT", "widget": {"name": "end_percent"}, "links": [66]}], "properties": {"Run widget replace on values": false}, "widgets_values": [0.8000000000000002, "fixed"]}, {"id": 17, "type": "DepthAnythingV2Preprocessor", "pos": [561, 212], "size": [315, 82], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 75}, {"localized_name": "ckpt_name", "name": "ckpt_name", "shape": 7, "type": "COMBO", "widget": {"name": "ckpt_name"}, "link": null}, {"localized_name": "resolution", "name": "resolution", "shape": 7, "type": "INT", "widget": {"name": "resolution"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [17, 36]}], "properties": {"cnr_id": "comfyui_controlnet_aux", "ver": "83463c2e4b04e729268e57f638b4212e0da4badc", "Node name for S&R": "DepthAnythingV2Preprocessor"}, "widgets_values": ["depth_anything_v2_vitl.pth", 512]}, {"id": 14, "type": "LoadImage", "pos": [-1107.6881103515625, -809.8729858398438], "size": [463.9326171875, 831.471435546875], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [16, 18, 41, 72]}, {"label": "遮罩", "localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_savbp_00009_.png", "image"]}, {"id": 19, "type": "UNETLoader", "pos": [44.24113845825195, 464.321044921875], "size": [315, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"label": "模型", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [28, 49]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev-fp8.safetensors", "fp8_e4m3fn"]}, {"id": 20, "type": "DualCLIPLoader", "pos": [-307.2766418457031, 655.5701904296875], "size": [315, 130], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [22, 26]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 12, "type": "ControlNetLoader", "pos": [937.4221801757812, 224.27699279785156], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"label": "ControlNet", "localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "links": [10]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["flux\\flux_controlnet_union.safetensors"]}, {"id": 36, "type": "VAELoader", "pos": [934.3263549804688, 562.42431640625], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [47, 50, 52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.27", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 54, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [-26.418088912963867, 162.1765594482422], "size": [303.6851501464844, 330], "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 72}, {"localized_name": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": null}, {"localized_name": "aspect_ratio", "name": "aspect_ratio", "type": "COMBO", "widget": {"name": "aspect_ratio"}, "link": null}, {"localized_name": "proportional_width", "name": "proportional_width", "type": "INT", "widget": {"name": "proportional_width"}, "link": null}, {"localized_name": "proportional_height", "name": "proportional_height", "type": "INT", "widget": {"name": "proportional_height"}, "link": null}, {"localized_name": "fit", "name": "fit", "type": "COMBO", "widget": {"name": "fit"}, "link": null}, {"localized_name": "method", "name": "method", "type": "COMBO", "widget": {"name": "method"}, "link": null}, {"localized_name": "round_to_multiple", "name": "round_to_multiple", "type": "COMBO", "widget": {"name": "round_to_multiple"}, "link": null}, {"localized_name": "scale_to_side", "name": "scale_to_side", "type": "COMBO", "widget": {"name": "scale_to_side"}, "link": null}, {"localized_name": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": null}, {"localized_name": "background_color", "name": "background_color", "type": "STRING", "widget": {"name": "background_color"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "links": [73, 74, 75]}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "original_size", "name": "original_size", "type": "BOX", "links": null}, {"localized_name": "width", "name": "width", "type": "INT", "links": null}, {"localized_name": "height", "name": "height", "type": "INT", "links": null}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "1.0.90", "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "longest", 1024, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 25, "type": "Florence2ModelLoader", "pos": [-1132.5289306640625, 205.02716064453125], "size": [274.778076171875, 106], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "LoRA", "localized_name": "lora", "name": "lora", "shape": 7, "type": "PEFTLORA", "link": null}, {"localized_name": "model", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "precision", "name": "precision", "type": "COMBO", "widget": {"name": "precision"}, "link": null}, {"localized_name": "attention", "name": "attention", "type": "COMBO", "widget": {"name": "attention"}, "link": null}], "outputs": [{"label": "Florence2", "localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "links": [29]}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2ModelLoader"}, "widgets_values": ["Florence-2-Flux-Large", "fp16", "sdpa"]}, {"id": 24, "type": "Florence2Run", "pos": [-875.3135375976562, 355.2562255859375], "size": [400, 364], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 73}, {"label": "Florence2", "localized_name": "florence2_model", "name": "florence2_model", "type": "FL2MODEL", "link": 29}, {"localized_name": "text_input", "name": "text_input", "type": "STRING", "widget": {"name": "text_input"}, "link": null}, {"localized_name": "task", "name": "task", "type": "COMBO", "widget": {"name": "task"}, "link": null}, {"localized_name": "fill_mask", "name": "fill_mask", "type": "BOOLEAN", "widget": {"name": "fill_mask"}, "link": null}, {"localized_name": "keep_model_loaded", "name": "keep_model_loaded", "shape": 7, "type": "BOOLEAN", "widget": {"name": "keep_model_loaded"}, "link": null}, {"localized_name": "max_new_tokens", "name": "max_new_tokens", "shape": 7, "type": "INT", "widget": {"name": "max_new_tokens"}, "link": null}, {"localized_name": "num_beams", "name": "num_beams", "shape": 7, "type": "INT", "widget": {"name": "num_beams"}, "link": null}, {"localized_name": "do_sample", "name": "do_sample", "shape": 7, "type": "BOOLEAN", "widget": {"name": "do_sample"}, "link": null}, {"localized_name": "output_mask_select", "name": "output_mask_select", "shape": 7, "type": "STRING", "widget": {"name": "output_mask_select"}, "link": null}, {"localized_name": "seed", "name": "seed", "shape": 7, "type": "INT", "widget": {"name": "seed"}, "link": null}], "outputs": [{"label": "图像", "localized_name": "image", "name": "image", "type": "IMAGE", "links": null}, {"label": "遮罩", "localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"label": "caption", "localized_name": "caption", "name": "caption", "type": "STRING", "links": [30, 33, 64, 80]}, {"label": "json数据", "localized_name": "data", "name": "data", "type": "JSON", "links": null}], "properties": {"cnr_id": "comfyui-florence2", "ver": "1.0.3", "Node name for S&R": "Florence2Run"}, "widgets_values": ["", "prompt_gen_mixed_caption_plus", true, false, 1024, 3, true, "", 589384926955654, "randomize"]}, {"id": 27, "type": "LayerUtility: Text<PERSON>oin", "pos": [-302.7232666015625, 473.29364013671875], "size": [315, 130], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "文本_1", "localized_name": "text_1", "name": "text_1", "type": "STRING", "link": 83}, {"label": "文本_2", "localized_name": "text_2", "name": "text_2", "shape": 7, "type": "STRING", "link": 64}, {"localized_name": "text_3", "name": "text_3", "shape": 7, "type": "STRING", "link": null}, {"localized_name": "text_4", "name": "text_4", "shape": 7, "type": "STRING", "link": null}], "outputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "STRING", "links": [34]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "a085d8ca3cf3e3e8624ac0bb37a474d9d329e60e", "Node name for S&R": "LayerUtility: Text<PERSON>oin"}, "widgets_values": [], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 48, "type": "PrimitiveNode", "pos": [737.6025390625, -837.1680908203125], "size": [210, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "FLOAT", "type": "FLOAT", "widget": {"name": "denoise"}, "links": [63]}], "properties": {"Run widget replace on values": false}, "widgets_values": [0.9000000000000001, "fixed"], "color": "#322", "bgcolor": "#533"}, {"id": 56, "type": "Color Correct (mtb)", "pos": [433.1546936035156, -935.3778076171875], "size": [270, 226], "flags": {"collapsed": false}, "order": 30, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 77}, {"localized_name": "clamp", "name": "clamp", "type": "COMBO", "widget": {"name": "clamp"}, "link": null}, {"localized_name": "gamma", "name": "gamma", "type": "FLOAT", "widget": {"name": "gamma"}, "link": null}, {"localized_name": "contrast", "name": "contrast", "type": "FLOAT", "widget": {"name": "contrast"}, "link": null}, {"localized_name": "exposure", "name": "exposure", "type": "FLOAT", "widget": {"name": "exposure"}, "link": null}, {"localized_name": "offset", "name": "offset", "type": "FLOAT", "widget": {"name": "offset"}, "link": null}, {"localized_name": "hue", "name": "hue", "type": "FLOAT", "widget": {"name": "hue"}, "link": null}, {"localized_name": "saturation", "name": "saturation", "type": "FLOAT", "widget": {"name": "saturation"}, "link": null}, {"localized_name": "value", "name": "value", "type": "FLOAT", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [78, 79]}], "properties": {"cnr_id": "comfy-mtb", "ver": "0.4.0", "Node name for S&R": "Color Correct (mtb)"}, "widgets_values": [false, 5, 5, 0, 0, 0, 1, 1]}, {"id": 61, "type": "ShowText|pysssss", "pos": [543.2540893554688, 870.5355224609375], "size": [422.1034851074219, 368.4195861816406], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 86}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": []}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": []}, {"id": 57, "type": "ShowText|pysssss", "pos": [66.95830535888672, 881.9618530273438], "size": [463.57080078125, 343.0244140625], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "link": 80}], "outputs": [{"localized_name": "字符串", "name": "STRING", "shape": 6, "type": "STRING", "links": [81]}], "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "Node name for S&R": "ShowText|pysssss"}, "widgets_values": []}, {"id": 60, "type": "LayerUtility: Text<PERSON>oin", "pos": [-1347.46923828125, 892.3548583984375], "size": [315, 130], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "文本_1", "localized_name": "text_1", "name": "text_1", "type": "STRING", "link": 81}, {"label": "文本_2", "localized_name": "text_2", "name": "text_2", "shape": 7, "type": "STRING", "link": 82}, {"localized_name": "text_3", "name": "text_3", "shape": 7, "type": "STRING", "link": null}, {"localized_name": "text_4", "name": "text_4", "shape": 7, "type": "STRING", "link": null}], "outputs": [{"label": "文本", "localized_name": "text", "name": "text", "type": "STRING", "links": [83, 85]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "a085d8ca3cf3e3e8624ac0bb37a474d9d329e60e", "Node name for S&R": "LayerUtility: Text<PERSON>oin"}, "widgets_values": [], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 62, "type": "LayerUtility: DeepSeekAPIV2", "pos": [-791.293701171875, 860.7501220703125], "size": [400, 324], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "历史", "name": "history", "shape": 7, "type": "DEEPSEEK_HISTORY", "link": null}, {"localized_name": "模型", "name": "model", "type": "COMBO", "widget": {"name": "model"}, "link": null}, {"localized_name": "超时", "name": "time_out", "type": "INT", "widget": {"name": "time_out"}, "link": null}, {"localized_name": "最大令牌数", "name": "max_tokens", "type": "INT", "widget": {"name": "max_tokens"}, "link": null}, {"localized_name": "温度", "name": "temperature", "type": "FLOAT", "widget": {"name": "temperature"}, "link": null}, {"localized_name": "顶部P", "name": "top_p", "type": "FLOAT", "widget": {"name": "top_p"}, "link": null}, {"localized_name": "存在惩罚", "name": "presence_penalty", "type": "FLOAT", "widget": {"name": "presence_penalty"}, "link": null}, {"localized_name": "频率惩罚", "name": "frequency_penalty", "type": "FLOAT", "widget": {"name": "frequency_penalty"}, "link": null}, {"localized_name": "历史长度", "name": "history_length", "type": "INT", "widget": {"name": "history_length"}, "link": null}, {"localized_name": "系统提示", "name": "system_prompt", "type": "STRING", "widget": {"name": "system_prompt"}, "link": null}, {"localized_name": "用户提示", "name": "user_prompt", "type": "STRING", "widget": {"name": "user_prompt"}, "link": 85}], "outputs": [{"localized_name": "text", "name": "text", "type": "STRING", "links": [86]}, {"localized_name": "history", "name": "history", "type": "DEEPSEEK_HISTORY", "links": null}], "properties": {"cnr_id": "ComfyUI_LayerStyle_Advance", "ver": "fe35b54bd2781206994176f8913db4afabffcdb1", "Node name for S&R": "LayerUtility: DeepSeekAPIV2"}, "widgets_values": ["deepseek-r1(al<PERSON><PERSON>)", 300, 4096, 1, 1, 0, 0, 8, "You are a helpful assistant.", ""], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 58, "type": "CR Text", "pos": [-351.1885681152344, 871.6921997070312], "size": [400, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "text", "name": "text", "type": "*", "links": [82]}, {"localized_name": "show_help", "name": "show_help", "type": "STRING", "links": null}], "properties": {"cnr_id": "ComfyUI_Comfyroll_CustomNodes", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "Node name for S&R": "CR Text"}, "widgets_values": ["前面是一段图片的反推提示词，我想把他换成像素风格，你只需要删除他的风格类的提示词，其他内容不要改，再加上像素风格的强提示词，输出英文结果"]}], "links": [[10, 12, 0, 11, 2, "CONTROL_NET"], [17, 17, 0, 15, 0, "IMAGE"], [22, 20, 0, 21, 0, "CLIP"], [24, 11, 0, 22, 1, "CONDITIONING"], [25, 11, 1, 22, 2, "CONDITIONING"], [26, 20, 0, 23, 0, "CLIP"], [27, 23, 0, 11, 1, "CONDITIONING"], [28, 19, 0, 22, 0, "MODEL"], [29, 25, 0, 24, 1, "FL2MODEL"], [30, 24, 2, 26, 0, "STRING"], [34, 27, 0, 21, 1, "STRING"], [36, 17, 0, 11, 3, "IMAGE"], [37, 29, 0, 22, 3, "SAMPLER"], [38, 30, 0, 22, 4, "SIGMAS"], [39, 21, 0, 31, 0, "CONDITIONING"], [40, 31, 0, 11, 0, "CONDITIONING"], [46, 22, 0, 35, 0, "LATENT"], [47, 36, 0, 35, 1, "VAE"], [49, 19, 0, 30, 0, "MODEL"], [50, 36, 0, 11, 4, "VAE"], [52, 36, 0, 38, 1, "VAE"], [53, 38, 0, 22, 5, "LATENT"], [60, 35, 0, 37, 0, "IMAGE"], [62, 35, 0, 45, 1, "IMAGE"], [63, 48, 0, 30, 3, "FLOAT"], [64, 24, 2, 27, 1, "STRING"], [66, 50, 0, 11, 7, "FLOAT"], [67, 49, 0, 11, 5, "FLOAT"], [72, 14, 0, 54, 0, "IMAGE"], [73, 54, 0, 24, 0, "IMAGE"], [74, 54, 0, 38, 0, "IMAGE"], [75, 54, 0, 17, 0, "IMAGE"], [77, 35, 0, 56, 0, "IMAGE"], [78, 56, 0, 44, 0, "IMAGE"], [79, 56, 0, 45, 0, "IMAGE"], [80, 24, 2, 57, 0, "STRING"], [81, 57, 0, 60, 0, "STRING"], [82, 58, 0, 60, 1, "STRING"], [83, 60, 0, 27, 0, "STRING"], [85, 60, 0, 62, 10, "STRING"], [86, 62, 0, 61, 0, "STRING"]], "groups": [{"id": 1, "title": "controlnet强度，和停止时间", "bounding": [485, -725, 453, 168], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "根据需求调整，推荐降噪0.7-1", "bounding": [544, -905, 352, 161], "color": "#A88", "font_size": 24, "flags": {}}, {"id": 3, "title": "FLUX生图", "bounding": [-364.02716064453125, 97.5869140625, 781, 724], "color": "#3f789e", "font_size": 50, "flags": {}}, {"id": 4, "title": "关键词反推+文本补充", "bounding": [-1129, 88, 678, 717], "color": "#88A", "font_size": 50, "flags": {}}, {"id": 5, "title": "depth v2 controlnet", "bounding": [540, 75, 747, 721], "color": "#a1309b", "font_size": 50, "flags": {}}, {"id": 6, "title": "调色后图像", "bounding": [943, -927, 620, 981], "color": "#b58b2a", "font_size": 50, "flags": {}}, {"id": 7, "title": "调色前后对比", "bounding": [-55, -933, 533, 989], "color": "#8AA", "font_size": 50, "flags": {}}, {"id": 8, "title": "未调色图像", "bounding": [-603, -936, 528, 993], "color": "#8A8", "font_size": 50, "flags": {}}, {"id": 9, "title": "加载图像", "bounding": [-1136, -934, 511, 990], "color": "#A88", "font_size": 50, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.45, "offset": [2034.8026969024456, -412.3401025489504]}, "frontendVersion": "1.18.9", "ue_links": [], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "0246.VERSION": [0, 0, 4]}, "version": 0.4}