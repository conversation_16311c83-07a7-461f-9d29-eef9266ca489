{"id": "9eb1fd3e-bfb6-4353-af43-69d51bcfa124", "revision": 0, "last_node_id": 361, "last_link_id": 705, "nodes": [{"id": 141, "type": "PreviewImage", "pos": [4238.80078125, -488], "size": [466.0791320800781, 510.5869445800781], "flags": {}, "order": 56, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 287}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 134, "type": "ImageCrop", "pos": [3224, 78], "size": [315, 130], "flags": {}, "order": 45, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 280}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [283, 289]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop"}, "widgets_values": [800, 1000, 57, 0]}, {"id": 149, "type": "Reroute", "pos": [4072, 262], "size": [75, 26], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 292}], "outputs": [{"name": "", "type": "IMAGE", "slot_index": 0, "links": [293, 294]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 84, "type": "PreviewImage", "pos": [1815, -488], "size": [486.7819519042969, 529.9662475585938], "flags": {}, "order": 39, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 386}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 97, "type": "FromBasicPipe_v2", "pos": [1519, 1268], "size": [267, 126], "flags": {"collapsed": true}, "order": 29, "mode": 0, "inputs": [{"localized_name": "basic_pipe", "name": "basic_pipe", "type": "BASIC_PIPE", "link": 149}], "outputs": [{"localized_name": "basic_pipe", "name": "basic_pipe", "type": "BASIC_PIPE", "slot_index": 0, "links": []}, {"localized_name": "model", "name": "model", "type": "MODEL", "slot_index": 1, "links": [150]}, {"localized_name": "clip", "name": "clip", "type": "CLIP", "slot_index": 2, "links": null}, {"localized_name": "vae", "name": "vae", "type": "VAE", "slot_index": 3, "links": [153]}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 4, "links": []}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 5, "links": []}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "FromBasicPipe_v2"}, "widgets_values": []}, {"id": 160, "type": "DualCLIPLoader", "pos": [-125.42578125, -296.70538330078125], "size": [315, 130], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [313, 329, 343, 362]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp8_e4m3fn.safetensors", "clip_l.safetensors", "flux", "default"], "color": "#323", "bgcolor": "#535"}, {"id": 129, "type": "ImageScale", "pos": [3224, 262], "size": [315, 130], "flags": {}, "order": 42, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 388}, {"localized_name": "缩放算法", "name": "upscale_method", "type": "COMBO", "widget": {"name": "upscale_method"}, "link": null}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "裁剪", "name": "crop", "type": "COMBO", "widget": {"name": "crop"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [280, 284, 292]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageScale"}, "widgets_values": ["bilinear", 2048, 2048, "disabled"]}, {"id": 95, "type": "ToBasicPipe", "pos": [685, 1267], "size": [241.79998779296875, 106], "flags": {"collapsed": true}, "order": 25, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 342}, {"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 343}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 344}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 340}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 341}], "outputs": [{"localized_name": "basic_pipe", "name": "basic_pipe", "type": "BASIC_PIPE", "slot_index": 0, "links": [149]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "ToBasicPipe"}, "widgets_values": []}, {"id": 159, "type": "VAELoader", "pos": [-125.42578125, -146.70538330078125], "size": [311.81634521484375, 60.429901123046875], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [327, 331, 344, 359]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"], "color": "#323", "bgcolor": "#535"}, {"id": 167, "type": "EmptySD3LatentImage", "pos": [234.57423400878906, -136.70538330078125], "size": [315, 106], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 319}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 320}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [404]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1280, 1280, 1]}, {"id": 178, "type": "SamplerCustomAdvanced", "pos": [1035, 143], "size": [270, 330], "flags": {}, "order": 31, "mode": 0, "inputs": [{"localized_name": "噪波", "name": "noise", "type": "NOISE", "link": 332}, {"localized_name": "引导器", "name": "guider", "type": "GUIDER", "link": 333}, {"localized_name": "采样器", "name": "sampler", "type": "SAMPLER", "link": 334}, {"localized_name": "西格玛", "name": "sigmas", "type": "SIGMAS", "link": 335}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 404}], "outputs": [{"localized_name": "Latent", "name": "output", "type": "LATENT", "slot_index": 0, "links": []}, {"localized_name": "降噪Latent", "name": "denoised_output", "type": "LATENT", "slot_index": 1, "links": [330]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 138, "type": "PreviewImage", "pos": [3224, -488], "size": [466.0791320800781, 510.5869445800781], "flags": {}, "order": 49, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 283}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 163, "type": "BasicScheduler", "pos": [235, 253], "size": [315, 106], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 315}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Sigmas", "name": "SIGMAS", "type": "SIGMAS", "slot_index": 0, "links": [335]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", 25, 1]}, {"id": 165, "type": "RandomNoise", "pos": [234.57423400878906, 3.2946057319641113], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "噪波随机种", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": null}], "outputs": [{"localized_name": "噪波", "name": "NOISE", "type": "NOISE", "slot_index": 0, "links": [332]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "RandomNoise"}, "widgets_values": [671983948663894, "fixed"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 162, "type": "KSamplerSelect", "pos": [234.57423400878906, 143.29461669921875], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}], "outputs": [{"localized_name": "采样器", "name": "SAMPLER", "type": "SAMPLER", "slot_index": 0, "links": [334]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "KSamplerSelect"}, "widgets_values": ["deis"]}, {"id": 258, "type": "PreviewImage", "pos": [5110, -488], "size": [439.9931945800781, 493.07720947265625], "flags": {}, "order": 61, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 496}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 142, "type": "ImageCrop", "pos": [4239, 78], "size": [315, 130], "flags": {}, "order": 53, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 293}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [287, 291]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop"}, "widgets_values": [1000, 1000, 1184, 1074]}, {"id": 139, "type": "ImageCrop", "pos": [3711, 78], "size": [315, 130], "flags": {}, "order": 46, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 284}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [285, 290]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop"}, "widgets_values": [1000, 1000, 1158, 42]}, {"id": 150, "type": "Reroute", "pos": [4557, 265], "size": [75, 26], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 294}], "outputs": [{"name": "", "type": "IMAGE", "slot_index": 0, "links": [498, 587]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 140, "type": "PreviewImage", "pos": [3711, -488], "size": [466.0791320800781, 510.5869445800781], "flags": {}, "order": 51, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 285}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 299, "type": "PreviewImage", "pos": [4751, -488], "size": [300.5074157714844, 502.5292053222656], "flags": {}, "order": 67, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 588}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 298, "type": "ImageCrop", "pos": [4737, 78], "size": [315, 130], "flags": {}, "order": 59, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 587}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [588, 589]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop"}, "widgets_values": [500, 1000, 785, 42]}, {"id": 161, "type": "UNETLoader", "pos": [-125.42578125, -426.70538330078125], "size": [315, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [321]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev-fp8.safetensors", "fp8_e4m3fn"], "color": "#323", "bgcolor": "#535"}, {"id": 259, "type": "ImageCrop", "pos": [5230, 78], "size": [315, 130], "flags": {}, "order": 58, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 498}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "x", "name": "x", "type": "INT", "widget": {"name": "x"}, "link": null}, {"localized_name": "y", "name": "y", "type": "INT", "widget": {"name": "y"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [496, 590, 620, 621, 622, 623]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 1369, 1031]}, {"id": 177, "type": "VAEDecode", "pos": [1356, 191], "size": [210, 46], "flags": {"collapsed": true}, "order": 33, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 330}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 331}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [339, 393]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 196, "type": "SaveImage", "pos": [1108, -488], "size": [497.5215148925781, 523.5535278320312], "flags": {}, "order": 36, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 393}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 629}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 314, "type": "Reroute", "pos": [-184, 1446], "size": [75, 26], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 702}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [628]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 145, "type": "SaveImage", "pos": [3224, 478], "size": [315, 270], "flags": {}, "order": 50, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 289}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 634}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 146, "type": "SaveImage", "pos": [3711, 478], "size": [315, 270], "flags": {}, "order": 52, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 290}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 636}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 147, "type": "SaveImage", "pos": [4239, 471], "size": [315, 270], "flags": {}, "order": 57, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 291}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 638}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 300, "type": "SaveImage", "pos": [4721, 471], "size": [315, 270], "flags": {}, "order": 68, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 589}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 640}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 325, "type": "Reroute", "pos": [2221, 1446], "size": [75, 26], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 631}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [632, 633]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 327, "type": "Reroute", "pos": [3522, 1446], "size": [75, 26], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 635}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [636, 637]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 328, "type": "Reroute", "pos": [4121, 1446], "size": [75, 26], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 637}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [638, 639]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 329, "type": "Reroute", "pos": [4579, 1446], "size": [75, 26], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 639}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [640, 641]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 330, "type": "Reroute", "pos": [5088, 1446], "size": [75, 26], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 641}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [642, 643]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 326, "type": "Reroute", "pos": [3046, 1446], "size": [75, 26], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 633}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [634, 635, 652]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 175, "type": "CLIPTextEncode", "pos": [235, -207], "size": [285.6000061035156, 81], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 329}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [325]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 301, "type": "SaveImage", "pos": [5203, 469], "size": [315, 270], "flags": {}, "order": 62, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 590}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 642}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 337, "type": "SaveImage", "pos": [5714, -490], "size": [399.6399841308594, 436.32391357421875], "flags": {}, "order": 69, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 675}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 687}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 341, "type": "SaveImage", "pos": [6188, -490], "size": [400, 440], "flags": {}, "order": 70, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 684}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 689}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 342, "type": "SaveImage", "pos": [6734, -490], "size": [400, 440], "flags": {}, "order": 71, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 685}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 691}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 343, "type": "SaveImage", "pos": [7314, -490], "size": [400, 440], "flags": {}, "order": 72, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 686}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 694}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 313, "type": "Reroute", "pos": [5545, 1446], "size": [75, 26], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 643}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [687, 688]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 344, "type": "Reroute", "pos": [6018, 1446], "size": [75, 26], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 688}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [689, 692]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 346, "type": "Reroute", "pos": [6498, 1445], "size": [75, 26], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 692}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [691, 693]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 347, "type": "Reroute", "pos": [7113, 1446], "size": [75, 26], "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 693}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [694]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 179, "type": "SaveImage", "pos": [2440, -490], "size": [497.5215148925781, 523.5535278320312], "flags": {}, "order": 41, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 364}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 632}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 169, "type": "ModelSamplingFlux", "pos": [235, 393], "size": [315, 130], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 321}, {"localized_name": "最大移位", "name": "max_shift", "type": "FLOAT", "widget": {"name": "max_shift"}, "link": null}, {"localized_name": "基础移位", "name": "base_shift", "type": "FLOAT", "widget": {"name": "base_shift"}, "link": null}, {"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 322}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 323}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [315, 316, 342, 358]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, 1280, 1280]}, {"id": 302, "type": "Fast Groups Muter (rgthree)", "pos": [-720, -440], "size": [486.3753967285156, 178], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "OPT_CONNECTION", "type": "*", "links": null}], "properties": {"matchColors": "", "matchTitle": "", "showNav": true, "sort": "alphanumeric", "customSortAlphabet": "", "toggleRestriction": "default"}, "color": "#432", "bgcolor": "#653"}, {"id": 168, "type": "Note", "pos": [-586, -190], "size": [336, 288], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["If you get an error in any of the nodes above make sure the files are in the correct directories.\n\nSee the top of the examples page for the links : https://comfyanonymous.github.io/ComfyUI_examples/flux/\n\nflux1-dev.safetensors goes in: ComfyUI/models/unet/\n\nt5xxl_fp16.safetensors and clip_l.safetensors go in: ComfyUI/models/clip/\n\nae.safetensors goes in: ComfyUI/models/vae/\n\n\nTip: You can set the weight_dtype above to one of the fp8 types if you have memory issues."], "color": "#432", "bgcolor": "#653"}, {"id": 185, "type": "ToDetailerPipe", "pos": [2483.886962890625, 1350.6824951171875], "size": [400, 296], "flags": {"collapsed": false}, "order": 28, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 358}, {"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 362}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 359}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 383}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 380}, {"localized_name": "bbox_detector", "name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 705}, {"localized_name": "sam_model_opt", "name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL", "link": null}, {"localized_name": "segm_detector_opt", "name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR", "link": null}, {"localized_name": "detailer_hook", "name": "detailer_hook", "shape": 7, "type": "DETAILER_HOOK", "link": 374}, {"localized_name": "wildcard", "name": "wildcard", "type": "STRING", "widget": {"name": "wildcard"}, "link": null}, {"localized_name": "Select to add LoRA", "name": "Select to add LoRA", "type": "COMBO", "widget": {"name": "Select to add LoRA"}, "link": null}, {"localized_name": "Select to add Wildcard", "name": "Select to add Wildcard", "type": "COMBO", "widget": {"name": "Select to add Wildcard"}, "link": null}], "outputs": [{"localized_name": "detailer_pipe", "name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [357]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "ToDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 187, "type": "CoreMLDetailerHookProvider", "pos": [2563.749755859375, 1301.3359375], "size": [327.5999755859375, 58], "flags": {"collapsed": true}, "order": 7, "mode": 0, "inputs": [{"localized_name": "mode", "name": "mode", "type": "COMBO", "widget": {"name": "mode"}, "link": null}], "outputs": [{"localized_name": "DETAILER_HOOK", "name": "DETAILER_HOOK", "type": "DETAILER_HOOK", "slot_index": 0, "links": [374]}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "CoreMLDetailerHookProvider"}, "widgets_values": ["512x768"]}, {"id": 356, "type": "ONNXDetectorProvider", "pos": [2151.610595703125, 1543.9971923828125], "size": [270, 58], "flags": {}, "order": 8, "mode": 4, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"localized_name": "BBOX_DETECTOR", "name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": []}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "ONNXDetectorProvider"}, "widgets_values": [null]}, {"id": 18, "type": "UltralyticsDetectorProvider", "pos": [2091.**********, 1394.6241455078125], "size": [315, 78], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "model_name", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"localized_name": "BBOX_DETECTOR", "name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "slot_index": 0, "links": [369, 705]}, {"localized_name": "SEGM_DETECTOR", "name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "slot_index": 1, "links": []}], "properties": {"cnr_id": "comfyui-impact-subpack", "ver": "1.3.2", "Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 170, "type": "PrimitiveNode", "pos": [-106.35124969482422, -45.93499755859375], "size": [210, 82], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "width"}, "slot_index": 0, "links": [319, 322]}], "title": "width", "properties": {"Run widget replace on values": false}, "widgets_values": [1280, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 171, "type": "PrimitiveNode", "pos": [-107.5819091796875, 94.5252685546875], "size": [210, 82], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "height"}, "slot_index": 0, "links": [320, 323]}], "title": "height", "properties": {"Run widget replace on values": false}, "widgets_values": [1280, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 180, "type": "LoadImage", "pos": [681.7241821289062, -249.93861389160156], "size": [315, 314], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [338]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "LoadImage"}, "widgets_values": ["Pose_sheet_v02.png", "image"], "color": "#223", "bgcolor": "#335"}, {"id": 354, "type": "PrimitiveString", "pos": [-501.3878479003906, 1422.2882080078125], "size": [270, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "value", "name": "value", "type": "STRING", "widget": {"name": "value"}, "link": null}], "outputs": [{"localized_name": "字符串", "name": "STRING", "type": "STRING", "links": [702]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "PrimitiveString"}, "widgets_values": ["character_sheet_flux"]}, {"id": 324, "type": "Reroute", "pos": [868, 1446], "size": [75, 26], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "", "type": "*", "widget": {"name": "value"}, "link": 628}], "outputs": [{"name": "", "type": "STRING", "slot_index": 0, "links": [629, 631]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 173, "type": "ControlNetApplySD3", "pos": [621.826416015625, 114.87770080566406], "size": [315, 186], "flags": {}, "order": 21, "mode": 0, "inputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 324}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 325}, {"localized_name": "ControlNet", "name": "control_net", "type": "CONTROL_NET", "link": 698}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 327}, {"localized_name": "图像", "name": "image", "type": "IMAGE", "link": 338}, {"localized_name": "强度", "name": "strength", "type": "FLOAT", "widget": {"name": "strength"}, "link": null}, {"localized_name": "开始百分比", "name": "start_percent", "type": "FLOAT", "widget": {"name": "start_percent"}, "link": null}, {"localized_name": "结束百分比", "name": "end_percent", "type": "FLOAT", "widget": {"name": "end_percent"}, "link": null}], "outputs": [{"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [318, 340]}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [341, 380, 382]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetApplySD3"}, "widgets_values": [0.66, 0, 0.4], "color": "#223", "bgcolor": "#335"}, {"id": 164, "type": "BasicGuider", "pos": [680.0453491210938, -436.72869873046875], "size": [222.3482666015625, 46], "flags": {"collapsed": false}, "order": 27, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 316}, {"localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 317}], "outputs": [{"localized_name": "引导器", "name": "GUIDER", "type": "GUIDER", "slot_index": 0, "links": [333]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 166, "type": "FluxGuidance", "pos": [692.5836791992188, -366.5229797363281], "size": [317.4000244140625, 58], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 318}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [317, 381, 383]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "FluxGuidance"}, "widgets_values": [4.2], "color": "#233", "bgcolor": "#355"}, {"id": 157, "type": "CLIPTextEncode", "pos": [235, -417], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 313}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [324]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a character sheet, white background, multiple views, from multiple angles, visible face, portrait, the american woman is wearing a coat, dressed in autumn fashion, neutral expression, it is a masterpiece, photography, american woman, blonde"], "color": "#232", "bgcolor": "#353"}, {"id": 83, "type": "UpscaleModelLoader", "pos": [1936.6685791015625, 76.81490325927734], "size": [315, 58], "flags": {}, "order": 14, "mode": 0, "inputs": [{"localized_name": "模型名称", "name": "model_name", "type": "COMBO", "widget": {"name": "model_name"}, "link": null}], "outputs": [{"localized_name": "放大模型", "name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "slot_index": 0, "links": [118]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "UpscaleModelLoader"}, "widgets_values": ["4x-UltraSharp.pth"]}, {"id": 183, "type": "FaceDetailerPipe", "pos": [2460.************, 187.1222686767578], "size": [423.3936462402344, 893.7151489257812], "flags": {}, "order": 38, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 365}, {"localized_name": "detailer_pipe", "name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 357}, {"localized_name": "scheduler_func_opt", "name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC", "link": null}, {"localized_name": "guide_size", "name": "guide_size", "type": "FLOAT", "widget": {"name": "guide_size"}, "link": null}, {"localized_name": "guide_size_for", "name": "guide_size_for", "type": "BOOLEAN", "widget": {"name": "guide_size_for"}, "link": null}, {"localized_name": "max_size", "name": "max_size", "type": "FLOAT", "widget": {"name": "max_size"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}, {"localized_name": "feather", "name": "feather", "type": "INT", "widget": {"name": "feather"}, "link": null}, {"localized_name": "noise_mask", "name": "noise_mask", "type": "BOOLEAN", "widget": {"name": "noise_mask"}, "link": null}, {"localized_name": "force_inpaint", "name": "force_inpaint", "type": "BOOLEAN", "widget": {"name": "force_inpaint"}, "link": null}, {"localized_name": "bbox_threshold", "name": "bbox_threshold", "type": "FLOAT", "widget": {"name": "bbox_threshold"}, "link": null}, {"localized_name": "bbox_dilation", "name": "bbox_dilation", "type": "INT", "widget": {"name": "bbox_dilation"}, "link": null}, {"localized_name": "bbox_crop_factor", "name": "bbox_crop_factor", "type": "FLOAT", "widget": {"name": "bbox_crop_factor"}, "link": null}, {"localized_name": "sam_detection_hint", "name": "sam_detection_hint", "type": "COMBO", "widget": {"name": "sam_detection_hint"}, "link": null}, {"localized_name": "sam_dilation", "name": "sam_dilation", "type": "INT", "widget": {"name": "sam_dilation"}, "link": null}, {"localized_name": "sam_threshold", "name": "sam_threshold", "type": "FLOAT", "widget": {"name": "sam_threshold"}, "link": null}, {"localized_name": "sam_bbox_expansion", "name": "sam_bbox_expansion", "type": "INT", "widget": {"name": "sam_bbox_expansion"}, "link": null}, {"localized_name": "sam_mask_hint_threshold", "name": "sam_mask_hint_threshold", "type": "FLOAT", "widget": {"name": "sam_mask_hint_threshold"}, "link": null}, {"localized_name": "sam_mask_hint_use_negative", "name": "sam_mask_hint_use_negative", "type": "COMBO", "widget": {"name": "sam_mask_hint_use_negative"}, "link": null}, {"localized_name": "drop_size", "name": "drop_size", "type": "INT", "widget": {"name": "drop_size"}, "link": null}, {"localized_name": "refiner_ratio", "name": "refiner_ratio", "type": "FLOAT", "widget": {"name": "refiner_ratio"}, "link": null}, {"localized_name": "cycle", "name": "cycle", "type": "INT", "widget": {"name": "cycle"}, "link": null}, {"localized_name": "inpaint_model", "name": "inpaint_model", "shape": 7, "type": "BOOLEAN", "widget": {"name": "inpaint_model"}, "link": null}, {"localized_name": "noise_mask_feather", "name": "noise_mask_feather", "shape": 7, "type": "INT", "widget": {"name": "noise_mask_feather"}, "link": null}, {"localized_name": "tiled_encode", "name": "tiled_encode", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_encode"}, "link": null}, {"localized_name": "tiled_decode", "name": "tiled_decode", "shape": 7, "type": "BOOLEAN", "widget": {"name": "tiled_decode"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [364, 388]}, {"localized_name": "cropped_refined", "name": "cropped_refined", "shape": 6, "type": "IMAGE", "slot_index": 1, "links": [695]}, {"localized_name": "cropped_enhanced_alpha", "name": "cropped_enhanced_alpha", "shape": 6, "type": "IMAGE", "links": null}, {"localized_name": "mask", "name": "mask", "type": "MASK", "links": null}, {"localized_name": "detailer_pipe", "name": "detailer_pipe", "type": "DETAILER_PIPE", "links": null}, {"localized_name": "cnet_images", "name": "cnet_images", "shape": 6, "type": "IMAGE", "links": null}], "properties": {"cnr_id": "comfyui-impact-pack", "ver": "8.14.2", "Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [512, true, 1024, 12346, "fixed", 20, 1, "deis", "beta", 0.22, 5, true, true, 0.5, 20, 3, "center-1", 0, 0.93, 0, 0.7, "False", 10, 0.2, 1, false, 20, false, false]}, {"id": 82, "type": "UltimateSDUpscale", "pos": [1939.5982666015625, 216.44186401367188], "size": [380.4031982421875, 875.0523681640625], "flags": {}, "order": 35, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 339}, {"localized_name": "model", "name": "model", "type": "MODEL", "link": 150}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 381}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 382}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 153}, {"localized_name": "upscale_model", "name": "upscale_model", "type": "UPSCALE_MODEL", "link": 118}, {"localized_name": "upscale_by", "name": "upscale_by", "type": "FLOAT", "widget": {"name": "upscale_by"}, "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}, {"localized_name": "mode_type", "name": "mode_type", "type": "COMBO", "widget": {"name": "mode_type"}, "link": null}, {"localized_name": "tile_width", "name": "tile_width", "type": "INT", "widget": {"name": "tile_width"}, "link": null}, {"localized_name": "tile_height", "name": "tile_height", "type": "INT", "widget": {"name": "tile_height"}, "link": null}, {"localized_name": "mask_blur", "name": "mask_blur", "type": "INT", "widget": {"name": "mask_blur"}, "link": null}, {"localized_name": "tile_padding", "name": "tile_padding", "type": "INT", "widget": {"name": "tile_padding"}, "link": null}, {"localized_name": "seam_fix_mode", "name": "seam_fix_mode", "type": "COMBO", "widget": {"name": "seam_fix_mode"}, "link": null}, {"localized_name": "seam_fix_denoise", "name": "seam_fix_denoise", "type": "FLOAT", "widget": {"name": "seam_fix_denoise"}, "link": null}, {"localized_name": "seam_fix_width", "name": "seam_fix_width", "type": "INT", "widget": {"name": "seam_fix_width"}, "link": null}, {"localized_name": "seam_fix_mask_blur", "name": "seam_fix_mask_blur", "type": "INT", "widget": {"name": "seam_fix_mask_blur"}, "link": null}, {"localized_name": "seam_fix_padding", "name": "seam_fix_padding", "type": "INT", "widget": {"name": "seam_fix_padding"}, "link": null}, {"localized_name": "force_uniform_tiles", "name": "force_uniform_tiles", "type": "BOOLEAN", "widget": {"name": "force_uniform_tiles"}, "link": null}, {"localized_name": "tiled_decode", "name": "tiled_decode", "type": "BOOLEAN", "widget": {"name": "tiled_decode"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [365, 386]}], "properties": {"cnr_id": "comfyui_ultimatesdupscale", "ver": "1.1.3", "Node name for S&R": "UltimateSDUpscale"}, "widgets_values": [2, 384340151733828, "fixed", 22, 1, "deis", "beta", 0.2, "Linear", 1024, 1024, 8, 32, "None", 1, 64, 8, 16, true, false]}, {"id": 348, "type": "ControlNetLoader", "pos": [625.302001953125, 378.03265380859375], "size": [315, 58], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "ControlNet名称", "name": "control_net_name", "type": "COMBO", "widget": {"name": "control_net_name"}, "link": null}], "outputs": [{"localized_name": "ControlNet", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [698]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "ControlNetLoader"}, "widgets_values": ["flux\\flux_controlnet_union.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 87, "type": "SaveImage", "pos": [3224, 876], "size": [1441.9891357421875, 570.8907470703125], "flags": {}, "order": 43, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 695}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": 652}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.33", "Node name for S&R": "SaveImage"}, "widgets_values": ["faceRefine"]}, {"id": 317, "type": "ExpressionEditor", "pos": [6190, 100], "size": [260.81048583984375, 690], "flags": {}, "order": 64, "mode": 0, "inputs": [{"localized_name": "src_image", "name": "src_image", "shape": 7, "type": "IMAGE", "link": 621}, {"localized_name": "motion_link", "name": "motion_link", "shape": 7, "type": "EDITOR_LINK", "link": null}, {"localized_name": "sample_image", "name": "sample_image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "add_exp", "name": "add_exp", "shape": 7, "type": "EXP_DATA", "link": null}, {"localized_name": "rotate_pitch", "name": "rotate_pitch", "type": "FLOAT", "widget": {"name": "rotate_pitch"}, "link": null}, {"localized_name": "rotate_yaw", "name": "rotate_yaw", "type": "FLOAT", "widget": {"name": "rotate_yaw"}, "link": null}, {"localized_name": "rotate_roll", "name": "rotate_roll", "type": "FLOAT", "widget": {"name": "rotate_roll"}, "link": null}, {"localized_name": "blink", "name": "blink", "type": "FLOAT", "widget": {"name": "blink"}, "link": null}, {"localized_name": "eyebrow", "name": "eyebrow", "type": "FLOAT", "widget": {"name": "eyebrow"}, "link": null}, {"localized_name": "wink", "name": "wink", "type": "FLOAT", "widget": {"name": "wink"}, "link": null}, {"localized_name": "pupil_x", "name": "pupil_x", "type": "FLOAT", "widget": {"name": "pupil_x"}, "link": null}, {"localized_name": "pupil_y", "name": "pupil_y", "type": "FLOAT", "widget": {"name": "pupil_y"}, "link": null}, {"localized_name": "aaa", "name": "aaa", "type": "FLOAT", "widget": {"name": "aaa"}, "link": null}, {"localized_name": "eee", "name": "eee", "type": "FLOAT", "widget": {"name": "eee"}, "link": null}, {"localized_name": "woo", "name": "woo", "type": "FLOAT", "widget": {"name": "woo"}, "link": null}, {"localized_name": "smile", "name": "smile", "type": "FLOAT", "widget": {"name": "smile"}, "link": null}, {"localized_name": "src_ratio", "name": "src_ratio", "type": "FLOAT", "widget": {"name": "src_ratio"}, "link": null}, {"localized_name": "sample_ratio", "name": "sample_ratio", "type": "FLOAT", "widget": {"name": "sample_ratio"}, "link": null}, {"localized_name": "crop_factor", "name": "crop_factor", "type": "FLOAT", "widget": {"name": "crop_factor"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [684]}, {"localized_name": "motion_link", "name": "motion_link", "type": "EDITOR_LINK", "links": null}, {"localized_name": "save_exp", "name": "save_exp", "type": "EXP_DATA", "links": null}], "properties": {"cnr_id": "comfyui-advancedliveportrait", "ver": "1.0.0", "Node name for S&R": "ExpressionEditor"}, "widgets_values": [-8, -8, 4, 0, 0, 0, 0, 0, 0, 8.1, 0, 1, 1, 1, 2]}, {"id": 318, "type": "ExpressionEditor", "pos": [6730, 100], "size": [260.81048583984375, 690], "flags": {}, "order": 65, "mode": 0, "inputs": [{"localized_name": "src_image", "name": "src_image", "shape": 7, "type": "IMAGE", "link": 622}, {"localized_name": "motion_link", "name": "motion_link", "shape": 7, "type": "EDITOR_LINK", "link": null}, {"localized_name": "sample_image", "name": "sample_image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "add_exp", "name": "add_exp", "shape": 7, "type": "EXP_DATA", "link": null}, {"localized_name": "rotate_pitch", "name": "rotate_pitch", "type": "FLOAT", "widget": {"name": "rotate_pitch"}, "link": null}, {"localized_name": "rotate_yaw", "name": "rotate_yaw", "type": "FLOAT", "widget": {"name": "rotate_yaw"}, "link": null}, {"localized_name": "rotate_roll", "name": "rotate_roll", "type": "FLOAT", "widget": {"name": "rotate_roll"}, "link": null}, {"localized_name": "blink", "name": "blink", "type": "FLOAT", "widget": {"name": "blink"}, "link": null}, {"localized_name": "eyebrow", "name": "eyebrow", "type": "FLOAT", "widget": {"name": "eyebrow"}, "link": null}, {"localized_name": "wink", "name": "wink", "type": "FLOAT", "widget": {"name": "wink"}, "link": null}, {"localized_name": "pupil_x", "name": "pupil_x", "type": "FLOAT", "widget": {"name": "pupil_x"}, "link": null}, {"localized_name": "pupil_y", "name": "pupil_y", "type": "FLOAT", "widget": {"name": "pupil_y"}, "link": null}, {"localized_name": "aaa", "name": "aaa", "type": "FLOAT", "widget": {"name": "aaa"}, "link": null}, {"localized_name": "eee", "name": "eee", "type": "FLOAT", "widget": {"name": "eee"}, "link": null}, {"localized_name": "woo", "name": "woo", "type": "FLOAT", "widget": {"name": "woo"}, "link": null}, {"localized_name": "smile", "name": "smile", "type": "FLOAT", "widget": {"name": "smile"}, "link": null}, {"localized_name": "src_ratio", "name": "src_ratio", "type": "FLOAT", "widget": {"name": "src_ratio"}, "link": null}, {"localized_name": "sample_ratio", "name": "sample_ratio", "type": "FLOAT", "widget": {"name": "sample_ratio"}, "link": null}, {"localized_name": "crop_factor", "name": "crop_factor", "type": "FLOAT", "widget": {"name": "crop_factor"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [685]}, {"localized_name": "motion_link", "name": "motion_link", "type": "EDITOR_LINK", "links": null}, {"localized_name": "save_exp", "name": "save_exp", "type": "EXP_DATA", "links": null}], "properties": {"cnr_id": "comfyui-advancedliveportrait", "ver": "1.0.0", "Node name for S&R": "ExpressionEditor"}, "widgets_values": [14.600000000000001, 0, 0, 5, 15, 0, 0, 0, 0, 0, 0, 0, 1, 1, 2]}, {"id": 316, "type": "ExpressionEditor", "pos": [5730, 100], "size": [260.81048583984375, 690], "flags": {}, "order": 63, "mode": 0, "inputs": [{"localized_name": "src_image", "name": "src_image", "shape": 7, "type": "IMAGE", "link": 620}, {"localized_name": "motion_link", "name": "motion_link", "shape": 7, "type": "EDITOR_LINK", "link": null}, {"localized_name": "sample_image", "name": "sample_image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "add_exp", "name": "add_exp", "shape": 7, "type": "EXP_DATA", "link": null}, {"localized_name": "rotate_pitch", "name": "rotate_pitch", "type": "FLOAT", "widget": {"name": "rotate_pitch"}, "link": null}, {"localized_name": "rotate_yaw", "name": "rotate_yaw", "type": "FLOAT", "widget": {"name": "rotate_yaw"}, "link": null}, {"localized_name": "rotate_roll", "name": "rotate_roll", "type": "FLOAT", "widget": {"name": "rotate_roll"}, "link": null}, {"localized_name": "blink", "name": "blink", "type": "FLOAT", "widget": {"name": "blink"}, "link": null}, {"localized_name": "eyebrow", "name": "eyebrow", "type": "FLOAT", "widget": {"name": "eyebrow"}, "link": null}, {"localized_name": "wink", "name": "wink", "type": "FLOAT", "widget": {"name": "wink"}, "link": null}, {"localized_name": "pupil_x", "name": "pupil_x", "type": "FLOAT", "widget": {"name": "pupil_x"}, "link": null}, {"localized_name": "pupil_y", "name": "pupil_y", "type": "FLOAT", "widget": {"name": "pupil_y"}, "link": null}, {"localized_name": "aaa", "name": "aaa", "type": "FLOAT", "widget": {"name": "aaa"}, "link": null}, {"localized_name": "eee", "name": "eee", "type": "FLOAT", "widget": {"name": "eee"}, "link": null}, {"localized_name": "woo", "name": "woo", "type": "FLOAT", "widget": {"name": "woo"}, "link": null}, {"localized_name": "smile", "name": "smile", "type": "FLOAT", "widget": {"name": "smile"}, "link": null}, {"localized_name": "src_ratio", "name": "src_ratio", "type": "FLOAT", "widget": {"name": "src_ratio"}, "link": null}, {"localized_name": "sample_ratio", "name": "sample_ratio", "type": "FLOAT", "widget": {"name": "sample_ratio"}, "link": null}, {"localized_name": "crop_factor", "name": "crop_factor", "type": "FLOAT", "widget": {"name": "crop_factor"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [675]}, {"localized_name": "motion_link", "name": "motion_link", "type": "EDITOR_LINK", "links": null}, {"localized_name": "save_exp", "name": "save_exp", "type": "EXP_DATA", "links": null}], "properties": {"cnr_id": "comfyui-advancedliveportrait", "ver": "1.0.0", "Node name for S&R": "ExpressionEditor"}, "widgets_values": [0, 0, 0, 0, 0, 23.5, 0, 0, 0, 0, 0, 0, 1, 1, 2]}, {"id": 319, "type": "ExpressionEditor", "pos": [7310, 100], "size": [260.81048583984375, 690], "flags": {}, "order": 66, "mode": 0, "inputs": [{"localized_name": "src_image", "name": "src_image", "shape": 7, "type": "IMAGE", "link": 623}, {"localized_name": "motion_link", "name": "motion_link", "shape": 7, "type": "EDITOR_LINK", "link": null}, {"localized_name": "sample_image", "name": "sample_image", "shape": 7, "type": "IMAGE", "link": null}, {"localized_name": "add_exp", "name": "add_exp", "shape": 7, "type": "EXP_DATA", "link": null}, {"localized_name": "rotate_pitch", "name": "rotate_pitch", "type": "FLOAT", "widget": {"name": "rotate_pitch"}, "link": null}, {"localized_name": "rotate_yaw", "name": "rotate_yaw", "type": "FLOAT", "widget": {"name": "rotate_yaw"}, "link": null}, {"localized_name": "rotate_roll", "name": "rotate_roll", "type": "FLOAT", "widget": {"name": "rotate_roll"}, "link": null}, {"localized_name": "blink", "name": "blink", "type": "FLOAT", "widget": {"name": "blink"}, "link": null}, {"localized_name": "eyebrow", "name": "eyebrow", "type": "FLOAT", "widget": {"name": "eyebrow"}, "link": null}, {"localized_name": "wink", "name": "wink", "type": "FLOAT", "widget": {"name": "wink"}, "link": null}, {"localized_name": "pupil_x", "name": "pupil_x", "type": "FLOAT", "widget": {"name": "pupil_x"}, "link": null}, {"localized_name": "pupil_y", "name": "pupil_y", "type": "FLOAT", "widget": {"name": "pupil_y"}, "link": null}, {"localized_name": "aaa", "name": "aaa", "type": "FLOAT", "widget": {"name": "aaa"}, "link": null}, {"localized_name": "eee", "name": "eee", "type": "FLOAT", "widget": {"name": "eee"}, "link": null}, {"localized_name": "woo", "name": "woo", "type": "FLOAT", "widget": {"name": "woo"}, "link": null}, {"localized_name": "smile", "name": "smile", "type": "FLOAT", "widget": {"name": "smile"}, "link": null}, {"localized_name": "src_ratio", "name": "src_ratio", "type": "FLOAT", "widget": {"name": "src_ratio"}, "link": null}, {"localized_name": "sample_ratio", "name": "sample_ratio", "type": "FLOAT", "widget": {"name": "sample_ratio"}, "link": null}, {"localized_name": "crop_factor", "name": "crop_factor", "type": "FLOAT", "widget": {"name": "crop_factor"}, "link": null}], "outputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "slot_index": 0, "links": [686]}, {"localized_name": "motion_link", "name": "motion_link", "type": "EDITOR_LINK", "slot_index": 1, "links": null}, {"localized_name": "save_exp", "name": "save_exp", "type": "EXP_DATA", "links": null}], "properties": {"cnr_id": "comfyui-advancedliveportrait", "ver": "1.0.0", "Node name for S&R": "ExpressionEditor"}, "widgets_values": [0, -20, 0, 5, 15, 0, 0, 0, 120, 0, 15, 1.3, 1, 1, 2]}], "links": [[118, 83, 0, 82, 5, "UPSCALE_MODEL"], [149, 95, 0, 97, 0, "BASIC_PIPE"], [150, 97, 1, 82, 1, "MODEL"], [153, 97, 3, 82, 4, "VAE"], [280, 129, 0, 134, 0, "IMAGE"], [283, 134, 0, 138, 0, "IMAGE"], [284, 129, 0, 139, 0, "IMAGE"], [285, 139, 0, 140, 0, "IMAGE"], [287, 142, 0, 141, 0, "IMAGE"], [289, 134, 0, 145, 0, "IMAGE"], [290, 139, 0, 146, 0, "IMAGE"], [291, 142, 0, 147, 0, "IMAGE"], [292, 129, 0, 149, 0, "*"], [293, 149, 0, 142, 0, "IMAGE"], [294, 149, 0, 150, 0, "*"], [313, 160, 0, 157, 0, "CLIP"], [315, 169, 0, 163, 0, "MODEL"], [316, 169, 0, 164, 0, "MODEL"], [317, 166, 0, 164, 1, "CONDITIONING"], [318, 173, 0, 166, 0, "CONDITIONING"], [319, 170, 0, 167, 0, "INT"], [320, 171, 0, 167, 1, "INT"], [321, 161, 0, 169, 0, "MODEL"], [322, 170, 0, 169, 3, "INT"], [323, 171, 0, 169, 4, "INT"], [324, 157, 0, 173, 0, "CONDITIONING"], [325, 175, 0, 173, 1, "CONDITIONING"], [327, 159, 0, 173, 3, "VAE"], [329, 160, 0, 175, 0, "CLIP"], [330, 178, 1, 177, 0, "LATENT"], [331, 159, 0, 177, 1, "VAE"], [332, 165, 0, 178, 0, "NOISE"], [333, 164, 0, 178, 1, "GUIDER"], [334, 162, 0, 178, 2, "SAMPLER"], [335, 163, 0, 178, 3, "SIGMAS"], [338, 180, 0, 173, 4, "IMAGE"], [339, 177, 0, 82, 0, "IMAGE"], [340, 173, 0, 95, 3, "CONDITIONING"], [341, 173, 1, 95, 4, "CONDITIONING"], [342, 169, 0, 95, 0, "MODEL"], [343, 160, 0, 95, 1, "CLIP"], [344, 159, 0, 95, 2, "VAE"], [357, 185, 0, 183, 1, "DETAILER_PIPE"], [358, 169, 0, 185, 0, "MODEL"], [359, 159, 0, 185, 2, "VAE"], [362, 160, 0, 185, 1, "CLIP"], [364, 183, 0, 179, 0, "IMAGE"], [365, 82, 0, 183, 0, "IMAGE"], [374, 187, 0, 185, 8, "DETAILER_HOOK"], [380, 173, 1, 185, 4, "CONDITIONING"], [381, 166, 0, 82, 2, "CONDITIONING"], [382, 173, 1, 82, 3, "CONDITIONING"], [383, 166, 0, 185, 3, "CONDITIONING"], [386, 82, 0, 84, 0, "IMAGE"], [388, 183, 0, 129, 0, "IMAGE"], [393, 177, 0, 196, 0, "IMAGE"], [404, 167, 0, 178, 4, "LATENT"], [496, 259, 0, 258, 0, "IMAGE"], [498, 150, 0, 259, 0, "IMAGE"], [587, 150, 0, 298, 0, "IMAGE"], [588, 298, 0, 299, 0, "IMAGE"], [589, 298, 0, 300, 0, "IMAGE"], [590, 259, 0, 301, 0, "IMAGE"], [620, 259, 0, 316, 0, "IMAGE"], [621, 259, 0, 317, 0, "IMAGE"], [622, 259, 0, 318, 0, "IMAGE"], [623, 259, 0, 319, 0, "IMAGE"], [628, 314, 0, 324, 0, "*"], [629, 324, 0, 196, 1, "STRING"], [631, 324, 0, 325, 0, "*"], [632, 325, 0, 179, 1, "STRING"], [633, 325, 0, 326, 0, "*"], [634, 326, 0, 145, 1, "STRING"], [635, 326, 0, 327, 0, "*"], [636, 327, 0, 146, 1, "STRING"], [637, 327, 0, 328, 0, "*"], [638, 328, 0, 147, 1, "STRING"], [639, 328, 0, 329, 0, "*"], [640, 329, 0, 300, 1, "STRING"], [641, 329, 0, 330, 0, "*"], [642, 330, 0, 301, 1, "STRING"], [643, 330, 0, 313, 0, "*"], [652, 326, 0, 87, 1, "STRING"], [675, 316, 0, 337, 0, "IMAGE"], [684, 317, 0, 341, 0, "IMAGE"], [685, 318, 0, 342, 0, "IMAGE"], [686, 319, 0, 343, 0, "IMAGE"], [687, 313, 0, 337, 1, "STRING"], [688, 313, 0, 344, 0, "*"], [689, 344, 0, 341, 1, "STRING"], [691, 346, 0, 342, 1, "STRING"], [692, 344, 0, 346, 0, "*"], [693, 346, 0, 347, 0, "*"], [694, 347, 0, 343, 1, "STRING"], [695, 183, 1, 87, 0, "IMAGE"], [698, 348, 0, 173, 2, "CONTROL_NET"], [702, 354, 0, 314, 0, "*"], [705, 18, 0, 185, 5, "BBOX_DETECTOR"]], "groups": [{"id": 1, "title": "1 CHARACTER GENERATION", "bounding": [-192, -597, 1894, 1241], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "2 UPSCALE + FACE FIX", "bounding": [1782, -602, 1323, 1832], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"id": 3, "title": "3 SAVE POSES", "bounding": [3152, -603, 2443, 1388], "color": "#88A", "font_size": 24, "flags": {}}, {"id": 4, "title": "4 EMOTIONS", "bounding": [5691, -601, 2077, 1514], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8140274938686372, "offset": [-1862.0198055657143, -310.73743126910324]}, "frontendVersion": "1.18.9", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}